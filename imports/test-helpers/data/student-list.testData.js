import * as testData from "./studentGroupWithHistory";

export function getStudentListContext(targetData) {
  return {
    studentsWithIndividualRuleNotProcessed: [],
    students: [
      {
        _id: "testStudentId1",
        identity: {
          name: {
            firstName: "<PERSON>",
            lastName: "<PERSON>"
          },
          identification: {
            localId: "1111111"
          }
        }
      },
      {
        _id: "testStudentId2",
        identity: {
          name: {
            firstName: "Lie<PERSON>",
            lastName: "Griff<PERSON>"
          },
          identification: {
            localId: "2222222"
          }
        }
      },
      {
        _id: "testStudentId3",
        identity: {
          name: {
            firstName: "<PERSON>",
            lastName: "<PERSON><PERSON><PERSON>"
          },
          identification: {
            localId: "3333333"
          }
        }
      },
      {
        _id: "testStudentId4",
        identity: {
          name: {
            firstName: "<PERSON>",
            lastName: "<PERSON>"
          },
          identification: {
            localId: "4444444"
          }
        }
      },
      {
        _id: "testStudentId5",
        identity: {
          name: {
            firstName: "<PERSON>",
            lastName: "<PERSON>"
          },
          identification: {
            localId: "5555555"
          }
        }
      }
    ],
    studentGroup: testData[targetData]
  };
}

export function getStudentListProps(targetData = "", customStudentGroup = null) {
  // NOTE(fmazur) - targetData references testData defaultStudentGroup, studentGroupWithBenchmarkHistory
  const data = customStudentGroup || testData[targetData];
  return {
    lastBenchmarkHistory: data.history.find(item => item.type === "benchmark"),
    individualInterventionStudentIds: [],
    individualInterventionQueueStudentIds: ["testStudentId2"]
  };
}
