import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { withRouter } from "react-router";
import { Meteor } from "meteor/meteor";
import { UserContext } from "./UserContext";

const buildOrganizationContext = () => {
  return {
    orgId: null,
    org: null,
    setOrgId: () => {},
    setOrg: () => {},
    sitesInOrg: []
  };
};

export const OrganizationContext = createContext(buildOrganizationContext());

// TODO(fmazur) - figure out how to implement useTracker to await for db changes for Organization
export const OrganizationContextProvider = withRouter(({ children, match }) => {
  const { userOrgId, userId } = useContext(UserContext);
  const [org, setOrg] = useState(null);
  const [orgId, setOrgId] = useState(null);
  const [sitesInOrg, setSitesInOrg] = useState([]);

  useEffect(() => {
    if (!userId) {
      setOrg(null);
      setOrgId(null);
      setSitesInOrg([]);
    }
  }, [userId]);

  // TODO(fmazur) - investigate why this triggers eg on clicking on logo
  // TODO(fmazur) - should only trigger on deps
  useEffect(() => {
    if (!userId) {
      return;
    }

    const { orgid: routeOrgId } = match.params;
    if (!userOrgId && !routeOrgId) {
      return;
    }
    // TODO(fmazur) - what about data admin with coach role
    // TODO(fmazur) - do we need to only look at primaryRole?
    let resolvedOrgId = userOrgId;
    if (userOrgId === "allOrgs") {
      resolvedOrgId = routeOrgId;
    }
    if (resolvedOrgId === orgId) {
      return;
    }
    setOrgId(resolvedOrgId);
    if (!resolvedOrgId) {
      return;
    }

    Meteor.call("Organizations:getOrganizationById", resolvedOrgId, (err, result) => {
      if (!err && result) {
        setOrg({ ...result, orgId: result.orgid });
      } else {
        setOrg(null);
      }
    });
  }, [userId, userOrgId, match.params.orgid]);

  useEffect(() => {
    if (userId && orgId) {
      Meteor.call("Sites:getSitesInOrg", orgId, (err, resp) => {
        if (!err) {
          // TODO(fmazur) - filter available sites per user roles
          setSitesInOrg(resp);
        }
      });
    }
  }, [userId, orgId]);

  return (
    <OrganizationContext.Provider
      value={{
        org,
        orgId,
        setOrgId,
        setOrg,
        sitesInOrg
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
});

OrganizationContextProvider.propTypes = {
  children: PropTypes.node,
  match: PropTypes.object
};
