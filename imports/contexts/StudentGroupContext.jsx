import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useParams } from "react-router-dom";
import { isEqual } from "lodash";
import { SiteContext } from "./SiteContext";
import { UserContext } from "./UserContext";

export const StudentGroupContext = createContext({
  studentGroupId: null,
  studentGroup: null,
  studentGroups: [],
  studentsInStudentGroup: []
});

export const StudentGroupContextProvider = ({ children }) => {
  const { studentGroupId: routeStudentGroupId } = useParams();
  const { studentGroupsInSite } = useContext(SiteContext);
  const { userId } = useContext(UserContext);
  const [viewedStudentGroup, setViewedStudentGroup] = useState(null);
  const [studentsInStudentGroup, setStudentsInStudentGroup] = useState([]);

  useEffect(() => {
    const potentialStudentGroup = studentGroupsInSite.find(sg => sg._id === routeStudentGroupId);
    if (!isEqual(viewedStudentGroup, potentialStudentGroup) && routeStudentGroupId) {
      setViewedStudentGroup(potentialStudentGroup);
    }
  }, [routeStudentGroupId, studentGroupsInSite]);

  useEffect(() => {
    if (viewedStudentGroup?._id) {
      Meteor.call("Students:getStudents", [viewedStudentGroup._id], viewedStudentGroup.schoolYear, (err, resp) => {
        if (!err) {
          setStudentsInStudentGroup(resp);
        }
      });
    }
  }, [viewedStudentGroup]);

  useEffect(() => {
    if (!userId) {
      setViewedStudentGroup(null);
      setStudentsInStudentGroup([]);
    }
  }, [userId]);

  return (
    <StudentGroupContext.Provider
      value={{
        studentGroupId: viewedStudentGroup?._id || null,
        studentGroup: viewedStudentGroup || null,
        studentsInStudentGroup: studentsInStudentGroup || []
      }}
    >
      {children}
    </StudentGroupContext.Provider>
  );
};

StudentGroupContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
