import React, { createContext, useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { OrganizationContext } from "./OrganizationContext";
import { UserContext } from "./UserContext";

export const SiteContext = createContext({
  siteId: null,
  siteName: "",
  site: null,
  studentGroupsInSite: []
});

export const SiteContextProvider = ({ children, siteId }) => {
  const { currentSiteAccess, userId } = useContext(UserContext);
  const { sitesInOrg, orgId } = useContext(OrganizationContext);

  const [potentialSite, setPotentialSite] = useState(null);
  const [studentGroupsInSite, setStudentGroupsInSite] = useState([]);

  useEffect(() => {
    if (sitesInOrg.length) {
      // TODO(fmazur) - use current schoolYear siteId of a user if available otherwise use context siteId
      const site = sitesInOrg.find(s => s._id === (siteId || currentSiteAccess?.siteId));
      setPotentialSite(site);
    }
  }, [sitesInOrg, siteId]);

  useEffect(() => {
    // TODO(fmazur) - limit data maybe for usage for all roles
    if (orgId && siteId) {
      Meteor.call("StudentGroups:getGroups", orgId, siteId, (err, result) => {
        if (!err) {
          setStudentGroupsInSite(result);
        }
      });
    }
  }, [orgId, siteId]);

  useEffect(() => {
    if (!userId) {
      setPotentialSite(null);
      setStudentGroupsInSite([]);
    }
  }, [userId]);

  return (
    <SiteContext.Provider
      value={{
        siteId: userId ? siteId || potentialSite?._id || null : null,
        siteName: potentialSite?.name || "",
        site: potentialSite || null,
        studentGroupsInSite: studentGroupsInSite || []
      }}
    >
      {children}
    </SiteContext.Provider>
  );
};

SiteContextProvider.propTypes = {
  children: PropTypes.node,
  siteId: PropTypes.string
};
