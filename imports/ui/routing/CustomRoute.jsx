import React from "react";
import { Route, withRouter, Redirect } from "react-router-dom";
import PropTypes from "prop-types";
import _isEmpty from "lodash/isEmpty";
import Alert from "react-s-alert";

import { getMeteorUser, getMeteorUserSync, isOnExposedRoute } from "/imports/api/utilities/utilities";

import Loading from "../components/loading";
import { AppDataContext } from "./AppDataContext";
import { navbarItemsByRoleId } from "./navbarItems";
import { SiteContextProvider } from "../../contexts/SiteContext";
import { StudentGroupContextProvider } from "../../contexts/StudentGroupContext";
import { OrganizationContextProvider } from "../../contexts/OrganizationContext";
import { SchoolYearContextProvider } from "../../contexts/SchoolYearContext";

class CustomRoute extends React.PureComponent {
  static contextType = AppDataContext;

  constructor(props) {
    super(props);
    this.setCurrentContext();
    this.state = {
      loading: true
    };
  }

  setCurrentContext() {
    this.setRouteContext();
    this.props.setRouteName(this.props.routeName);
    this.props.setRouteNavbarItems(this.getNavItemsForCurrentUserRole());
    this.props.setRouterGroupName(this.props.routerGroupName);
  }

  getNavItemsForCurrentUserRole = () => {
    const { path } = this.props;
    if (isOnExposedRoute(path)) {
      return [];
    }
    const user = getMeteorUserSync();
    const userRole = user?.profile?.siteAccess?.filter(sa => sa.isActive).map(sa => sa.role)[0] || [];
    return this.props.navbarItems.length ? this.props.navbarItems : navbarItemsByRoleId[userRole];
  };

  runTasks() {
    this.runTasksInSequence(this.context.schoolYear);
    this.setState({ loading: false });
  }

  componentDidMount = async () => {
    Alert.closeAll();
    if (this.props.path !== "/logout" && (await getMeteorUser())) {
      Meteor.call("users:authorizationChecks", (err, data = {}) => {
        const { shouldChangePassword = false, isConfiguringMFA = false } = data;
        if (err) {
          Alert.error(err.reason || "There was a problem checking user authorization");
        } else if (this.props.path !== "/login" && isConfiguringMFA && localStorage.getItem("isMFARequired")) {
          this.props.history.push("/login");
        } else if (this.props.path !== "/profile" && shouldChangePassword) {
          localStorage.setItem("isPasswordChangeRequired", "true");
          this.props.history.push("/profile");
          this.runTasks();
        } else {
          this.runTasks();
        }
      });
    } else {
      this.runTasks();
    }
  };

  componentDidUpdate = prevProps => {
    if (
      this.hasRouteNameChanged(prevProps.routeName) ||
      this.hasPathChanged(prevProps.path) ||
      this.hasLocationChanged(prevProps.location.pathname)
    ) {
      this.setCurrentContext();
      this.runTasksInSequence(this.context.schoolYear);
    }
  };

  setRouteContext() {
    const { match: defaultMatch, computedMatch, location } = this.props;
    let match = defaultMatch;

    if (_isEmpty(match.params) && !_isEmpty(computedMatch.params)) {
      match = computedMatch;
    }
    return this.props.updateAppDataContext({ match, location });
  }

  runTasksInSequence = schoolYear => {
    const { history, location, computedMatch: match, path } = this.props;
    const { siteId } = match.params;
    const savedRedirect = localStorage.getItem("redirect");
    try {
      this.props.tasks.forEach(task => {
        return task({ ctx: { history, location, match, path }, schoolYear, siteId });
      });
      if (savedRedirect && location.pathname === savedRedirect) {
        localStorage.removeItem("redirect");
      }
    } catch (e) {
      history.push("/");
    }
  };

  hasLocationChanged(prevLocation) {
    return this.props.location.pathname !== prevLocation;
  }

  hasPathChanged(prevPath) {
    return this.props.path !== prevPath;
  }

  hasRouteNameChanged(prevRouteName) {
    return this.props.routeName && this.props.routeName !== prevRouteName;
  }

  render() {
    if (this.state.loading) {
      return <Loading message="custom route" />;
    }

    if (!window.location.href.includes("print") && window.document.getElementById("print-iframe")) {
      window.document.getElementById("print-iframe").remove();
      window.document.title = "SpringMath";
    }
    const { path, exact } = this.props;
    return (
      <Route
        path={path}
        exact={exact}
        render={props => {
          const siteId = props.match?.params?.siteId;
          if (this.props.render) {
            return (
              <OrganizationContextProvider>
                <SchoolYearContextProvider>
                  <SiteContextProvider siteId={siteId}>
                    <StudentGroupContextProvider>{this.props.render(props)}</StudentGroupContextProvider>
                  </SiteContextProvider>
                </SchoolYearContextProvider>
              </OrganizationContextProvider>
            );
          }
          return <Redirect to={{ ...this.props.location, pathname: "/" }} />;
        }}
      />
    );
  }
}

CustomRoute.propTypes = {
  tasks: PropTypes.array,
  navbarItems: PropTypes.array,
  history: PropTypes.object,
  location: PropTypes.object,
  match: PropTypes.object,
  computedMatch: PropTypes.object,
  path: PropTypes.string,
  exact: PropTypes.bool,
  render: PropTypes.func,
  updateAppDataContext: PropTypes.func,
  setRouteName: PropTypes.func,
  setRouteNavbarItems: PropTypes.func,
  setRouterGroupName: PropTypes.func,
  routeName: PropTypes.string,
  routerGroupName: PropTypes.string
};

CustomRoute.defaultProps = {
  tasks: [],
  updateAppDataContext: () => {}
};

const CustomRouteWithRouter = withRouter(CustomRoute);

const CustomRouteWithContext = props => (
  <AppDataContext.Consumer>
    {({ setRouteName, setRouteNavbarItems, setRouterGroupName }) => (
      <CustomRouteWithRouter
        {...props}
        setRouteName={setRouteName}
        setRouteNavbarItems={setRouteNavbarItems}
        setRouterGroupName={setRouterGroupName}
      />
    )}
  </AppDataContext.Consumer>
);

export default CustomRouteWithContext;
