import React, { useEffect, useState, useCallback } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { Tracker } from "meteor/tracker";
import {
  getCurrentSchoolYear,
  getLatestAvailableSchoolYear,
  getMeteorUserSync,
  getOrgIdSync
} from "/imports/api/utilities/utilities";
import { isEqual } from "lodash";
import Loading from "../components/loading";

const AppDataContext = React.createContext();
const { Provider } = AppDataContext;

const AppDataProvider = ({
  children,
  orgid,
  siteId,
  routeName: initialRouteName,
  navbarItems: initialNavbarItems,
  routerGroupName: initialRouterGroupName,
  user,
  env: initialEnv,
  schoolYear: initialSchoolYear
}) => {
  if (initialSchoolYear && typeof initialSchoolYear.then === "function") {
    throw new Error("AppDataProvider received a Promise as schoolYear — must be resolved before passing.");
  }

  const [env, setEnv] = useState(initialEnv);
  const [schoolYear, setSchoolYear] = useState(initialSchoolYear);
  const [routeName, setRouteName] = useState(initialRouteName);
  const [navbarItems, setNavbarItems] = useState(initialNavbarItems);
  const [routerGroupName, setRouterGroupName] = useState(initialRouterGroupName);
  const [idOfStudentGroupWithConfetti, setIdOfStudentGroupWithConfetti] = useState(null);
  const [classwideIntervention, setClasswideIntervention] = useState({
    selectedSkillIndex: null,
    selectedSkillAssessmentId: null
  });

  const getEnv = useCallback(() => {
    Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT", "CI"], (err, resp) => {
      if (!isEqual(env, resp)) {
        setEnv(resp);
      }
    });
  }, [env]);

  useEffect(() => {
    getEnv();
  }, [getEnv]);

  const updateAppDataContext = useCallback(
    async (newState = {}) => {
      const setters = {
        schoolYear: setSchoolYear,
        routeName: setRouteName,
        navbarItems: setNavbarItems,
        routerGroupName: setRouterGroupName,
        classwideIntervention: setClasswideIntervention,
        idOfStudentGroupWithConfetti: setIdOfStudentGroupWithConfetti,
        env: setEnv
      };

      if ("schoolYear" in newState) {
        const maybePromise = newState.schoolYear;
        const isPromise = maybePromise && typeof maybePromise.then === "function";
        if (!isPromise) {
          const currentSchoolYear = await getLatestAvailableSchoolYear(getMeteorUserSync(), getOrgIdSync(orgid, user));
          const normalizedYear = !maybePromise || maybePromise > currentSchoolYear ? currentSchoolYear : maybePromise;
          setters.schoolYear(normalizedYear);
        }
      }

      Object.entries(newState).forEach(([key, value]) => {
        if (key !== "schoolYear" && key in setters) {
          setters[key](value);
        }
      });
    },
    [orgid, user]
  );

  const setContext = useCallback(
    (newContext, cb = () => {}) => {
      updateAppDataContext(newContext);
      cb();
    },
    [updateAppDataContext]
  );

  const resolveAndSetSchoolYear = useCallback(
    async tempSchoolYear => {
      const resolved = tempSchoolYear ?? (await getCurrentSchoolYear(getMeteorUserSync(), getOrgIdSync(orgid, user)));
      updateAppDataContext({ schoolYear: resolved });
    },
    [orgid, user, updateAppDataContext]
  );

  const value = {
    orgid,
    siteId,
    schoolYear,
    routeName,
    navbarItems,
    routerGroupName,
    idOfStudentGroupWithConfetti,
    classwideIntervention,
    env,
    setClasswideInterventionDetails: setClasswideIntervention,
    setContext,
    setRouteName,
    setRouteNavbarItems: setNavbarItems,
    setRouterGroupName,
    setSchoolYear, // sync only
    resolveAndSetSchoolYear, // async
    updateAppDataContext
  };

  return <Provider value={value}>{children}</Provider>;
};

AppDataProvider.propTypes = {
  children: PropTypes.any,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  routeName: PropTypes.string,
  routerGroupName: PropTypes.string,
  navbarItems: PropTypes.array,
  user: PropTypes.object,
  env: PropTypes.object,
  schoolYear: PropTypes.number
};

const AppDataUserWrapper = props => {
  const [user, setUser] = useState(null);
  const [env, setEnv] = useState({});
  const [isLoading, setIsLoading] = useState(!!localStorage.getItem("Meteor.userId"));
  const [schoolYear, setSchoolYear] = useState();

  useEffect(() => {
    let isMounted = true;
    let currentUser = null;
    const userHandle = Tracker.autorun(() => {
      try {
        currentUser = Meteor.user();
        if (currentUser && isMounted) {
          setUser(currentUser);
          userHandle.stop();
        }
      } catch {
        currentUser = null;
      }
    });

    Meteor.call("getEnvironmentVariables", ["METEOR_ENVIRONMENT", "CI"], (err, resp) => {
      if (isMounted) {
        setEnv(resp || {});
      }
    });

    return () => {
      isMounted = false;
      if (userHandle) {
        userHandle.stop();
      }
    };
  }, []);

  useEffect(() => {
    let isMounted = true;
    const fetchSchoolYear = async () => {
      if (!user) return;
      try {
        const orgid = getOrgIdSync(props, user);
        const year = await getCurrentSchoolYear(user, orgid);
        if (isMounted) {
          setSchoolYear(year);
        }
      } catch (error) {
        console.error("Error fetching school year:", error);
      }
    };
    fetchSchoolYear();

    return () => {
      isMounted = false;
    };
  }, [user, props]);

  useEffect(() => {
    if (user && schoolYear) {
      setIsLoading(false);
    }
  }, [user, schoolYear]);

  if (isLoading || !schoolYear) {
    return <Loading />;
  }

  return <AppDataProvider {...props} user={user} env={env} schoolYear={schoolYear || props.schoolYear} />;
};

AppDataUserWrapper.propTypes = {
  orgid: PropTypes.string,
  schoolYear: PropTypes.number
};

export { AppDataUserWrapper, AppDataProvider, AppDataContext };
