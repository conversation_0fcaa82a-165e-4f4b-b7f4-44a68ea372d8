import React from "react";
import { Switch, Redirect } from "react-router-dom";
import PropTypes from "prop-types";
import queryString from "query-string";
import { Meteor } from "meteor/meteor";
import Layout from "../layouts/layout";
import CustomRoute from "./CustomRoute";
import { checkLoggedIn, setApplicationVersion } from "../../startup/client/routeUtils";
import LoadingScreen from "../components/loading-screen/loading-screen";
import { clearWaitingOn } from "/imports/api/loadingCounter/methods";
import GradesSideNavLayout from "../layouts/grades-side-nav-layout";
import SampleAssessments from "../pages/sample-assessments/sample-assessments";
import Logout from "../pages/authentication/logout";
import InactiveOrganization from "../pages/inactive-organization/inactive-organization";
import Unauthorized from "../pages/unauthorized-page/unauthorized";
import { AppDataContext } from "./AppDataContext";
import * as utils from "/imports/api/utilities/utilities";
import NoAccess from "../pages/no-access/no-access";
import UserProfile from "../components/user/user-profile";
import { getMeteorUser, getMeteorUserSync, getMeteorUserId } from "/imports/api/utilities/utilities";

export default class LoggedInRoutes extends React.Component {
  static contextType = AppDataContext;

  navbarItems = [];

  routerGroupName = "loggedIn";

  render() {
    return (
      <Switch>
        <CustomRoute
          path="/"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          updateAppDataContext={({ location }) => {
            const user = getMeteorUserSync();
            const queryParams = queryString.parse(location.search);
            const { siteId, schoolYear } = queryParams;
            const newContext = { siteId };
            if (schoolYear) {
              newContext.schoolYear = parseInt(schoolYear);
            } else if (user) {
              newContext.schoolYear = utils.getCurrentSchoolYear(user);
            }
            this.context.updateAppDataContext(newContext);
          }}
          render={({ history, location }) => {
            const { encodedUrl } = queryString.parse(location.search);
            const redirect = localStorage.getItem("redirect");
            if (getMeteorUserId()) {
              if (encodedUrl || redirect) {
                clearWaitingOn();
                return <Redirect to={decodeURIComponent(encodedUrl || redirect)} />;
              }
              Meteor.call("users:authorization:getLandingPageForUser", getMeteorUserId(), (err, resp) => {
                if (err) {
                  // sign out?
                  // TODO - figure out how to properly sign out user
                } else {
                  clearWaitingOn();
                  let route = resp;
                  if (resp?.route === "/profile" && resp?.isPasswordChangeRequired) {
                    localStorage.setItem("isPasswordChangeRequired", "true");
                    route = resp.route;
                  }
                  history.push(route);
                }
              });
              return <Layout content={<LoadingScreen />} />;
            }
            return <Redirect to="/login" />;
          }}
        />
        <CustomRoute
          path="/sampleAssessments/:grade"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <GradesSideNavLayout content={<SampleAssessments {...match.params} />} />;
          }}
        />
        <CustomRoute
          path="/logout"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => {
            return <Layout content={<Logout />} />;
          }}
        />
        <CustomRoute
          path="/organization-is-not-active"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            return <Layout content={<InactiveOrganization {...match.params} />} />;
          }}
        />
        <CustomRoute
          path="/unauthorized"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={({ match }) => {
            const user = getMeteorUser();
            return <Layout content={<Unauthorized {...match.params} user={user} />} />;
          }}
        />
        <CustomRoute
          path="/no-access"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => <Layout content={<NoAccess />} />}
        />
        <CustomRoute
          path="/profile"
          exact
          routerGroupName={this.routerGroupName}
          navbarItems={this.navbarItems}
          tasks={this.props.tasks}
          render={() => <Layout content={<UserProfile isModal={false} />} />}
        />
      </Switch>
    );
  }
}

LoggedInRoutes.defaultProps = {
  tasks: [checkLoggedIn, setApplicationVersion]
};

LoggedInRoutes.propTypes = {
  tasks: PropTypes.array
};
