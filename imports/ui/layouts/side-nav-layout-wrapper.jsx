import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import React, { useEffect, useContext } from "react";
import { useHistory } from "react-router-dom";
import PropTypes from "prop-types";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import Alert from "react-s-alert";
import SideNavLayout from "./side-nav-layout";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import Navigation from "./navigation/navigation";
import layoutMethods from "./methods";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";

const SideNavLayoutWrapper = ({ loading, navName, orgid, ...props }) => {
  if (loading) {
    return (
      <div className="wrapper">
        <Navigation navName={navName} orgid={orgid} />
        <footer>{layoutMethods.version()}</footer>
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
  return <SideNavLayout {...props} navName={navName} orgid={orgid} />;
};

const LayoutWithTracker = params => {
  const { schoolYear } = useContext(SchoolYearContext) || {};
  const { orgId: contextOrgId } = useContext(OrganizationContext) || {};

  const history = useHistory();

  const { siteId, studentGroupId, orgid: propsOrgId } = params;
  const orgid = contextOrgId || propsOrgId || "";

  const { studentGroup, loading, user } = useTracker(() => {
    if (!schoolYear || !orgid) {
      return { loading: true, studentGroup: null, user: null };
    }

    const userDataSub = Meteor.subscribe("userData");
    if (!userDataSub.ready()) {
      return { loading: true, studentGroup: null, user: null };
    }

    const currentUser = getMeteorUserSync();
    const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser:BasicData", schoolYear, siteId, orgid);
    const isLoading = !studentGroupsSub.ready();

    if (isLoading) {
      return { loading: true, studentGroup: null, user: currentUser };
    }

    const foundStudentGroup = StudentGroups.findOne(studentGroupId);

    return {
      loading: false,
      studentGroup: foundStudentGroup,
      user: currentUser
    };
  }, [schoolYear, siteId, orgid, studentGroupId]);

  useEffect(() => {
    if (!loading && (!studentGroup || !user)) {
      let userRole;
      const roles = [ROLE_IDS.admin, ROLE_IDS.support, ROLE_IDS.universalCoach];
      user?.profile?.siteAccess?.forEach(sa => {
        if (sa.schoolYear === schoolYear || (roles.includes(sa.role) && sa.isActive)) {
          userRole = sa.role;
        }
      });

      if (user && roles.includes(userRole)) {
        const targetRoute = orgid && siteId ? `/school-overview/${orgid}/all/${siteId}` : "/";
        history.push(targetRoute);
      } else {
        history.push(`/${user ? "unauthorized" : "login"}`);
      }
    }
  }, [loading, studentGroup, user, schoolYear, orgid, siteId, history]);

  // Use orgid from studentGroup if available, otherwise use context/props orgid
  const resolvedOrgId = studentGroup?.orgid || orgid;

  return (
    <SideNavLayoutWrapper
      {...params}
      studentGroup={studentGroup}
      schoolYear={schoolYear}
      siteId={siteId}
      loading={loading}
      orgid={resolvedOrgId}
      user={user}
    />
  );
};

export default LayoutWithTracker;

SideNavLayoutWrapper.propTypes = {
  loading: PropTypes.bool,
  studentGroupId: PropTypes.string,
  studentGroup: PropTypes.object,
  user: PropTypes.object,
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  navName: PropTypes.string
};
