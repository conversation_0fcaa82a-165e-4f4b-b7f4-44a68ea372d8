// eslint-disable-next-line max-classes-per-file
import React from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import { withRouter } from "react-router-dom";

import url from "url";
import selectComponentToPrint from "/imports/api/printing/selectComponentToPrint";
import Loading from "../components/loading";
import { ClassProvider } from "../pages/classContext";
import { AppDataContext } from "../routing/AppDataContext";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

class PrintLayout extends React.Component {
  static contextType = AppDataContext;

  componentDidMount() {
    this.printThisPage();
  }

  printThisPage = () => {
    const currentDate = new Date().toISOString().slice(0, 10);
    const pdfTitle = `${this.props.component}${
      this.props.params.siteId ? ` ${this.props.params.siteId}` : ""
    } ${currentDate}`;
    // Set title for iframe parent page
    window.parent.document.querySelector("title").innerHTML = pdfTitle;
    // Set title for iframe page
    window.document.querySelector("title").innerHTML = pdfTitle;
    const shouldPrintAllStudents = window.location.href.includes("printAllStudents");
    if (!shouldPrintAllStudents) {
      this.printInterval = setInterval(() => {
        if (document.getElementsByClassName("rect5").length === 0) {
          clearInterval(this.printInterval);
          setTimeout(() => {
            window.print();
            window.parent.postMessage(`printWindowClosed`, "*");
            // Restore tab name after printing page
            window.parent.document.title = "SpringMath";
            if (this.context.env.METEOR_ENVIRONMENT !== "TEST") {
              window.close();
            }
          }, 1000);
        }
      }, 2000);
    }
  };

  render() {
    if (this.props.user && !this.props.loading) {
      return (
        <div className="printContainer">
          <this.props.Component {...this.props.params} />
        </div>
      );
    }
    return <Loading />;
  }
}

PrintLayout.propTypes = {
  loading: PropTypes.bool,
  component: PropTypes.string,
  user: PropTypes.object,
  params: PropTypes.object,
  history: PropTypes.object,
  studentGroup: PropTypes.object,
  studentsInGroup: PropTypes.array,
  studentIdsInGroup: PropTypes.array
};

const PrintLayoutContainer = withRouter(
  withTracker(props => {
    const { component } = props;
    const user = getMeteorUserSync();
    const parsedQuery = url.parse(window.location.href, true).query;
    const params = {
      ...parsedQuery,
      schoolYear: parseInt(parsedQuery.schoolYear)
    };
    const Component = selectComponentToPrint(component);

    return { ...props, Component, params, user };
  })(PrintLayout)
);

export default class PrintLayoutWrapper extends React.Component {
  render = () => {
    return ["StudentDetail", "AllStudentDetail", "AllStudentsClasswideInterventionGraphs"].includes(
      this.props.component
    ) ? (
      <ClassProvider>
        <PrintLayoutContainer {...this.props} />
      </ClassProvider>
    ) : (
      <PrintLayoutContainer {...this.props} />
    );
  };
}

PrintLayoutWrapper.propTypes = {
  component: PropTypes.string
};
