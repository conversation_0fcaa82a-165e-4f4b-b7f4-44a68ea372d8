import React, { Component } from "react";
import PropTypes from "prop-types";

import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import Alert from "react-s-alert";
import * as messageNoticeUtils from "/imports/api/messageNotices/methods";
import { Loading } from "/imports/ui/components/loading";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { roles } from "/imports/api/roles/methods";
import sortBy from "lodash/sortBy";
import Navigation from "./navigation/navigation";
import SideNav from "../components/side-nav";
import DetailLayout from "./detail-layout";
import StudentGroupLayout from "./student-group-layout";
import DefaultLayout from "./default-layout";
import { areSubscriptionsLoading, isHighSchoolGrade, renderFooter } from "../utilities";
import { sortByGradeAndName } from "/imports/api/utilities/utilities";
import { Students } from "../../api/students/students";
import { sortStudentsByName } from "../components/student-groups/helperFunction";

class SideNavLayout extends Component {
  state = {
    expandedNoticeState: false
  };

  expandedStateCB = isExpanded => {
    this.setState({ expandedNoticeState: isExpanded });
  };

  render() {
    const studentGroup =
      this.props.studentGroups && this.props.studentGroups.find(sg => sg._id === this.props.studentGroupId);
    const { studentId } = this.props;
    if (this.props.loading) return <Loading />;
    return (
      <div className="wrapper nav-support">
        <Navigation navName={this.props.navName} />
        <main>
          <SideNav
            studentGroups={this.props.studentGroups}
            isAdmin={this.props.isAdmin}
            isSupport={this.props.isSupport}
            isUniversalCoach={this.props.isUniversalCoach}
            orgid={this.props.orgid}
            siteId={this.props.siteId}
          />
          {this.props.context === "student-detail" && (
            <DetailLayout
              studentId={this.props.studentId}
              students={this.props.students}
              studentGroup={this.props.studentGroup}
              siteId={this.props.siteId}
              studentGroupId={this.props.studentGroupId}
              content={this.props.content}
              context={this.props.context}
              expandedStateCB={this.expandedStateCB}
              messageNotice={this.props.messageNotice}
              expandedNoticeState={this.state.expandedNoticeState}
            />
          )}
          {!studentId && studentGroup && (
            <StudentGroupLayout
              groupName={studentGroup.name}
              siteId={this.props.siteId}
              studentGroupId={this.props.studentGroupId}
              studentGroup={this.props.studentGroup}
              content={this.props.content}
              expandedStateCB={this.expandedStateCB}
              expandedNoticeState={this.state.expandedNoticeState}
              messageNotice={this.props.messageNotice}
              isHighSchoolGroup={isHighSchoolGrade(studentGroup.grade)}
            />
          )}
          {!studentId && !studentGroup && <DefaultLayout content={this.props.content} />}
        </main>
        {renderFooter({ hasSideNav: true })}
        <Alert stack={{ limit: 3 }} effect="jelly" position="top-right" offset={30} />
      </div>
    );
  }
}

SideNavLayout.propTypes = {
  content: PropTypes.object,
  loading: PropTypes.bool,
  messageNotice: PropTypes.object,
  navName: PropTypes.string,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  studentGroups: PropTypes.array,
  students: PropTypes.array,
  studentId: PropTypes.string,
  context: PropTypes.string,
  user: PropTypes.object,
  isAdmin: PropTypes.bool,
  isSupport: PropTypes.bool,
  isUniversalCoach: PropTypes.bool,
  siteId: PropTypes.string,
  orgid: PropTypes.string
};

/** ****************************************************************
 // Data Container
 ***************************************************************** */
export default withTracker(params => {
  const { user, orgid, siteId, studentGroup, schoolYear } = params;
  // Subscriptions
  const orgSub = Meteor.subscribe("Organizations", orgid);
  const sitesHandle = Meteor.subscribe("Sites", orgid, siteId);
  const studentsHandle =
    params.context === "student-detail"
      ? Meteor.subscribe("StudentsInStudentGroup", studentGroup._id)
      : { ready: () => true };
  const loading = areSubscriptionsLoading(sitesHandle, orgSub, studentsHandle);
  let isAdmin = false;
  let isSupport = false;
  let isUniversalCoach = false;

  let students = [];
  if (!loading) {
    // Admin - only show the student group that they clicked on in navigation
    if (user?.profile?.siteAccess) {
      let result = false;
      const role = roles.find(r => r.name === "admin");
      if (role) {
        result = user.profile.siteAccess.filter(sa => sa.siteId === params.siteId).some(sa => sa.role === role._id);
      }
      isAdmin = result;

      const supportRole = roles.find(r => r.name === "support");
      if (supportRole) {
        isSupport = user.profile.siteAccess.some(sa => sa.role === supportRole._id);
      }

      const universalCoachRole = roles.find(r => r.name === "universalCoach");
      if (universalCoachRole) {
        isUniversalCoach = user.profile.siteAccess.some(sa => sa.role === universalCoachRole._id);
      }

      if (params.context === "student-detail") {
        const rosterSortingItem = localStorage.getItem("rosterSorting");
        const sorting = rosterSortingItem || "lastFirst";
        if (!rosterSortingItem) {
          localStorage.setItem("rosterSorting", "lastFirst");
        }
        const isIndividualSection = window.location.href.includes("/individual");
        const studentDocs = Students.find({}, { fields: { _id: 1, "identity.name": 1, currentSkill: 1 } }).fetch();
        if (isIndividualSection) {
          students = sortStudentsByName(studentDocs.filter(s => s.currentSkill?.assessmentId));
        } else {
          students = sortBy(studentDocs, student => {
            const { firstName, lastName } = student.identity.name;
            return sorting === "lastFirst" ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
          });
        }
      }
    }
  }
  const messageNotice = messageNoticeUtils.getMessageNoticeByLocation("side-nav-layout");
  const studentGroups = StudentGroups.find({ schoolYear, isActive: true, siteId: params.siteId })
    .fetch()
    .sort(sortByGradeAndName);
  return {
    studentGroupId: params.studentGroupId,
    isAdmin,
    isSupport,
    isUniversalCoach,
    studentGroups,
    user,
    loading,
    messageNotice,
    studentGroup,
    students,
    orgid
  };
})(SideNavLayout);
