import React, { Component } from "react";
import PropTypes from "prop-types";
import isEmpty from "lodash/isEmpty";
import { getGender } from "/imports/api/utilities/utilities";
import { ClassContext } from "../pages/classContext";
import Loading from "../components/loading";
import { useContextOrProps } from "../utilities";

export default class DetailHeaderText extends Component {
  static contextType = ClassContext;

  getInterventionStats = () => {
    const { groupStats, student } = this.context;
    let statsToUse = groupStats;
    if (student && groupStats.individualResults) {
      statsToUse = groupStats.individualResults.find(indRes => indRes.studentId === student._id) || groupStats;
    }
    let interventionConsistency = "N/A";
    if (statsToUse.interventionConsistency !== undefined) {
      // null is a factor here that's why comparing to undefined
      interventionConsistency =
        statsToUse.interventionConsistency !== null ? `${statsToUse.interventionConsistency}%` : "N/A";
    }
    const averageWeeksPerSkill = statsToUse.averageWeeksPerSkill || "N/A";
    return { interventionConsistency, averageWeeksPerSkill };
  };

  renderStats() {
    if (this.context.groupStats) {
      const { interventionConsistency, averageWeeksPerSkill } = this.getInterventionStats();
      return (
        <span>
          <span className="intervention-stat">
            {interventionConsistency}
            <label>Weeks with Scores</label>
          </span>
          <span className="intervention-stat">
            {averageWeeksPerSkill}
            <label>Avg Weeks per Skill</label>
          </span>
        </span>
      );
    }
    return null;
  }

  render() {
    const { context: pageContext } = this.props;
    const student = useContextOrProps({ componentInstance: this, property: "student", verificationGetPath: "_id" });
    const studentGroup = this.props.studentGroup || this.context.studentGroup;

    if (isEmpty(student) || isEmpty(studentGroup)) {
      return (
        <header id="detail-header">
          <Loading inline={true} />
        </header>
      );
    }
    let headingName = "";
    let grade;
    let localId;
    let gender;
    let studentGrade;
    if (pageContext === "student-detail") {
      ({ localId } = student.identity.identification);
      gender = getGender(student.demographic.gender);
      headingName = `${student.identity.name.lastName}, ${student.identity.name.firstName}`;
      ({ studentGrade } = student);
      ({ grade } = studentGroup);
    }
    return (
      <header id="detail-header">
        <h1 className="profile-name">
          {headingName}
          <span className="profile-name-info">
            {localId && (
              <span>
                Local ID: <span className="field-value">{localId}</span>
              </span>
            )}
            {gender && (
              <span>
                Gender: <span className="field-value">{gender}</span>
              </span>
            )}
            <span>
              Grade: <span className="field-value">{grade}</span>{" "}
            </span>
            {studentGrade && (
              <span>
                Student Grade: <span className="field-value">{studentGrade}</span>{" "}
              </span>
            )}
            {this.props.displayStats ? this.renderStats() : null}
          </span>
        </h1>
      </header>
    );
  }
}

DetailHeaderText.propTypes = {
  displayStats: PropTypes.bool,
  context: PropTypes.string,
  studentGroup: PropTypes.object,
  student: PropTypes.any
};

DetailHeaderText.defaultProps = {
  context: "student-detail",
  studentGroup: {}
};
