import { Component } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import * as messageNoticeUtils from "/imports/api/messageNotices/methods.js";

class MessageNotice extends Component {
  render() {
    if (
      this.props.messageNotice &&
      this.props.messageNotice.restOfComponent &&
      this.props.messageNotice.reactTypeOfKeyValue
    ) {
      // mini mongo doesn't like storing react components inside of itself, so this is a workaround.
      return {
        $$typeof: this.props.messageNotice.reactTypeOfKeyValue,
        ...this.props.messageNotice.restOfComponent
      };
    }

    return null;
  }
}

MessageNotice.propTypes = {
  messageNotice: PropTypes.object,
  noticeLocation: PropTypes.string.isRequired
};

export default withTracker(props => {
  const messageNotice = messageNoticeUtils.getMessageNoticeByLocation(props.noticeLocation);
  if (messageNotice && messageNotice.restOfComponent && messageNotice.reactTypeOfKeyValue) {
    messageNotice.restOfComponent.props.expandedStateCB = props.expandedStateCB;
    messageNotice.restOfComponent.props.expandedNoticeState = !!props.expandedNoticeState; //in case of undefined
  }

  return {
    messageNotice
  };
})(MessageNotice);
