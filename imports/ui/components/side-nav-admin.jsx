import { Meteor } from "meteor/meteor";
import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useTracker } from "meteor/react-meteor-data";
import { ListGroup } from "react-bootstrap";

import { Grades } from "/imports/api/grades/grades";
import { Sites } from "/imports/api/sites/sites";
import * as utils from "/imports/api/utilities/utilities";
import { getCurrentEnrolledGrade } from "/imports/api/students/utils";
import { Loading } from "./loading.jsx";
import { Organizations } from "/imports/api/organizations/organizations";
import { areSubscriptionsLoading } from "../utilities";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";
import { getUserRoles } from "../pages/data-admin/utilities";

const AdminSideNav = ({ loading, siteName, siteId, orgid, grades, organizationName, match }) => {
  const [shouldDisplayZendeskWidget, setShouldDisplayZendeskWidget] = useState(false);
  const [isFetchingZendeskData, setIsFetchingZendeskData] = useState(true);

  // Handle Zendesk widget flag fetching
  useEffect(() => {
    const userRole = getUserRoles();
    const isAllowedToFetchZendeskFlag = userRole.includes("teacher") || userRole.includes("admin");
    if (!isAllowedToFetchZendeskFlag) {
      return;
    }
    Meteor.call("Settings:getZendeskWidgetFlag", siteId, (err, resp) => {
      if (!err) {
        setShouldDisplayZendeskWidget(resp);
      }
    });
  }, [siteId]);

  // Handle fetching data completion
  useEffect(() => {
    if (siteName && organizationName) {
      setIsFetchingZendeskData(false);
    }
  }, [siteName, organizationName]);

  const renderZendeskWidget = () => {
    if (!isFetchingZendeskData && shouldDisplayZendeskWidget) {
      return utils.renderZendeskWidget(organizationName, siteName);
    }
    return null;
  };

  const isActive = gradeId => (match.params.gradeId === gradeId ? " active" : "");

  if (loading) {
    return <Loading />;
  }

  return (
    <aside className="side-nav">
      <div className="site-selector" data-testid="siteSelectorId">
        {siteName}
      </div>
      <ListGroup className="student-grade-list student-group-list">
        <Link key="all" to={`/school-overview/${orgid}/all/${siteId}`} className={`${isActive("all")} list-group-item`}>
          School Overview
        </Link>
        {grades.map(({ _id, display }) => (
          <Link
            key={_id}
            to={`/school-overview/${orgid}/${_id}/${siteId}`}
            className={`${isActive(_id)} list-group-item`}
          >
            {getCurrentEnrolledGrade(display)}
          </Link>
        ))}
      </ListGroup>
      {renderZendeskWidget()}
    </aside>
  );
};

AdminSideNav.propTypes = {
  grades: PropTypes.array,
  loading: PropTypes.bool,
  organizationName: PropTypes.string,
  siteName: PropTypes.node,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  match: PropTypes.object
};

const AdminSideNavWithTracker = ({ orgid, siteId, history, schoolYear, ...props }) => {
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(schoolYear);

  // Handle async school year resolution
  useEffect(() => {
    const resolveSchoolYear = async () => {
      if (!schoolYear) {
        const user = getMeteorUserSync();
        if (user && orgid) {
          const currentSchoolYear = await utils.getCurrentSchoolYear(user, orgid);
          setResolvedSchoolYear(currentSchoolYear);
        }
      } else {
        setResolvedSchoolYear(schoolYear);
      }
    };

    resolveSchoolYear();
  }, [orgid, schoolYear]);

  const trackerData = useTracker(() => {
    if (!resolvedSchoolYear) {
      return { loading: true };
    }

    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, resolvedSchoolYear);
    const sitesSub = Meteor.subscribe("Sites", orgid, siteId);
    const orgSub = Meteor.subscribe("Organizations", orgid);
    const userDataSub = Meteor.subscribe("userData");

    const loading = areSubscriptionsLoading(gradesSub, sitesSub, orgSub, userDataSub);

    let grades = [];
    let siteName = "";
    let organizationName = "";

    if (!loading) {
      if (!Sites.findOne()) {
        const user = Meteor.user();
        history.push(user ? "/unauthorized" : "/login");
      }
      grades = Grades.find({}, { sort: { sortorder: 1 } }).fetch();
      const site = Sites.findOne({ _id: siteId });
      const organization = Organizations.findOne(orgid, { fields: { name: 1 } });
      organizationName = organization?.name || "";
      siteName = site?.name || "Loading...";
    }

    return {
      loading,
      grades,
      organizationName,
      siteName,
      orgid,
      siteId
    };
  }, [resolvedSchoolYear, siteId, orgid, history]);

  return <AdminSideNav {...props} {...trackerData} />;
};

AdminSideNavWithTracker.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  history: PropTypes.object,
  schoolYear: PropTypes.number
};

export default withRouter(AdminSideNavWithTracker);
