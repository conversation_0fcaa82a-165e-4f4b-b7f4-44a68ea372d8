import React, { Component } from "react";
import PropTypes from "prop-types";

import { withTracker } from "meteor/react-meteor-data";
import IndividualRecommendation from "./individual-recommendation";
import { isSML } from "/imports/api/utilities/utilities";

function classDoesNotMeetTarget(medianScore) {
  return medianScore < 50 && <i className="fa fa-exclamation" />;
}

class IndividualProblem extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedAssessment: 0,
      checkedStudents: []
    };
  }

  handleClick(assessmentIndex) {
    this.setState({ selectedAssessment: assessmentIndex });
  }

  percentOfStudentsMeetingTarget() {
    return this.props.assessmentResults.classwideResults.percentMeetingTarget;
  }

  renderMeasureResults() {
    return this.props.assessmentResults.measures.map((measureResults, index) => (
      <li
        key={index}
        className={this.state.selectedAssessment === index ? "active" : ""}
        data-toggle="tooltip"
        data-placement="top"
        title={measureResults.measureName}
        onClick={this.handleClick.bind(this, index)}
      >
        <h2 className="w7 text-center">
          {measureResults.percentMeetingTarget}%{classDoesNotMeetTarget(measureResults.percentMeetingTarget)}
        </h2>
        <h4 className="data-sub">Measure {index + 1}</h4>
      </li>
    ));
  }

  getClassroomPerformanceSummary = () => {
    if (this.props.onlyOneStudentTookAndFailedBenchmark) {
      return (
        <React.Fragment>
          <p>
            Because there is only 1 student in your class and they would benefit from intervention we recommend doing an
            <span className="cw-underline"> individual intervention.</span>
          </p>
        </React.Fragment>
      );
    }
    return this.props.classwideEnabled ? (
      <p>
        <span className="text-success">
          {`${this.props.assessmentResults.classwideResults.percentMeetingTarget}% of students are meeting all requirements.`}
        </span>
      </p>
    ) : (
      <p>
        <em>{this.percentOfStudentsMeetingTarget()}% of your class is meeting the target.</em>
        &nbsp; See below for a list of students in need of intervention to benefit from grade-level instruction.
      </p>
    );
  };

  render() {
    return (
      <div>
        <div className="screening-results item">
          <div className="classwide-result">
            <h3 className="w7">Classroom Performance</h3>
            {this.getClassroomPerformanceSummary()}
            {isSML(this.props.students[0].orgid) || this.props.isPrinting ? null : (
              <div className="big-stats">{this.renderMeasureResults()}</div>
            )}
          </div>
        </div>
        <IndividualRecommendation
          selectedAssessment={this.state.selectedAssessment}
          assessmentResults={this.props.assessmentResults}
          students={this.props.students}
          currentBenchmarkPeriodId={this.props.currentBenchmarkPeriod._id}
          classwideEnabled={this.props.classwideEnabled}
          scheduledStudentIds={this.props.scheduledStudentIds}
          studentGroup={this.props.studentGroup}
          isInCurrentSchoolYear={this.props.isInCurrentSchoolYear}
          user={this.props.user}
          onlyOneStudentTookAndFailedBenchmark={this.props.onlyOneStudentTookAndFailedBenchmark}
          firstClasswideInterventionCreatedAt={this.props.firstClasswideInterventionCreatedAt}
          isClasswideInterventionComplete={this.props.isClasswideInterventionComplete}
          isPrinting={this.props.isPrinting}
        />
      </div>
    );
  }
}

IndividualProblem.propTypes = {
  assessmentResults: PropTypes.object,
  classwideEnabled: PropTypes.bool,
  currentBenchmarkPeriod: PropTypes.object,
  scheduledStudentIds: PropTypes.array,
  students: PropTypes.array,
  studentGroup: PropTypes.object,
  isInCurrentSchoolYear: PropTypes.bool,
  onlyOneStudentTookAndFailedBenchmark: PropTypes.bool,
  user: PropTypes.object,
  isPrinting: PropTypes.bool,
  firstClasswideInterventionCreatedAt: PropTypes.number,
  isClasswideInterventionComplete: PropTypes.bool
};

// Data Container
const IndividualProblemContainer = withTracker(
  ({ assessmentResults, scheduledStudentIds, currentBenchmarkPeriod, classwideEnabled }) => ({
    assessmentResults,
    scheduledStudentIds,
    currentBenchmarkPeriod,
    classwideEnabled
  })
)(IndividualProblem);

export default IndividualProblemContainer;
