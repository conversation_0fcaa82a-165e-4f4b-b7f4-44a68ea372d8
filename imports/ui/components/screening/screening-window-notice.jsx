import React, { Component } from "react";
import PropTypes from "prop-types";

export default class ScreeningWindowNotice extends Component {
  constructor(props) {
    super(props);
    this.showScreeningProgress = this.showScreeningProgress.bind(this);
    this.state = {
      showScreeningProgress: props.expandedNoticeState
    };
  }

  showScreeningProgress() {
    if (this.props.expandedStateCB) {
      this.props.expandedStateCB(!this.state.showScreeningProgress);
    }
    this.setState({ showScreeningProgress: !this.state.showScreeningProgress });
  }

  render() {
    return (
      <div
        className={`conScreeningNotice ${this.props.showThreeDayWarning ? "screeningWindowStarted" : ""}
            schoolwideLevelProgress ${this.state.showScreeningProgress ? "opened" : "closed"}`}
      >
        <div className="conScreeningNotice-Heading clearfix">
          <button
            className={`btnNoticeAction btn
                  ${this.props.showThreeDayWarning ? "btnStartScreening" : "btnViewProgress btn-success"} btn-xs`}
            onClick={this.showScreeningProgress}
          >
            {this.state.showScreeningProgress ? "Hide" : "Learn More"}
          </button>

          <div className="iconCallout">
            <i className="fa fa-bullhorn" />
          </div>

          <h2>
            {this.props.screeningSeason} screening starts in {this.props.daysBeforeScreeingWindow} days!
          </h2>
        </div>
      </div>
    );
  }
}

ScreeningWindowNotice.propTypes = {
  expandedNoticeState: PropTypes.bool,
  showThreeDayWarning: PropTypes.bool,
  daysBeforeScreeingWindow: PropTypes.number,
  screeningSeason: PropTypes.string,
  expandedStateCB: PropTypes.func
};
