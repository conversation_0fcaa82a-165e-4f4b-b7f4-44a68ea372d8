import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import ScoreEntryRowCell from "./score-entry-row-cell";
import { ninjalog } from "/imports/api/utilities/utilities";

const valueNA = "N/A";

export class ScoreEntryRow extends Component {
  state = {
    siteId: this.props.match.params.siteId,
    isAbsent: false
  };

  getBulletClassNames = () => {
    const statuses = this.props.currentStudentScreeningStatus;
    let bulletIcon = "fa-circle-o";
    let bulletColor = "default";
    if (statuses.someScoresEntered && !statuses.allScoresCancelled) {
      bulletIcon = statuses.allScoresEntered ? "fa-check-circle" : bulletIcon;
      bulletColor = "success";
    } else if (statuses.allScoresCancelled) {
      bulletIcon = "fa-times-circle";
      bulletColor = "danger";
    }
    return `fa ${bulletIcon} text-${bulletColor}`;
  };

  highlightRow(isVisible, e) {
    if (
      e.target.getAttribute("data-purpose") !== "allScoresUnavailable" &&
      e.target.getAttribute("data-purpose") !== "allScoresAvailable"
    ) {
      const studentItems = Array.from(document.getElementsByClassName("student-item"));
      studentItems.forEach(si => {
        const studentItem = si;
        studentItem.className = "student-item";
      });
      this[e.target.getAttribute("data-student-id")].setAttribute("class", `student-item ${isVisible ? "active" : ""}`);
    }
  }

  updateScoreData(e) {
    e.preventDefault();
    const self = this;
    const purpose = e.currentTarget.getAttribute("data-purpose");
    let scoresPkg = [];

    self.highlightRow(false, e);
    if (purpose === "allScoresUnavailable") {
      this.setState({ isAbsent: true });
      scoresPkg = self.props.assessmentScores.map(score => ({
        assessmentResultId: this.props.assessmentResultId,
        scoreId: score._id,
        number_correct: valueNA,
        status: "CANCELLED"
      }));
    } else if (purpose === "allScoresAvailable") {
      this.setState({ isAbsent: false });
      scoresPkg = self.props.assessmentScores.map(score => ({
        assessmentResultId: this.props.assessmentResultId,
        scoreId: score._id,
        number_correct: "",
        status: "STARTED"
      }));
    }
    Meteor.call("AssessmentResults:updateScores", scoresPkg, this.state.siteId, err => {
      if (err) {
        ninjalog.error({
          msg: "Error in AssessmentResults:updateScores",
          val: err,
          context: "score-entry"
        });
      }
    });
  }

  renderButton(hide, textTop, textBottom, purpose) {
    return (
      <li>
        <a
          id="btnUnavailable"
          className={`btn btn-link not-screened ${hide ? "hide" : ""}`}
          data-purpose={purpose}
          onClick={this.props.inActiveSchoolYear ? this.updateScoreData.bind(this) : null}
          disabled={!this.props.inActiveSchoolYear}
        >
          {textTop}
          <br />
          {textBottom}
        </a>
      </li>
    );
  }

  render() {
    // Provide assessment labels only when administering individual level progressmonitoring
    let inLinePMAssessmentLabel = null;

    if (
      this.props.progressMonitoringInfo &&
      this.props.progressMonitoringInfo.type === "INDIVIDUAL_PROGRESS_MONITORING"
    ) {
      inLinePMAssessmentLabel = <td className="student-name">{this.props.interventionName} </td>;
    }
    const bulletClassNames = this.getBulletClassNames();

    const statuses = this.props.currentStudentScreeningStatus;
    const studentFirstName = this.props.student.identity.name.firstName;
    const studentLastName = this.props.student.identity.name.lastName;
    return (
      <tr
        ref={r => {
          this[this.props.student._id] = r;
        }}
        data-testid="student-test-name"
        className="student-item"
      >
        <td className="student-name">
          <i className={bulletClassNames} />
          &nbsp;
          {this.props.sortBy === "lastFirst"
            ? `${studentLastName}, ${studentFirstName}`
            : `${studentFirstName} ${studentLastName}`}
        </td>
        {inLinePMAssessmentLabel}
        <td className="screening-status">
          <form className="" id="screening-score-js" autoComplete="off">
            <ul className="screening-cols">
              {this.props.assessmentScores.length < 1
                ? null
                : this.props.assessmentScores.map((assessmentScore, index) => (
                    <li key={`${assessmentScore.studentId}_${index}`}>
                      <ul className="lstScoreInputs">
                        <ScoreEntryRowCell
                          cellId={`score_input_${this.props.studentIndex * this.props.assessmentScores.length + index}`}
                          assessmentScore={assessmentScore}
                          isAbsent={this.state.isAbsent}
                          assessmentResultId={this.props.assessmentResultId}
                          assessmentScoreLimit={this.props.assessmentScoreLimits.find(
                            l => l.assessmentId === assessmentScore.assessmentId
                          )}
                          index={index}
                          inActiveSchoolYear={this.props.inActiveSchoolYear}
                          isReadOnly={this.props.isReadOnly}
                          onFocus={this.highlightRow.bind(this, true)}
                          rowScoreCount={this.props.assessmentScores.length}
                        />
                      </ul>
                    </li>
                  ))}
              {statuses.allScoresCancelled
                ? this.renderButton(false, "Enter", "Scores", "allScoresAvailable")
                : this.renderButton(
                    this.props.isSML || statuses.allScoresEntered,
                    "Mark Student",
                    "Absent",
                    "allScoresUnavailable"
                  )}
            </ul>
          </form>
        </td>
      </tr>
    );
  }
}

ScoreEntryRow.propTypes = {
  assessmentResultId: PropTypes.string,
  assessmentScoreLimits: PropTypes.array,
  assessmentScores: PropTypes.array.isRequired,
  currentStudentScreeningStatus: PropTypes.object.isRequired,
  inActiveSchoolYear: PropTypes.bool,
  interventionName: PropTypes.string,
  progressMonitoringInfo: PropTypes.object,
  student: PropTypes.object.isRequired,
  studentIndex: PropTypes.number,
  isReadOnly: PropTypes.bool,
  match: PropTypes.object,
  isSML: PropTypes.bool,
  sortBy: PropTypes.string
};

/** ************************************************
 * Screening Score Entry Data Container
 ************************************************* */
export default withTracker(props => props)(withRouter(ScoreEntryRow));

/** ************************************************
 * Progress Monitoring Score Entry Data Container
 ************************************************* */
export const PMScoreEntry = withTracker(({ student, assessmentScores, assessmentResultId }) => {
  // Subscriptions were performed on the parent (dashboard) component

  const screeningStatus = {
    allScoresCancelled: false,
    allScoresEntered: false,
    studentId: student._id
  };

  // Weekly progress monitoring will only be
  // performed once per week so there should only be one score.
  if (assessmentScores && assessmentScores[0] && assessmentScores[0].status === "CANCELLED") {
    screeningStatus.allScoresCancelled = true;
  } else if (assessmentScores && assessmentScores[0] && assessmentScores[0].status === "COMPLETE") {
    screeningStatus.someScoresEntered = true;
    screeningStatus.allScoresEntered = true;
  }
  return {
    student,
    assessmentScores: assessmentScores.filter(a => a),
    currentStudentScreeningStatus: screeningStatus,
    assessmentResultId
  };
})(withRouter(ScoreEntryRow));
