import React, { Component } from "react";
import PropTypes from "prop-types";
import { debounce } from "lodash";
import { ninjalog } from "/imports/api/utilities/utilities";
import { ScoreEntryContext } from "./score-entry-context";

class ScoreEntryRowCell extends Component {
  static contextType = ScoreEntryContext;

  state = {
    value: this.props.assessmentScore.value ?? ""
  };

  componentWillUnmount() {
    this.debouncedValidateAndUpdate.flush();
  }

  componentDidUpdate(prevProps, prevState) {
    if (!prevProps.isAbsent && this.props.isAbsent) {
      this.setState({ value: "N/A" });
    } else if (prevProps.isAbsent && !this.props.isAbsent) {
      this.setState({ value: "" });
    }
    if (prevState.value !== this.state.value) {
      const elem = document.getElementById(this.props.cellId);
      this.resetErrors(elem);
      this.debouncedValidateAndUpdate(elem, this.state.value);
    }
  }

  resetErrors = elemTarget => {
    /* eslint-disable no-param-reassign */
    clearTimeout(this.scoreHelperTimeoutHandle);
    elemTarget.nextElementSibling.textContent = "";
    elemTarget.nextElementSibling.className = "help-block d-none";
    const { cellId, assessmentResultId } = this.props;
    this.context.removeUnusualHighScoreField({ cellId, assessmentResultId });
  };

  handleNavigationKeys = e => {
    const inputVal = document.activeElement.value;
    const currentPosition = document.activeElement.selectionStart;
    const currentIndex = document.activeElement.id.slice(12);
    let newIndex = currentIndex;
    switch (e.which) {
      case 9: // Tab
        break;
      case 37: // Left Arrow
        if (currentPosition === 0) {
          newIndex = parseInt(currentIndex) - 1;
        }
        break;
      case 38: // Up Arrow
        newIndex = parseInt(currentIndex) - this.props.rowScoreCount;
        break;
      case 39: // Right Arrow
        if ((inputVal && currentPosition === inputVal.length) || !inputVal) {
          newIndex = parseInt(currentIndex) + 1;
        }
        break;
      case 40: // Down Arrow
      case 13: // Enter
        e.preventDefault();
        newIndex = parseInt(currentIndex) + this.props.rowScoreCount;
        break;
      default:
      // Do Nothing
    }
    const newElement = document.getElementById(`score_input_${newIndex}`);
    if (newElement) {
      newElement.focus();
    }
  };

  scoreHelperTimeoutHandle = null;

  onChange = e => {
    const elemTarget = e.target;
    const newValue = elemTarget.value || "";
    this.setState({ value: newValue });
  };

  validateAndUpdate = (elemTarget, newValue) => {
    if (this.validateField(elemTarget, newValue)) {
      this.updateCellData(elemTarget, newValue);
    }
  };

  debouncedValidateAndUpdate = debounce(this.validateAndUpdate, 300, {
    leading: false,
    trailing: true
  });

  validateField = (elemTarget, newValue) => {
    if (this.props.isAbsent) {
      return false;
    }
    /* eslint-disable no-param-reassign */
    const { cellId, assessmentResultId } = this.props;
    if (newValue.match(/[^0-9]/g) || (newValue[0] === "0" && newValue.length > 1)) {
      // Set helper label / class if invalid input
      if (newValue[0] === "0" && newValue.length > 1) {
        elemTarget.nextElementSibling.textContent = "No Leading Zeroes";
      } else if (newValue[0] === "-") {
        elemTarget.nextElementSibling.textContent = "Must Be Positive";
      } else {
        elemTarget.nextElementSibling.textContent = "Integers Only";
      }
      // alert
      elemTarget.nextElementSibling.className = "help-block text-danger animated bounceIn";
      // focus
      elemTarget.focus();
      return false;
    }

    const limit = this.props.assessmentScoreLimit;
    if (limit && +newValue > +limit.limit) {
      elemTarget.nextElementSibling.textContent = "Unusual High Score";
      elemTarget.nextElementSibling.className = "help-block text-warning animated bounceIn";
      this.context.setUnusualHighScoreField({ cellId, ref: this[this.props.assessmentScore._id], assessmentResultId });
      return false;
    }
    return true;
  };

  updateCellData = (elemTarget, newValue) => {
    const scoresPkg = [];

    scoresPkg.push({
      assessmentResultId: this.props.assessmentResultId,
      scoreId: elemTarget.getAttribute("data-assessment-score-id"),
      number_correct: newValue,
      status: newValue !== "" ? "COMPLETE" : "STARTED"
    });

    const { siteId, assessmentScore } = this.props;
    Meteor.call("AssessmentResults:updateScores", scoresPkg, siteId || assessmentScore.siteId, err => {
      if (err) {
        ninjalog.error({
          msg: "Error in AssessmentResults:updateScores",
          val: err,
          context: "score-entry"
        });
      } else if (["", "Saved"].includes(elemTarget.nextElementSibling.textContent) || newValue === elemTarget.value) {
        elemTarget.nextElementSibling.textContent = "Saved";
        elemTarget.nextElementSibling.className = "help-block text-success animated bounceIn";
        this.scoreHelperTimeoutHandle = setTimeout(() => {
          elemTarget.nextElementSibling.className = "help-block d-none";
        }, 1500);
      }
    });
  };

  render() {
    const {
      assessmentScore,
      assessmentScoreLimit,
      cellId,
      inActiveSchoolYear,
      index,
      isReadOnly,
      onFocus,
      seleniumSelectorText
    } = this.props;
    return (
      <li key={`${assessmentScore._id}:${index}`}>
        <input
          id={cellId}
          ref={r => {
            this[assessmentScore._id] = r;
          }}
          className="form-control"
          placeholder={"Enter Score"}
          name={`score ${index + 1}`}
          data-student-id={assessmentScore.studentId}
          data-purpose="enterScore"
          data-assessment-score-id={assessmentScore._id}
          data-testid={seleniumSelectorText || "scoreInput"}
          data-assessment-score-limit={assessmentScoreLimit ? assessmentScoreLimit.limit : 0}
          data-assessment-score-at={assessmentScoreLimit ? assessmentScoreLimit.at : 0}
          value={this.state.value}
          disabled={assessmentScore.value === "N/A" || !inActiveSchoolYear || isReadOnly}
          onFocus={onFocus}
          onKeyDown={this.handleNavigationKeys}
          onChange={this.onChange}
        />
        <span
          ref={r => {
            this[`error${assessmentScore._id}`] = r;
          }}
          className="help-block d-none"
        />
      </li>
    );
  }
}

ScoreEntryRowCell.propTypes = {
  assessmentScore: PropTypes.object.isRequired,
  assessmentScoreLimit: PropTypes.shape({
    limit: PropTypes.number,
    at: PropTypes.number
  }),
  assessmentResultId: PropTypes.string,
  cellId: PropTypes.string.isRequired,
  index: PropTypes.number.isRequired,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  onFocus: PropTypes.func,
  rowScoreCount: PropTypes.number.isRequired,
  seleniumSelectorText: PropTypes.string,
  siteId: PropTypes.string,
  isAbsent: PropTypes.bool
};

export default ScoreEntryRowCell;
