import React, { Component, useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import isEqual from "lodash/isEqual";
import sortBy from "lodash/sortBy";
import { Alert } from "react-bootstrap";
import get from "lodash/get";
import moment from "moment";
import { cloneDeep } from "lodash";
import getSkillsHistory from "../../student-detail/getSkillHistory";
import { PMScoreEntry } from "../../score-entry/score-entry-row.jsx";
import ActiveSchoolYearMessage from "../../ActiveSchoolYearMessage";
import { Rules } from "/imports/api/rules/rules";
import ClasswidePMChart from "../../student-detail/student-detail-PM-Chart";
import Loading from "../../loading";
import ScoreDisplay from "../../score-display/score-display";
import { ScoreEntryContext } from "../../score-entry/score-entry-context";
import {
  hasGroupPassedClasswideIntervention,
  MAX_SKILLS_FROM_NEXT_GRADE,
  shouldUseDevMode
} from "/imports/api/utilities/utilities";
import { renderQuickActionButtons } from "/imports/ui/utilities";

class ClassWideStudentTable extends Component {
  static contextType = ScoreEntryContext;

  constructor(props) {
    super(props);

    this.state = {
      calculatedAssessmentResults: null,
      errorCalculatingAssessmentResults: false,
      shouldShowStudentsScores: false,
      displayInterventionScores: false,
      selectedDate: null,
      previewingResults: false,
      sortBy: "lastFirst"
    };
  }

  componentDidMount() {
    if (this.areStudentScoresFilled()) {
      this.calculateScores();
    }
  }

  componentDidUpdate(prevProps) {
    // Only call refreshScroll when skillList or selectedIndex actually changes
    if (
      (this.props.skillList.length !== prevProps.skillList.length ||
        this.props.selectedIndex !== prevProps.selectedIndex) &&
      this.props.skillList.length &&
      this.props.selectedIndex !== null
    ) {
      this.props.refreshScroll();
    }
    if (
      !isEqual(this.props.latestAssessmentResult, prevProps.latestAssessmentResult) &&
      this.areStudentScoresFilled()
    ) {
      this.calculateScores();
    }
    if (this.props.selectedIndex !== prevProps.selectedIndex) {
      this.hideSelectedScores();
    }
  }

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillReceiveProps(newProps) {
    if (!isEqual(this.props.studentPMEntries, newProps.studentPMEntries)) {
      this.setState({ calculatedAssessmentResults: null });
    }
  }

  saveScoreResult = () => {
    if (!this.props.assessmentResult) {
      return;
    }
    this.context.clearUnusualHighScoreFields();
    this.saveScoreButton.innerHTML = "Saving...";
    this.saveScoreButton.setAttribute("disabled", "disabled");
    const { isAdditional = false } = this.props.skillList[this.props.selectedIndex];
    Meteor.call(
      "saveScoreResult",
      {
        assessmentResultId: this.props.assessmentResult._id,
        isAdditional
      },
      (err, data) => {
        if (err) {
          this.setState({ errorCalculatingAssessmentResults: true });
        } else {
          this.setState({
            errorCalculatingAssessmentResults: false,
            calculatedAssessmentResults: null
          });
          this.props.setIdOfStudentGroupWithConfetti(data.hasPassed ? this.props.studentGroup._id : null);
        }
      }
    );

    this.setState({ previewingResults: false });
  };

  calculateScores = () => {
    const scrollStep = -document.documentElement.scrollTop / (1000 / 15);
    const scrollInterval = setInterval(() => {
      if (document.documentElement.scrollTop !== 0) {
        window.scrollBy(0, scrollStep);
      } else {
        clearInterval(scrollInterval);
        this.calculateScoreResult();
      }
    }, 15);
  };

  calculateScoreResult = () => {
    if (!this.props.assessmentResult) {
      return;
    }
    Meteor.call(
      "calculateScoreResult",
      {
        assessmentResultId: this.props.assessmentResult._id
      },
      (err, res) => {
        if (err) {
          this.setState({ errorCalculatingAssessmentResults: true });
        } else {
          this.setState({
            errorCalculatingAssessmentResults: false,
            calculatedAssessmentResults: res,
            previewingResults: true
          });
        }
      }
    );
  };

  showSelectedScores = event => {
    event.preventDefault();
    this.setState({
      displayInterventionScores: true,
      selectedDate: event.target.x
    });
  };

  hideSelectedScores = event => {
    event?.preventDefault?.();
    this.setState({
      displayInterventionScores: false,
      selectedDate: null
    });
  };

  displayScoresForSelectedDate = () => {
    const scores = this.props.studentGroup.history.find(result => result.whenEnded.on === this.state.selectedDate);
    if (!scores) {
      return null;
    }
    return <ScoreDisplay scores={scores} />;
  };

  get sortByString() {
    return this.state.sortBy === "lastFirst" ? "Last, First" : "First / Last";
  }

  toggleSorting = () => {
    this.setState(prevState => ({ sortBy: prevState.sortBy === "lastFirst" ? "firstLast" : "lastFirst" }));
  };

  getSortingValue = studentObject => {
    const { student } = studentObject;
    const { firstName, lastName } = student.identity.name;
    return this.state.sortBy === "lastFirst" ? `${lastName} ${firstName}` : `${firstName} ${lastName}`;
  };

  getStudents = () => sortBy(this.props.studentPMEntries || [], this.getSortingValue);

  renderPMChart() {
    const { skillList, selectedIndex, studentGroup, latestClassScores: classScores, studentPMEntries } = this.props;
    const {
      calculatedAssessmentResults,
      shouldShowStudentsScores,
      displayInterventionScores,
      selectedDate
    } = this.state;
    const shouldCalculatePreview = this.isOnCurrentSkill() && calculatedAssessmentResults;
    if (shouldCalculatePreview) {
      classScores.scores.push(calculatedAssessmentResults.measures[0].medianScore);
      classScores.dates.push(`<strong>${moment().format("MMM DD")}</strong>`);
    }

    const chartOptions = {
      chartType: "line",
      title: "",
      height: 390,
      xAxisTitle: "",
      yAxisTitle: "Score",
      marginTop: 50,
      marginRight: 175
    };
    if (skillList.length && selectedIndex !== null) {
      const skillScores = cloneDeep(skillList[selectedIndex]);
      if (shouldCalculatePreview) {
        const { scores, measures, created } = calculatedAssessmentResults;
        const { medianScore } = measures[0];
        const assessmentTimestamp = new Date(created.on).getTime();
        skillScores.classMedianScores.push([assessmentTimestamp, medianScore >= 0 ? medianScore : null, "Preview"]);
        scores.forEach(({ studentId, value }) => {
          let existingScore = skillScores.allStudentsScores.find(score => score.studentId === studentId);
          if (!existingScore) {
            const { student: newStudent } = studentPMEntries.find(({ student }) => student._id === studentId);
            if (newStudent) {
              const {
                identity: {
                  name: { firstName, lastName }
                }
              } = newStudent;
              const newStudentScore = { studentId, lastName, name: `${firstName} ${lastName}`, data: [] };
              skillScores.allStudentsScores.push(newStudentScore);
              existingScore = skillScores.allStudentsScores[skillScores.allStudentsScores.length - 1];
            }
          }
          existingScore.data.push([assessmentTimestamp, value !== "N/A" ? parseInt(value) : null, "Preview"]);
        });
      }
      return (
        <ClasswidePMChart
          scores={skillScores}
          studentGroup={studentGroup}
          chartId={`classwide_PM_CHART_${studentGroup._id}_${selectedIndex}`}
          type="chart"
          options={chartOptions}
          pmName="Progress Monitoring Scores"
          shouldShowStudentsScores={shouldShowStudentsScores}
          displayInterventionScores={displayInterventionScores}
          showSelectedScores={this.showSelectedScores}
          hideSelectedScores={this.hideSelectedScores}
          selectedScoreDate={selectedDate}
          scoresClickable={true}
        />
      );
    }

    return <Loading />;
  }

  renderStudents = () => {
    if (!this.isOnCurrentSkill() || !this.props.inActiveSchoolYear || !this.props.assessmentResult) {
      return null;
    }
    return this.getStudents().map((sEntry, index) => (
      <PMScoreEntry
        studentIndex={index}
        key={sEntry.student._id}
        student={sEntry.student}
        inActiveSchoolYear={this.props.inActiveSchoolYear}
        isReadOnly={this.props.isReadOnly}
        assessmentScores={sEntry.studentAssessmentScores}
        assessmentResultId={this.props.assessmentResult._id}
        assessmentScoreLimits={this.props.assessmentScoreLimits}
        sortBy={this.state.sortBy}
      />
    ));
  };

  renderSaveResultsButton = () => {
    return (
      <button
        type="button"
        ref={ref => {
          this.saveScoreButton = ref;
        }}
        className="calculate-scores-js btn btn-success pull-right"
        name="buttonSaveScoreResult"
        data-testid="saveScoreBtn"
        onClick={this.saveScoreResult}
      >
        Save Results <i className="fa fa-chevron-right fa-left" />
      </button>
    );
  };

  renderUserFeedback(calculatedAssessmentResults) {
    if (!this.canProceedWithSkillReview()) {
      return null;
    }

    const { instructionalTarget, masteryTarget } = this.props.latestClassScores;

    const { medianScore, totalStudentsAssessed, targetScores, studentScores } = calculatedAssessmentResults.measures[0];

    if (
      hasGroupPassedClasswideIntervention({
        medianScore,
        totalStudentsAssessed,
        targetScores,
        studentScores,
        numberOfEnrolledStudents: calculatedAssessmentResults.scores.length
      })
    ) {
      if (totalStudentsAssessed > 10 || medianScore >= masteryTarget) {
        return (
          <Alert
            variant="success"
            className="alert-success-dark alert-warning-flex"
            data-testid="placementNotification"
          >
            <div className="alert-item-center">
              <i className="fa fa-thumbs-o-up fa-lg fa-right" />
              Your class received a{" "}
              <strong>
                <u>median score of {medianScore}</u>
              </strong>
              . Excellent work! Save your scores and move on to the next skill.
            </div>
            <div>{this.renderSaveResultsButton()}</div>
          </Alert>
        );
      }
      return (
        <Alert variant="success" className="alert-success-dark alert-warning-flex" data-testid="placementNotification">
          <div className="alert-item-center">
            <i className="fa fa-thumbs-o-up fa-lg fa-right" />
            Your class received a{" "}
            <strong>
              <u>median score of {medianScore} </u>
            </strong>
            and the majority of students in your small class are above the instructional target. Nice work! Save your
            scores and move on to the next skill.
          </div>
          <div>{this.renderSaveResultsButton()}</div>
        </Alert>
      );
    }
    if (medianScore < instructionalTarget) {
      return (
        <div>
          <Alert variant="danger" className="alert-warning-dark alert-warning-flex" data-testid="placementNotification">
            <div className="alert-item-center">
              <i className="fa fa-warning fa-lg" />
              It appears your class needs to better learn this skill. We recommend providing an acquisition this week.
            </div>
            <div>{this.renderSaveResultsButton()}</div>
          </Alert>
        </div>
      );
    }
    return (
      <Alert variant="warning" className="alert-warning-dark alert-warning-flex" data-testid="placementNotification">
        <div className="alert-item-center">
          <i className="fa fa-warning fa-lg" /> Your class received a{" "}
          <strong>
            <u>median score of {medianScore}</u>
          </strong>
          . Please save your scores and continue practicing this skill.
        </div>
        <div>{this.renderSaveResultsButton()}</div>
      </Alert>
    );
  }

  renderErrorMessage = () => (
    <div>
      <br />
      <Alert variant="danger" className="alert-danger-dark" data-testid="placementNotification">
        <i className="fa fa-warning fa-lg" /> Error calculating results. Please refresh page and try again.
      </Alert>
    </div>
  );

  toggleStudentsScoresVisibility = () => {
    this.setState({
      shouldShowStudentsScores: !this.state.shouldShowStudentsScores
    });
  };

  isInterventionCompleted = () => {
    const { studentPMEntries, assessmentResult } = this.props;
    if (!assessmentResult || !assessmentResult.scores || !studentPMEntries) {
      return false;
    }
    const studentIdsWithEntries = studentPMEntries.map(pme => pme.student._id);
    return (
      !!assessmentResult.scores.length &&
      assessmentResult.scores
        .filter(score => studentIdsWithEntries.includes(score.studentId))
        .every(score => score.status === "COMPLETE" || score.status === "CANCELLED")
    );
  };

  canProceedWithSkillReview = () => {
    return (
      this.isInterventionCompleted() &&
      this.props.inActiveSchoolYear &&
      this.state.calculatedAssessmentResults &&
      !this.state.displayInterventionScores &&
      this.isOnCurrentSkill()
    );
  };

  getEnterScoresButton = () => {
    if (
      this.props.assessmentResult &&
      Object.keys(this.context.getUnusualHighScoreFields(this.props.assessmentResult._id)).length
    ) {
      return (
        <button type="button" className="btn btn-default" name="button" data-testid="calculateScoreBtnDisabled">
          <i className="fa fa-warning fa-left" /> Fix Unusual High Scores
        </button>
      );
    }

    if (this.canProceedWithSkillReview()) {
      return null;
    }

    return this.isOnCurrentSkill() && this.props.inActiveSchoolYear && !this.state.displayInterventionScores ? (
      <button type="button" className="btn btn-default" name="button" data-testid="calculateScoreBtnDisabled">
        <i className="fa fa-warning fa-left" /> Enter All Scores to Continue
      </button>
    ) : null;
  };

  areStudentScoresFilled = () => {
    const { studentPMEntries } = this.props;
    const studentScores = studentPMEntries.map(se => se.studentAssessmentScores[0].value).filter(f => f);
    return studentPMEntries.length === studentScores.length;
  };

  renderSelectedScoreElement = () => {
    if (!this.state.selectedDate) {
      return null;
    }
    return (
      <div className="pull-left">
        Selected Point: {moment(new Date(this.state.selectedDate)).format("hh:mm A - MMM DD YYYY")}{" "}
        <button className="btn btn-default btn-xs m-l-5" onClick={this.hideSelectedScores}>
          Deselect
        </button>
      </div>
    );
  };

  getStudentScoreVisibilityButton = () => {
    return (
      <button className="btn btn-primary" onClick={this.toggleStudentsScoresVisibility}>
        {this.state.shouldShowStudentsScores ? "Hide Students scores" : "Show Students scores"}
      </button>
    );
  };

  renderScoreSection = () => {
    const { isPrinting, inActiveSchoolYear } = this.props;
    if (isPrinting) {
      return null;
    }

    const {
      env: { CI }
    } = this.context;
    const { displayInterventionScores, errorCalculatingAssessmentResults } = this.state;
    const shouldRenderQuickActionButtons = shouldUseDevMode(CI);

    return (
      <React.Fragment>
        <ActiveSchoolYearMessage inActiveSchoolYear={inActiveSchoolYear} />
        <table className="table intervention-table">
          <thead>
            <tr>
              <th colSpan="2">
                <div className="score-status">
                  {this.renderSelectedScoreElement()}

                  <div className="d-flex gap-1">
                    <div className="d-flex flex-grow-1 flex-column justify-content-center">
                      <div>{this.renderUserFeedback(this.state.calculatedAssessmentResults)}</div>
                    </div>
                    <div className="d-flex gap-1 flex-column justify-content-between">
                      <div className="d-flex justify-content-end">{this.getStudentScoreVisibilityButton()}</div>
                      <div className="d-flex justify-content-end">{this.getEnterScoresButton()}</div>
                    </div>
                  </div>
                </div>
                {this.isOnCurrentSkill() && this.props.inActiveSchoolYear && !displayInterventionScores ? (
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      Sort by:{" "}
                      <u role="button" onClick={this.toggleSorting} data-testid="sort-by">
                        {this.sortByString}
                      </u>
                    </div>
                    <div>{shouldRenderQuickActionButtons && renderQuickActionButtons("end")}</div>
                  </div>
                ) : null}
                {errorCalculatingAssessmentResults && this.renderErrorMessage()}
              </th>
            </tr>
          </thead>
          <tbody>
            {displayInterventionScores ? (
              <tr>
                <td>{this.displayScoresForSelectedDate()}</td>
              </tr>
            ) : (
              this.renderStudents()
            )}
          </tbody>
        </table>
      </React.Fragment>
    );
  };

  isOnCurrentSkill = () => {
    const { skillList, selectedIndex, studentGroup } = this.props;
    return skillList[selectedIndex]?.id === studentGroup.currentClasswideSkill.assessmentId;
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    return (
      <div>
        {this.renderPMChart()}
        {this.renderScoreSection()}
      </div>
    );
  }
}

ClassWideStudentTable.propTypes = {
  assessmentResult: PropTypes.object,
  assessmentScoreLimits: PropTypes.array,
  inActiveSchoolYear: PropTypes.bool,
  latestClassScores: PropTypes.object,
  loading: PropTypes.bool,
  schoolYear: PropTypes.number,
  students: PropTypes.array,
  studentGroup: PropTypes.object,
  studentPMEntries: PropTypes.array,
  skillList: PropTypes.array,
  selectedIndex: PropTypes.number,
  selectedSkillIndex: PropTypes.number,
  isReadOnly: PropTypes.bool,
  isPrinting: PropTypes.bool,
  refreshScroll: PropTypes.func,
  setIdOfStudentGroupWithConfetti: PropTypes.func,
  latestAssessmentResult: PropTypes.object
};

function ClassWideStudentTableWithData({
  students,
  studentGroup,
  assessmentResult,
  selectedSkillIndex,
  ...otherProps
}) {
  const [skillData, setSkillData] = useState({
    skillList: [],
    masteryTarget: 0,
    instructionalTarget: 0,
    selectedIndex: null,
    loading: true
  });

  const subscriptionData = useTracker(() => {
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroup._id);
    const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;

    return {
      loading: !rulesSub.ready(),
      gradeLevelRules: rulesSub.ready() ? Rules.findOne({ grade: studentGroup.grade }) : null,
      additionalGradeLevelRules:
        rulesSub.ready() && additionalGradeForRules ? Rules.findOne({ grade: additionalGradeForRules }) : null
    };
  }, [studentGroup._id, studentGroup.grade]);

  useEffect(() => {
    async function loadSkillData() {
      if (subscriptionData.loading || !subscriptionData.gradeLevelRules) {
        return;
      }

      try {
        const studentGroupHistory = JSON.parse(JSON.stringify(studentGroup.history));
        const additionalStudentGroupHistory = JSON.parse(JSON.stringify(studentGroup.additionalHistory || []));

        const defaultSkillList = await getSkillsHistory(
          studentGroupHistory,
          subscriptionData.gradeLevelRules,
          studentGroup,
          students
        );

        const isAdditionalSkillList = true;
        const additionalSkillListFull = await getSkillsHistory(
          additionalStudentGroupHistory,
          subscriptionData.additionalGradeLevelRules,
          studentGroup,
          students,
          undefined,
          isAdditionalSkillList
        );

        const additionalSkillList = (additionalSkillListFull || []).slice(0, MAX_SKILLS_FROM_NEXT_GRADE);
        const skillList = [...(defaultSkillList || []), ...(additionalSkillList || [])];

        let selectedIndex =
          selectedSkillIndex !== null ? selectedSkillIndex : skillList.findIndex(skill => skill.active);
        selectedIndex = selectedIndex >= 0 ? selectedIndex : skillList.length - 1;

        const selectedSkill = skillList[selectedIndex] || {};
        const masteryTarget = selectedSkill.masteryTarget || 0;
        const instructionalTarget = selectedSkill.instructionalTarget || 0;

        setSkillData({
          skillList,
          masteryTarget,
          instructionalTarget,
          selectedIndex,
          loading: false
        });
      } catch (error) {
        console.error("Error loading skill data:", error);
        setSkillData(prev => ({ ...prev, loading: false }));
      }
    }

    loadSkillData();
  }, [
    subscriptionData.loading,
    subscriptionData.gradeLevelRules,
    subscriptionData.additionalGradeLevelRules,
    studentGroup,
    students,
    selectedSkillIndex
  ]);

  const trackerData = useTracker(() => {
    // only want students who were enrolled at the time of the creation of the assessment result
    const studentPMEntries = students.reduce((a, c) => {
      if (!assessmentResult || !assessmentResult.scores) {
        return a;
      }
      const studentAssessmentScore = assessmentResult.scores.find(sc => sc.studentId === c._id);
      if (studentAssessmentScore) {
        a.push({
          studentAssessmentScores: [studentAssessmentScore],
          student: c
        });
      }
      return a;
    }, []);

    // use the target on the student group current classwide skill -- this COULD potentially cause an issue if targets are modified
    const compiledScores = {
      dates: [],
      scores: [],
      instructionalTarget: get(studentGroup, "currentClasswideSkill.targets[0]", 0),
      masteryTarget: get(studentGroup, "currentClasswideSkill.targets[1]", 0),
      skillName: "Median Score"
    };

    const assessmentScoreLimits = [
      {
        assessmentId: studentGroup.currentClasswideSkill.assessmentId,
        limit: skillData.masteryTarget * 5,
        at: skillData.instructionalTarget
      }
    ];

    return {
      studentGroup,
      studentPMEntries,
      latestClassScores: compiledScores,
      assessmentScoreLimits,
      skillList: skillData.skillList,
      selectedIndex: skillData.selectedIndex,
      latestAssessmentResult: assessmentResult,
      loading: subscriptionData.loading || skillData.loading
    };
  }, [students, studentGroup, assessmentResult, skillData, subscriptionData.loading]);

  return <ClassWideStudentTable {...trackerData} {...otherProps} />;
}

ClassWideStudentTableWithData.propTypes = {
  students: PropTypes.array,
  studentGroup: PropTypes.object,
  assessmentResult: PropTypes.object,
  selectedSkillIndex: PropTypes.number
};

export default ClassWideStudentTableWithData;
