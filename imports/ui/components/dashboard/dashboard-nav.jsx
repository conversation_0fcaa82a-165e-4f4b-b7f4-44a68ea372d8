import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import get from "lodash/get";
import { Meteor } from "meteor/meteor";
import { Link } from "react-router-dom";

import * as utils from "/imports/api/utilities/utilities";
import { isAdminOrUniversalCoach } from "/imports/api/roles/methods";
import {
  dashboardNavs,
  getMeteorUserSync,
  hasGroupPassedClasswideIntervention
} from "/imports/api/utilities/utilities";

import { isHighSchoolGrade } from "../../utilities";
import { AppDataContext } from "../../routing/AppDataContext";

export const DashboardNav = ({ situation, studentGroup, activeNav, idsOfStudentsInIndividualInterventions }) => {
  const context = useContext(AppDataContext);
  const { orgid, individualInterventionQueue = [], _id: studentGroupId, siteId, grade } = studentGroup;
  const idsOfStudentsRecommendedForIndividualInterventions = individualInterventionQueue.filter(
    studentId => !idsOfStudentsInIndividualInterventions.includes(studentId)
  );
  const hasIndividualInterventionRecommendations = idsOfStudentsRecommendedForIndividualInterventions.length > 0;

  const hasCompletedClasswideIntervention = !!studentGroup?.history?.find(historyItem => {
    const { enrolledStudentIds } = historyItem;
    const { medianScore, totalStudentsAssessed, targetScores, studentScores } = historyItem.assessmentResultMeasures[0];
    return (
      historyItem.type === "classwide" &&
      hasGroupPassedClasswideIntervention({
        medianScore,
        totalStudentsAssessed,
        targetScores,
        studentScores,
        numberOfEnrolledStudents: enrolledStudentIds.length
      })
    );
  });

  const lis = [];
  const userSiteAccess = get(getMeteorUserSync(), "profile.siteAccess", []);
  const isAdminUser = isAdminOrUniversalCoach(siteId, userSiteAccess);
  const isSupportUser = userSiteAccess[0]?.role === "arbitraryIdsupport";
  const isTeacherUser = !!userSiteAccess.find(
    sa =>
      sa.role === "arbitraryIdteacher" &&
      sa.siteId === studentGroup.siteId &&
      sa.isActive &&
      sa.schoolYear === studentGroup.schoolYear
  );
  const hasClasswideIntervention = situation & utils.situations.classwide;
  const currentClasswideSkillMessageCode = get(studentGroup, "currentClasswideSkill.message.messageCode");
  const isClasswideInterventionComplete = currentClasswideSkillMessageCode === "5" || !!studentGroup.hasCompletedCWI;
  const [shouldShowBeginInterventionWithoutScreeningTab, setShouldShowBeginInterventionWithoutScreeningTab] = useState(
    false
  );
  const isHS = isHighSchoolGrade(grade);

  useEffect(() => {
    if (orgid) {
      Meteor.call("Organizations:areInterventionsWithoutScreeningsAllowed", orgid, (err, resp) => {
        if (!err && resp) {
          setShouldShowBeginInterventionWithoutScreeningTab(resp);
        }
      });
    }
  }, [orgid]);

  useEffect(() => {
    if (activeNav !== "classwide") {
      context.updateAppDataContext({ idOfStudentGroupWithConfetti: null });
    }
  }, [activeNav]);

  if (isHS && !hasClasswideIntervention) {
    lis.push(
      <li className="nav-container-item" key="hsClasswide">
        <Link
          className={`${activeNav === dashboardNavs.screening ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/screening`}
          data-testid="hsClasswideNav"
        >
          Classwide Intervention
        </Link>
      </li>
    );
  }
  if (hasClasswideIntervention) {
    lis.push(
      <li className="nav-container-item" key="classwideNav">
        <Link
          className={`${activeNav === dashboardNavs.classwide ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/classwide`}
          data-testid="classwideInterventionTab"
        >
          Classwide Intervention
        </Link>
        {isClasswideInterventionComplete ? (
          <div className="important-dog-ear">
            <i className="fa fa-star important-dog-ear-icon gold-icon" />
          </div>
        ) : null}
      </li>
    );
  }
  if (situation & utils.situations.individual) {
    lis.push(
      <li className="nav-container-item" key="individualNav">
        <Link
          className={`${activeNav === dashboardNavs.individual ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/individual`}
          data-testid="individualInterventionTab"
        >
          Individual Interventions
        </Link>
      </li>
    );
  }
  if (!isHS) {
    lis.push(
      <li className="nav-container-item" key="screeningNav">
        <Link
          className={`${activeNav === dashboardNavs.screening ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/screening`}
          data-testid="screeningTab"
        >
          Screening
        </Link>
      </li>
    );
  }
  lis.push(
    <li className="nav-container-item" key="studentsNav">
      <Link
        className={`${activeNav === dashboardNavs.students ? "active" : ""}`}
        to={`/site/${siteId}/student-groups/${studentGroupId}/students`}
        data-testid="studentsTab"
      >
        Students
      </Link>
      {hasIndividualInterventionRecommendations ? (
        <div className="important-dog-ear">
          <i className="fa fa-star important-dog-ear-icon" />
        </div>
      ) : null}
    </li>
  );
  if ((isAdminUser || isTeacherUser || isSupportUser) && (!isHS || (isHS && hasCompletedClasswideIntervention))) {
    lis.push(
      <li className="nav-container-item" key="growthNav">
        <Link
          className={`${activeNav === dashboardNavs.growth ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/growth`}
          data-testid="growthTab"
        >
          Growth
        </Link>
      </li>
    );
  }
  if (
    (isAdminUser || isTeacherUser || isSupportUser) &&
    !isHS &&
    !hasClasswideIntervention &&
    shouldShowBeginInterventionWithoutScreeningTab
  ) {
    lis.push(
      <li className="nav-container-item" key="interventionWithoutScreeningNav">
        <Link
          className={`${activeNav === dashboardNavs.interventionWithoutScreening ? "active" : ""}`}
          to={`/site/${siteId}/student-groups/${studentGroupId}/interventionWithoutScreening`}
          data-testid="interventionWithoutScreeningTab"
        >
          Begin classwide intervention without screening
        </Link>
      </li>
    );
  }
  return (
    <ul className="nav nav-pills middle-sub-nav" id="dashboard-nav-items">
      {lis}
    </ul>
  );
};

DashboardNav.propTypes = {
  activeNav: PropTypes.string,
  grade: PropTypes.string,
  hasCompletedClasswideIntervention: PropTypes.bool,
  hasIndividualInterventionRecommendations: PropTypes.bool,
  idsOfStudentsInIndividualInterventions: PropTypes.array,
  navs: PropTypes.object,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  situation: PropTypes.number,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string
};
