import { assert } from "chai";
import React from "react";
import { mount } from "enzyme";
import { PureDashboardIndividual as DashboardIndividual } from "./dashboard-individual.jsx";
import IndividualComplete from "./individual-complete.jsx";

describe("InterventionContent UI", () => {
  describe("IndividualComplete Component", () => {
    const studentWithMessageToDisplay = [
      {
        _id: "studentId",
        identity: {
          name: {
            firstName: "Test",
            lastName: "Tester"
          }
        },
        currentSkill: {
          message: {
            code: "99",
            dismissed: false
          }
        }
      }
    ];
    const studentWithMessageToDisplayAndAssessmentId = [
      {
        _id: "studentId",
        identity: {
          name: {
            firstName: "Test",
            lastName: "Tester"
          }
        },
        currentSkill: {
          assessmentId: "assessmentId",
          message: {
            code: "99",
            dismissed: false
          }
        }
      }
    ];
    const studentGroup = {};
    it("is rendered when the Student has a currentSkill with a message that has not been dismissed", () => {
      const dashboardIndividualComponent = mount(
        <DashboardIndividual
          students={studentWithMessageToDisplay}
          assessmentResults={[]}
          studentGroup={studentGroup}
        />
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find("IndividualComplete");
      assert.isOk(IndividualCompleteComponent.length);
    });
    it("is not rendered when the Student has a currentSkill with a message that has been dismissed", () => {
      studentWithMessageToDisplay[0].currentSkill.message.dismissed = true;
      const dashboardIndividualComponent = mount(
        <DashboardIndividual
          students={studentWithMessageToDisplay}
          assessmentResults={[]}
          studentGroup={studentGroup}
        />
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find(IndividualComplete);
      assert.isNotOk(IndividualCompleteComponent.length);
    });
    it("is not rendered when the Student has a currentSkill with an assessmentId", () => {
      const dashboardIndividualComponent = mount(
        <DashboardIndividual
          students={studentWithMessageToDisplayAndAssessmentId}
          assessmentResults={[]}
          studentGroup={studentGroup}
        />
      );
      const IndividualCompleteComponent = dashboardIndividualComponent.find(IndividualComplete);
      assert.isNotOk(IndividualCompleteComponent.length);
    });
  });
});
