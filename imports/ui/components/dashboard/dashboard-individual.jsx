import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
// import { Button } from "react-bootstrap";

import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import queryString from "query-string";
import { groupBy, keyBy, map, uniq } from "lodash";

import IndividualIntervention from "../../pages/dashboard/individual-intervention.jsx";
import FollowUpAssessmentContainer from "../../pages/dashboard/follow-up-assessment.jsx";
import IndividualComplete from "./individual-complete.jsx";
import ActiveSchoolYearMessage from "../ActiveSchoolYearMessage";
import ScrollIndicator from "../scrollIndicator";
import ScrollIndicatorView from "../scrollIndicatorView";
import InstructionalVideoModal from "../instructional-video-modal";
import Loading from "../loading";
import { GroupedAssessmentResults } from "./grouped-assessment-results";

class DashboardIndividual extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      scroll: {
        lastReload: new Date().valueOf()
      },
      isFetchingSkillGroups: false,
      skillGroups: [],
      shouldOpenManualIIVideoPopup: false,
      videoId: "",
      videoTimestamp: ""
    };
  }

  componentDidMount() {
    this.setState({
      isFetchingSkillGroups: true
    });
    const { siteId, schoolYear, orgid } = this.props.studentGroup;
    Meteor.call("getSkillGroupAssignments", { siteId, schoolYear, orgid }, (err, resp) => {
      if (!err) {
        this.setState({ skillGroups: resp });
      } else {
        Alert.error("Error getting skill groups");
      }
      this.setState({ isFetchingSkillGroups: false });
    });
    this.setVideoData();
  }

  setVideoData = () => {
    const youTubeUrl = "https://www.youtube.com/watch?v=V5PPIQeXCD0&feature=youtu.be";
    const queryParameters = youTubeUrl.substring(youTubeUrl.indexOf("?"));
    const { v, t = "" } = queryString.parse(queryParameters);
    this.setState({
      videoId: v,
      videoTimestamp: parseInt(t.replace(/[^0-9.]+/g, "")) // removes non-digit characters and parses into number
    });
  };

  refreshScroll = () => {
    const state = { ...this.state };
    state.scroll.lastReload = new Date().valueOf();
    this.setState(state);
  };

  renderInterventionComplete = (stu, context, shouldDismissEndIntervention = false) => (
    <IndividualComplete
      key={`Individual_${context}_${stu._id}`}
      student={stu}
      studentGroup={{
        _id: this.props.studentGroup?._id,
        orgid: this.props.studentGroup?.orgid,
        siteId: this.props.studentGroup?.siteId
      }}
      shouldDismissEndIntervention={shouldDismissEndIntervention}
    />
  );

  renderIndividualIntervention = ar => {
    const student = this.props.students.find(s => s._id === ar.studentId);
    return (
      <IndividualIntervention
        key={`${student._id}_individual_int`}
        assessmentName={ar.individualSkills.assessmentName}
        inActiveSchoolYear={this.props.inActiveSchoolYear}
        isReadOnly={this.props.isReadOnly}
        studentInfo={student}
        studentGroupId={this.props.studentGroup._id}
        schoolYear={this.props.studentGroup.schoolYear}
        benchmarkAssessmentName={ar.individualSkills.benchmarkAssessmentName}
        assessmentResult={ar}
        refreshScroll={this.refreshScroll}
        skillGroups={this.state.skillGroups}
        isFetchingSkillGroups={this.state.isFetchingSkillGroups}
      />
    );
  };

  renderFollowUpAssessment = ar => {
    const student = this.props.students.find(s => s._id === ar.studentId);
    return (
      <FollowUpAssessmentContainer
        key={`${student._id}_followup`}
        assessmentName={ar.individualSkills.assessmentName}
        inActiveSchoolYear={this.props.inActiveSchoolYear}
        isReadOnly={this.props.isReadOnly}
        studentInfo={student}
        studentGroupId={this.props.studentGroup._id}
        schoolYear={this.props.studentGroup.schoolYear}
        preAssessmentResultId={student.currentSkill ? student.currentSkill.assessmentResultId : null}
        assessmentResult={ar}
        refreshScroll={this.refreshScroll}
        skillGroups={this.state.skillGroups}
        isFetchingSkillGroups={this.state.isFetchingSkillGroups}
      />
    );
  };

  isAssessmentResultBelongsToStudentInGroup = assessmentResult =>
    this.props.students.find(student => student._id === assessmentResult.studentId);

  isStudentFinishedWithIntervention = stu =>
    stu.currentSkill &&
    !stu.currentSkill.assessmentId &&
    stu.currentSkill.message &&
    !stu.currentSkill.message.dismissed;

  openManualIIVideoPopup = () => {
    this.setState({ shouldOpenManualIIVideoPopup: true });
  };

  closeManualIIVideoPopup = () => {
    this.setState({ shouldOpenManualIIVideoPopup: false });
  };

  renderGroupedInterventions = ({ assessmentResultsPerType = [], type }) => {
    const { students, groupedAssessments, studentGroup, measureNumberByAssessmentId } = this.props;
    const studentsById = keyBy(students, "_id");
    if (!assessmentResultsPerType.length) {
      return null;
    }

    const groupedAssessmentResults = assessmentResultsPerType.reduce((groupedResults, assessmentResult) => {
      const { assessmentId, benchmarkAssessmentId, assessmentName } = assessmentResult.individualSkills;
      const groupedAssessmentsDoc = groupedAssessments.find(ga => ga.assessmentIds.includes(assessmentId)) || {};
      if (!groupedResults[benchmarkAssessmentId]) {
        // eslint-disable-next-line no-param-reassign
        groupedResults[benchmarkAssessmentId] = {};
      }
      const skillName = type === "drillDownAssessment" ? assessmentName : groupedAssessmentsDoc.skillName;
      if (!groupedResults[benchmarkAssessmentId][skillName]) {
        // eslint-disable-next-line no-param-reassign
        groupedResults[benchmarkAssessmentId][skillName] = [];
      }

      const studentDoc = studentsById[assessmentResult.studentId];
      const studentMostRecentOutcome =
        studentDoc?.history?.[0]?.assessmentResultMeasures.find(arm => arm.assessmentId === assessmentId)
          ?.studentResults[0].individualRuleOutcome || "";
      const studentInstructionalLevel = studentMostRecentOutcome === "at" ? "Fluency" : "Acquisition";
      if (!groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel]) {
        // eslint-disable-next-line no-param-reassign
        groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel] = [];
      }
      groupedResults[benchmarkAssessmentId][skillName][studentInstructionalLevel].push(assessmentResult);
      return groupedResults;
    }, {});

    return (
      <div>
        <h3>{type === "individualIntervention" ? "Individual Interventions" : "Drill-Down Assessments"}</h3>
        {map(
          Object.entries(groupedAssessmentResults),
          ([benchmarkAssessmentId, assessmentResultsPerBenchmarkAssessmentId]) => {
            return map(
              Object.entries(assessmentResultsPerBenchmarkAssessmentId),
              ([groupedAssessmentName, assessmentResultsByInstructionalLevel]) => {
                return map(
                  Object.entries(assessmentResultsByInstructionalLevel),
                  ([instructionalLevel, assessmentResults]) => {
                    const sortedAssessmentResults = assessmentResults.sort((a, b) => {
                      const studentA = studentsById[a.studentId];
                      const studentB = studentsById[b.studentId];
                      return studentA.identity.name.lastName.localeCompare(studentB.identity.name.lastName);
                    });
                    return (
                      <GroupedAssessmentResults
                        key={`${groupedAssessmentName}_${instructionalLevel}`}
                        instructionalLevel={instructionalLevel}
                        assessmentResults={sortedAssessmentResults}
                        studentsById={studentsById}
                        type={type}
                        benchmarkAssessmentId={benchmarkAssessmentId}
                        groupedAssessments={groupedAssessments}
                        measureNumberByAssessmentId={measureNumberByAssessmentId}
                        renderIndividualIntervention={this.renderIndividualIntervention}
                        renderFollowUpAssessment={this.renderFollowUpAssessment}
                        studentGroupName={studentGroup.name}
                      />
                    );
                  }
                );
              }
            );
          }
        )}
      </div>
    );
  };

  render() {
    const { students, assessmentResults, inActiveSchoolYear } = this.props;
    const assessmentResultsInStudentGroup = assessmentResults.filter(assessmentResult =>
      this.isAssessmentResultBelongsToStudentInGroup(assessmentResult)
    );
    const {
      drillDownAssessment: drillDownAssessmentResults,
      individualIntervention: individualInterventionAssessmentResults
    } = groupBy(assessmentResultsInStudentGroup, assessmentResult => {
      return assessmentResult.individualSkills.interventions.length ? "individualIntervention" : "drillDownAssessment";
    });
    const studentIdsThatAdvancedToNextSkillTree =
      students
        .filter(s => s.currentSkill?.message?.messageCode === "55" && !s.currentSkill?.userSelectedContinue)
        .map(s => s._id) || [];

    return (
      <ScrollIndicator
        container={this}
        targetSelector={"div"}
        indicatorComponent={<ScrollIndicatorView />}
        uniqKey={this.state.scroll.lastReload}
      >
        <div>
          <ActiveSchoolYearMessage inActiveSchoolYear={inActiveSchoolYear} />
          {/* <div className="text-center"> */}
          {/*  <Button */}
          {/*    variant="default" */}
          {/*    className="m-t-5" */}
          {/*    onClick={this.openManualIIVideoPopup} */}
          {/*    data-testid="manual-intervention-video-button" */}
          {/*  > */}
          {/*    Video - How to manually schedule and implement individual interventions */}
          {/*  </Button> */}
          {/* </div> */}
          {students
            .filter(
              stu =>
                this.isStudentFinishedWithIntervention(stu) && !studentIdsThatAdvancedToNextSkillTree.includes(stu._id)
            )
            .map(stu => this.renderInterventionComplete(stu, "Completed"))}
          {students
            .filter(s => studentIdsThatAdvancedToNextSkillTree.includes(s._id))
            .map(s => this.renderInterventionComplete(s, "Continue_Stop", true))}
          {this.renderGroupedInterventions({
            assessmentResultsPerType: individualInterventionAssessmentResults?.filter(
              a => !studentIdsThatAdvancedToNextSkillTree.includes(a.studentId)
            ),
            type: "individualIntervention"
          })}
          {this.renderGroupedInterventions({
            assessmentResultsPerType: drillDownAssessmentResults?.filter(
              a => !studentIdsThatAdvancedToNextSkillTree.includes(a.studentId)
            ),
            type: "drillDownAssessment"
          })}
          {this.state.shouldOpenManualIIVideoPopup ? (
            <InstructionalVideoModal
              showModal={this.state.shouldOpenManualIIVideoPopup}
              closeModal={this.closeManualIIVideoPopup}
              onCloseModal={() => {}}
              videoId={this.state.videoId}
              videoTimestamp={this.state.videoTimestamp}
              headerText="If this is your first time using manual individual intervention please watch this video."
              testIdPrefix="manual-individual-intervention"
            />
          ) : null}
        </div>
      </ScrollIndicator>
    );
  }
}

DashboardIndividual.propTypes = {
  assessmentResults: PropTypes.array,
  measureNumberByAssessmentId: PropTypes.object,
  inActiveSchoolYear: PropTypes.bool,
  studentGroup: PropTypes.object,
  students: PropTypes.array,
  groupedAssessments: PropTypes.array,
  isReadOnly: PropTypes.bool
};

export default function DashboardIndividualWrapper(props) {
  const [isLoading, setIsLoading] = useState(true);
  const [groupedAssessments, setGroupedAssessments] = useState([]);
  const [measureNumberByAssessmentId, setMeasureNumberByAssessmentId] = useState({});

  useEffect(() => {
    const { siteId } = props.studentGroup;
    const assessmentResultsInStudentGroup = props.assessmentResults.filter(assessmentResult =>
      props.students.find(student => student._id === assessmentResult.studentId)
    );
    // NOTE(fmazur) - assessmentResult assessmentIds contain ids for interventionSkill and goalSkill
    const assessmentIdsInGroup = uniq(assessmentResultsInStudentGroup.map(a => a.assessmentIds).flat(2));
    Meteor.call("getGroupedAssessments", { assessmentIds: assessmentIdsInGroup, siteId }, (err, resp) => {
      if (!err) {
        const { groupedAssessments: ga, measureNumberByAssessmentId: a } = resp || {};
        setGroupedAssessments(ga);
        setMeasureNumberByAssessmentId(a);
      }
      setIsLoading(false);
    });
  }, []);

  if (isLoading) {
    return <Loading />;
  }
  return (
    <DashboardIndividual
      {...props}
      groupedAssessments={groupedAssessments}
      measureNumberByAssessmentId={measureNumberByAssessmentId}
    />
  );
}

DashboardIndividualWrapper.propTypes = {
  assessmentResults: PropTypes.array,
  students: PropTypes.array,
  studentGroup: PropTypes.object
};

export { DashboardIndividual as PureDashboardIndividual };
