import React, { Component, useContext } from "react";
import PropTypes from "prop-types";
import Alert from "react-s-alert";
import { cloneDeep, findLast, get, isObject, uniq } from "lodash";
import { Meteor } from "meteor/meteor";
import IndividualSkillList from "./individual-skill-list.jsx";
import IndividualGoalSkillList from "./individual-goal-skill-list.jsx";
import { Rules } from "/imports/api/rules/rules";
import ClasswidePMChart from "./student-detail-PM-Chart.jsx";
import {
  translateBenchmarkPeriod,
  calculateRoI,
  getPageBreakClassForEverySecondElementByIndex
} from "/imports/api/utilities/utilities";
import { ScreeningAssignments } from "/imports/api/screeningAssignments/screeningAssignments";
import { Assessments } from "/imports/api/assessments/assessments";
import IndividualGraphTitle from "./individual-graph-title";
import { ClassContext } from "../../pages/classContext";
import { getTimeRange, isOnPrintPage, useContextOrProps } from "../../utilities";
import ConfirmModal from "../../pages/data-admin/confirm-modal";
import Loading from "../loading";
import { AppDataContext } from "../../routing/AppDataContext";

class IndividualInterventionProgress extends Component {
  static contextType = ClassContext;

  constructor(props, context) {
    super(props, context);
    const student = useContextOrProps({ componentInstance: this, property: "student", verificationGetPath: "_id" });
    const studentGroup = useContextOrProps({
      componentInstance: this,
      property: "studentGroup",
      verificationGetPath: "_id"
    });

    const activeBMPeriodIds = getActiveBMPeriods(student.history);
    const skillHistoryHierarchy = getHistoryHierarchy(student, studentGroup, activeBMPeriodIds);

    const { goalSkillScore, interventionSkillScore } = this.getGoalAndInterventionScore(student);
    this.state = {
      selectedGoalAssessmentId: null,
      selectedAssessmentToDisplay: null,
      skillHistoryHierarchy,
      activeBMPeriodIds,
      isEditing: false,
      isClearScoresModalOpen: false,
      student,
      studentGroup,
      lastScoreUpdatedAt: 0,
      isUpdatingScores: false,
      initialGoalSkillScore: goalSkillScore,
      initialInterventionSkillScore: interventionSkillScore,
      newGoalSkillScore: goalSkillScore,
      newInterventionSkillScore: interventionSkillScore,
      shouldShowSaveIndividualScores: false,
      isEditScoresModalOpen: false
    };
  }

  componentDidUpdate() {
    if (
      get(this.context.student, "history") &&
      (this.state.student.history.length !== this.context.student.history.length ||
        get(this.state.student.history[0], "assessmentResultMeasures") !==
          get(this.context.student.history[0], "assessmentResultMeasures"))
    ) {
      this.setState({
        student: this.context.student
      });
      this.updateDisplayedScores();
      this.setState({
        skillHistoryHierarchy: getHistoryHierarchy(
          this.context.student,
          this.context.studentGroup,
          getActiveBMPeriods(this.state.student.history)
        )
      });
    }
  }

  updateDisplayedScores = () => {
    const student = useContextOrProps({ componentInstance: this, property: "student", verificationGetPath: "_id" });
    const { goalSkillScore, interventionSkillScore } = this.getGoalAndInterventionScore(student);
    this.setState({
      initialGoalSkillScore: goalSkillScore,
      initialInterventionSkillScore: interventionSkillScore,
      newGoalSkillScore: goalSkillScore,
      newInterventionSkillScore: interventionSkillScore
    });
  };

  // eslint-disable-next-line class-methods-use-this
  getGoalAndInterventionScore(student) {
    const { assessmentResultMeasures, assessmentId, benchmarkAssessmentId, interventions } = student.history?.[0] || {
      assessmentResultMeasures: []
    };
    const goalSkillResultMeasure = assessmentResultMeasures.find(arm => arm.assessmentId === benchmarkAssessmentId);
    const interventionSkillResultMeasure = assessmentResultMeasures.find(arm => arm.assessmentId === assessmentId);
    const goalSkillScore = goalSkillResultMeasure ? goalSkillResultMeasure.studentScores[0] : undefined;
    let interventionSkillScore;
    if (
      assessmentId !== benchmarkAssessmentId &&
      assessmentResultMeasures.length === 2 &&
      interventions &&
      interventions.length > 0
    ) {
      interventionSkillScore = interventionSkillResultMeasure
        ? interventionSkillResultMeasure.studentScores[0]
        : undefined;
    }
    return { goalSkillScore, interventionSkillScore };
  }

  selectGraphToDisplay = (benchmarkAssessmentId, assessmentId, isGoalSkill) => {
    if (isGoalSkill) {
      this.setState({
        selectedAssessmentToDisplay: null,
        selectedGoalAssessmentId: benchmarkAssessmentId
      });
    } else {
      this.setState({
        selectedAssessmentToDisplay: assessmentId
      });
    }
  };

  getGraphableGoals = () => {
    return this.state.skillHistoryHierarchy.filter(sh => sh.studentScores && sh.studentScores.length > 0);
  };

  renderAllSkills(graphableSkills) {
    const { student } = this.state;
    if (!graphableSkills.length) {
      return null;
    }
    return (
      <div>
        {graphableSkills.map((skill, index) => (
          <div
            className={getPageBreakClassForEverySecondElementByIndex(index, true)}
            key={`skill_CHART_${student._id}_${index}`}
          >
            <IndividualGraphTitle skill={skill} goalSkill={false} />
            <ClasswidePMChart
              scores={skill}
              chartId={`skill_CHART_${student._id}_${index}`}
              type="chart"
              pmName="Progress Monitoring Scores"
              studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
              studentGroup={this.state.studentGroup}
            />
          </div>
        ))}
      </div>
    );
  }

  renderAllGoals(graphableGoals, goalSkillSectionClassName = "", startFromFirstIndex = false) {
    const { student } = this.state;
    if (!graphableGoals.length) {
      return null;
    }
    return (
      <div className={goalSkillSectionClassName}>
        {graphableGoals.map((goal, index) => {
          const goalCopy = cloneDeep(goal);
          goalCopy.active = false;
          return (
            <div
              className={getPageBreakClassForEverySecondElementByIndex(index, startFromFirstIndex)}
              key={`goal_CHART_${student._id}_${index}`}
            >
              <IndividualGraphTitle skill={goalCopy} goalSkill={true} />
              <ClasswidePMChart
                scores={goalCopy}
                chartId={`goal_CHART_${student._id}_${index}`}
                type="chart"
                pmName="Progress Monitoring Scores"
                studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
                studentGroup={this.state.studentGroup}
              />
            </div>
          );
        })}
      </div>
    );
  }

  getDatesFromScores = (goal, skill) => {
    if (!goal || !goal.studentScores) {
      return null;
    }
    const goalDates = goal.studentScores.map(date => (isObject(date) ? date.x : date[0]));
    let skillDates = [];
    if (skill) {
      skillDates = skill.studentScores.map(date => (isObject(date) ? date.x : date[0]));
    }
    const selectedScoreDates = [...skillDates, ...goalDates];

    return selectedScoreDates.length ? getTimeRange(selectedScoreDates) : null;
  };

  toggleEditingScoresModal = () => {
    this.setState(state => ({ ...state, isEditScoresModalOpen: !this.state.isEditScoresModalOpen }));
  };

  confirmActionEditModal = () => {
    this.toggleEditingScoresModal();
    this.toggleEditingScores();
  };

  handleEditScoresButton = () => {
    if (this.state.isEditing) {
      this.toggleEditingScores();
    } else {
      this.toggleEditingScoresModal();
    }
  };

  toggleEditingScores = () => {
    const editState = this.state.isEditing;
    this.setState(state => ({ ...state, isEditing: !this.state.isEditing }));
    if (editState) {
      if (this.state.isUpdatingScores) {
        this.updateDisplayedScores();
      }
      this.setState({
        shouldShowSaveIndividualScores: false
      });
    }
  };

  clearScores = () => {
    if (!this.state.student.history.length) {
      return;
    }
    this.setState({ isUpdatingScores: true });
    Meteor.call(
      "clearLastIndividualInterventionScores",
      {
        studentGroupId: this.state.studentGroup._id,
        studentId: this.state.student._id,
        orgid: this.state.studentGroup.orgid
      },
      (err, resp) => {
        if (!err) {
          const { student, studentGroup } = resp;
          const studentName = this.state.student.identity.name;
          Alert.success(`Successfully cleared score(s) for: ${studentName.lastName}, ${studentName.firstName}`);
          this.setState({ isUpdatingScores: false });
          this.context.setStudent(student); // update student history for context student
          this.context.setStudentGroup(studentGroup);
          const studentWithIndividualHistory = student.history.find(h => h.type === "individual");
          this.context.setContext({ lastIndividualScoreUpdatedAt: get(studentWithIndividualHistory, "whenEnded.on") });
          if (this.state.isEditing) {
            this.toggleEditingScores();
          }
        }
      }
    );
  };

  showSelectedScores = event => {
    event.preventDefault();
    this.setState({
      selectedDate: event.target.x
    });
  };

  checkUnusuallyHighScores = ({
    newGoalSkillScore = this.state.newGoalSkillScore,
    newInterventionSkillScore = this.state.newInterventionSkillScore
  }) => {
    const completedAssessmentsResultsForStudent = this.context.completedAssessmentResults.find(
      result => result.studentId === this.context.student._id
    );
    const isUnusuallyHighIndividualInterventionScore =
      newInterventionSkillScore > completedAssessmentsResultsForStudent.individualSkills.assessmentTargets[1] * 5;
    const isUnusuallyHighGoalSkillScore =
      newGoalSkillScore > completedAssessmentsResultsForStudent.individualSkills.benchmarkAssessmentTargets[1] * 5;
    return { isUnusuallyHighGoalSkillScore, isUnusuallyHighIndividualInterventionScore };
  };

  onValueChange = scoreName => e => {
    e.preventDefault();
    clearTimeout(this.scoreHelperTimeoutHandle);
    const elemTarget = e.target;
    const newScore = e.target.value;
    elemTarget.nextElementSibling.className = "help-block invisible";
    const parsedScore = parseInt(newScore);
    let isUnusuallyHighScore = false;
    const newScoreObject = { [scoreName]: parsedScore >= 0 ? parsedScore : "" };

    if (parsedScore >= 0 || newScore === "") {
      const {
        isUnusuallyHighGoalSkillScore,
        isUnusuallyHighIndividualInterventionScore
      } = this.checkUnusuallyHighScores(newScoreObject);
      isUnusuallyHighScore = isUnusuallyHighGoalSkillScore || isUnusuallyHighIndividualInterventionScore;
      if (
        (scoreName === "newInterventionSkillScore" && isUnusuallyHighIndividualInterventionScore) ||
        (scoreName === "newGoalSkillScore" && isUnusuallyHighGoalSkillScore)
      ) {
        elemTarget.nextElementSibling.className = "help-block text-warning text-center animated bounceIn";
      }
      this.setState(newScoreObject);
    }
    this.shouldShowSaveIndividualScoresButton({ ...newScoreObject, areScoresUnusuallyHigh: isUnusuallyHighScore });
  };

  toggleClearScoresModal = () => {
    this.setState(prevState => ({ isClearScoresModalOpen: !prevState.isClearScoresModalOpen }));
  };

  saveNewScores = () => {
    this.setState({ isUpdatingScores: true });
    this.toggleEditingScores();
    Meteor.call(
      "editLastIndividualInterventionScores",
      {
        studentGroupId: this.context.studentGroup._id,
        orgid: this.context.studentGroup.orgid,
        studentId: this.context.student._id,
        newGoalScoreValue: parseInt(this.state.newGoalSkillScore),
        newInterventionScoreValue: parseInt(this.state.newInterventionSkillScore)
      },
      (err, { wereScoresEdited, student, studentGroup }) => {
        const studentName = this.context.student.identity.name;
        if (!err && wereScoresEdited) {
          Alert.success(`Successfully edited score(s) for: ${studentName.lastName}, ${studentName.firstName}`, {
            timeout: 2000
          });
          this.setState({ isUpdatingScores: false });
          this.context.setStudent(student);
          this.context.setStudentGroup(studentGroup);
        } else {
          Alert.error(`There was an error editing score(s) for: ${studentName.lastName}, ${studentName.firstName}`, {
            timeout: 2000
          });
        }
      }
    );
  };

  shouldShowSaveIndividualScoresButton = ({
    newGoalSkillScore = this.state.newGoalSkillScore,
    newInterventionSkillScore = this.state.newInterventionSkillScore,
    areScoresUnusuallyHigh = false
  }) => {
    const { initialGoalSkillScore, initialInterventionSkillScore } = this.state;
    const hasGoalSkillScoreChanged = initialGoalSkillScore !== newGoalSkillScore;
    const hasInterventionSkillScoreChanged = initialInterventionSkillScore !== newInterventionSkillScore;
    const isGoalSkillScoreValid = isScoreValid(newGoalSkillScore);
    const isInterventionSkillScoreValid = isScoreValid(newInterventionSkillScore);
    const isDiagnostic = !this.state.initialGoalSkillScore;

    const isAnyScoreEmpty =
      (initialGoalSkillScore !== undefined && newGoalSkillScore === "") ||
      (initialInterventionSkillScore !== undefined && newInterventionSkillScore === "");
    const didAnyScoreChange =
      (isGoalSkillScoreValid && hasGoalSkillScoreChanged) ||
      !initialGoalSkillScore ||
      (isInterventionSkillScoreValid && hasInterventionSkillScoreChanged);
    const isDiagnosticScoreValid = isInterventionSkillScoreValid && hasInterventionSkillScoreChanged && isDiagnostic;
    const shouldShowSaveIndividualScores =
      !areScoresUnusuallyHigh && !isAnyScoreEmpty && (didAnyScoreChange || isDiagnosticScoreValid);
    if (shouldShowSaveIndividualScores) {
      this.setState({ shouldShowSaveIndividualScores: true });
    } else {
      this.setState({ shouldShowSaveIndividualScores: false });
    }
  };

  renderEditScoreRows = () => {
    return this.state.isEditing ? (
      <div className="row mt-1 mb-1 p-l-1">
        {this.renderInterventionSkillRow()}
        {this.renderGoalSkillRow()}
      </div>
    ) : null;
  };

  renderInterventionSkillRow = () => {
    return this.state.initialInterventionSkillScore !== undefined ? (
      <React.Fragment>
        <hr className="col-sm-9" />
        <label className="col-sm-7">Intervention Skill score</label>
        <div className="col-sm-2 input-no-arrows">
          <input
            className="form-control"
            type="text"
            data-testid="individualInterventionEditStudent"
            value={this.state.newInterventionSkillScore}
            onChange={this.onValueChange("newInterventionSkillScore")}
          />
          <span className="help-block text-center invisible">Unusual High Score</span>
        </div>
      </React.Fragment>
    ) : null;
  };

  renderGoalSkillRow = () => {
    return this.state.initialGoalSkillScore !== undefined ? (
      <React.Fragment>
        <hr className="col-sm-9" />
        <label className="col-sm-7">Goal Skill score</label>
        <div className="col-sm-2 input-no-arrows">
          <input
            className="form-control"
            type="text"
            data-testid="individualInterventionEditGoalStudent"
            value={this.state.newGoalSkillScore}
            onChange={this.onValueChange("newGoalSkillScore")}
          />
          <span className="help-block text-center invisible">Unusual High Score</span>
        </div>
        <hr className="col-sm-9" />
      </React.Fragment>
    ) : null;
  };

  getSubjectName = () => {
    return this.context.userPrivilegedRole === "arbitraryIddataAdmin" ? "teacher" : "customer";
  };

  renderConfirmModal = () => {
    let modalDateClearScore = [];
    const scoreDate = this.context.lastIndividualScoreUpdatedAt;
    if (scoreDate) {
      modalDateClearScore = new Date(scoreDate).toDateString().split(" ");
    }
    return (
      <ConfirmModal
        showModal={this.state.isClearScoresModalOpen}
        onCloseModal={this.toggleClearScoresModal}
        confirmAction={() => {
          this.clearScores();
        }}
        headerText="Are you sure you want to clear score(s) for selected graph point(s)?"
        bodyText={<strong>WARNING:</strong>}
        bodyQuestion={`This action will permanently delete the scores for ${modalDateClearScore[1]}-${
          modalDateClearScore[2]
        } and cannot be undone.
                Confirm with the ${this.getSubjectName()} that they have a paper copy of student score(s) or are willing to
                re-administer the Intervention`}
        confirmText="Yes, clear scores"
        isWarning={true}
      />
    );
  };

  renderEditConfirmationModal = () => {
    const { isEditScoresModalOpen } = this.state;
    return (
      <ConfirmModal
        showModal={isEditScoresModalOpen}
        onCloseModal={this.toggleEditingScoresModal}
        confirmAction={this.confirmActionEditModal}
        headerText="Are you sure you want to edit scores for selected graph point?"
        bodyQuestion={`Before editing scores, it is recommended to verify that the ${this.getSubjectName()} has access to the paper tests since all changes that you save are final.`}
        confirmText="Acknowledged"
        isWarning={true}
      />
    );
  };

  renderNoGraphableSkillsNotice = (graphableGoals, graphableSkills) => {
    if (!graphableGoals.length && !graphableSkills.length && isOnPrintPage()) {
      return (
        <div className="alert alert-info text-center">
          No graphable intervention skills or goal skills for this student found
        </div>
      );
    }
    return null;
  };

  renderIndividualInterventionSkill = ({
    selectedSkill,
    selectedGoalInterventionSkillList,
    timeRange,
    student,
    selectedGoal,
    selectedGoalAssessmentId
  }) => {
    if (!selectedSkill?.studentScores?.length) {
      return null;
    }

    const assessmentIdPriorToInterventionTimestamps = getDrillDownItemsPriorToIntervention(
      student.history,
      selectedGoalAssessmentId
    ).map(item => item.whenEnded.on);
    const parsedStudentScores = (
      selectedSkill?.studentScores?.map((sr, index) => {
        if (
          index === 0 &&
          !assessmentIdPriorToInterventionTimestamps.includes(sr.x) &&
          sr.name.includes("Drill-down")
        ) {
          return { x: sr.x };
        }
        return sr;
      }) || []
    ).filter(s => "y" in s);

    return (
      <div className="row">
        <div className="col-md-9 print-clear">
          <div>
            <IndividualGraphTitle skill={selectedSkill} goalSkill={false} />
            <ClasswidePMChart
              scores={{ ...selectedSkill, studentScores: parsedStudentScores }}
              chartId={`skill_CHART_${student._id}`}
              type="chart"
              pmName="Progress Monitoring Scores"
              studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
              timeRange={timeRange}
              scoresClickable={this.context.userPrivilegedRole}
              showSelectedScores={this.showSelectedScores}
              studentGroup={this.state.studentGroup}
            />
          </div>
        </div>
        <div className="col-md-3 print-display">
          <IndividualSkillList
            skillList={selectedGoalInterventionSkillList}
            setSelectedAssessment={this.selectGraphToDisplay}
            selectedGoal={selectedGoal}
            selectedSkill={selectedSkill}
          />
        </div>
      </div>
    );
  };

  render() {
    if (!this.state.skillHistoryHierarchy || !this.state.skillHistoryHierarchy.length > 0) {
      return null;
    }
    // If the user selected a goal skill, filter the intervention skill list to only include those that were done
    // in support of the selected goal skill
    const activeBenchmarkAssessmentId =
      findLast(this.state.skillHistoryHierarchy, sh => sh.active)?.goalAssessmentId ||
      findLast(this.state.skillHistoryHierarchy, sh => sh.complete)?.goalAssessmentId;

    // Preselect the active benchmarkAssessment if they have something to show, otherwise pick the last complete one
    let selectedGoalAssessmentId;
    const currentCompletedSkillMatchingBenchmarkSkill = findLast(
      this.state.skillHistoryHierarchy,
      sh => sh.goalAssessmentId === activeBenchmarkAssessmentId
    );
    if (this.state.selectedGoalAssessmentId) {
      ({ selectedGoalAssessmentId } = this.state);
    } else if (
      currentCompletedSkillMatchingBenchmarkSkill.interventions ||
      currentCompletedSkillMatchingBenchmarkSkill.active
    ) {
      selectedGoalAssessmentId = activeBenchmarkAssessmentId;
    } else {
      selectedGoalAssessmentId = findLast(this.state.skillHistoryHierarchy, sh => sh.complete)?.goalAssessmentId;
    }
    const selectedGoalInterventionSkillList = this.state.skillHistoryHierarchy.find(
      sh => sh.goalAssessmentId === selectedGoalAssessmentId
    )?.interventions;
    const selectedGoal = this.state.skillHistoryHierarchy.find(sh => sh.goalAssessmentId === selectedGoalAssessmentId);
    // Pick which intervention graph to display
    let selectedSkillAssessmentId;
    if (this.state.selectedAssessmentToDisplay) {
      selectedSkillAssessmentId = this.state.selectedAssessmentToDisplay;
    } else if (selectedGoalInterventionSkillList && selectedGoalInterventionSkillList.find(skill => skill.active)) {
      selectedSkillAssessmentId = selectedGoalInterventionSkillList.find(skill => skill.active).assessmentId;
    } else if (selectedGoalInterventionSkillList && selectedGoalInterventionSkillList.length) {
      // Select the last one in the list -- should be most recent performed
      selectedSkillAssessmentId =
        selectedGoalInterventionSkillList[selectedGoalInterventionSkillList.length - 1].assessmentId;
    }
    const { student } = this.state;
    const assessmentIdsWithDrillDownPriorToInterventionAndInterventions = uniq([
      ...getDrillDownItemsPriorToIntervention(student.history, selectedGoalAssessmentId).map(d => d.assessmentId),
      ...student.history
        .filter(h => h.benchmarkAssessmentId === selectedGoalAssessmentId && h.interventions.length)
        .map(h => h.assessmentId)
    ]);
    const filteredGoalSkillList =
      selectedGoalInterventionSkillList?.filter(skill =>
        assessmentIdsWithDrillDownPriorToInterventionAndInterventions.includes(skill.assessmentId)
      ) || [];
    const selectedSkill =
      filteredGoalSkillList.find(skill => skill.assessmentId === selectedSkillAssessmentId) ||
      filteredGoalSkillList[filteredGoalSkillList.length - 1];
    const selectedGoalHasScores = selectedGoal && selectedGoal.studentScores && selectedGoal.studentScores.length > 0;

    const timeRange = this.getDatesFromScores(selectedGoal, selectedSkill);
    const graphableGoals = this.getGraphableGoals();
    const graphableSkills = [].concat(...graphableGoals.map(goal => goal.interventions || []));
    graphableSkills.sort(
      (a, b) =>
        a.active - b.active ||
        a.studentScores[a.studentScores.length - 1].x - b.studentScores[b.studentScores.length - 1].x
    );

    const className = !this.props.isFirstSection ? "page-break-before" : "";
    const shouldAddPageBreakForGoalSkillSection =
      (this.props.printAllIndividualInterventionGraphs && graphableSkills?.length > 0) ||
      (!this.props.printAllIndividualInterventionGraphs && selectedSkill);
    const goalSkillSectionClassName = shouldAddPageBreakForGoalSkillSection ? "page-break-before" : "";
    const shouldAddPageBreakAfterFirstGoalSkill = !shouldAddPageBreakForGoalSkillSection && this.props.isFirstSection;

    return (
      <div data-testid="individual-intervention-progress-section" className={className}>
        {this.context.userPrivilegedRole ? null : <h3>Individual Intervention Progress</h3>}
        {this.renderNoGraphableSkillsNotice(graphableGoals, graphableSkills)}
        {this.props.printAllIndividualInterventionGraphs
          ? this.renderAllSkills(graphableSkills)
          : this.renderIndividualInterventionSkill({
              selectedSkill,
              selectedGoalInterventionSkillList: filteredGoalSkillList,
              timeRange,
              student,
              selectedGoal,
              selectedGoalAssessmentId
            })}
        <br />
        {this.props.printAllIndividualInterventionGraphs ? (
          this.renderAllGoals(graphableGoals, goalSkillSectionClassName, shouldAddPageBreakAfterFirstGoalSkill)
        ) : (
          <div className={`row ${goalSkillSectionClassName}`}>
            <div className="col-md-9 print-clear">
              <IndividualGraphTitle skill={selectedGoal} goalSkill={true} />
              {selectedGoalHasScores ? (
                <ClasswidePMChart
                  scores={selectedGoal}
                  chartId={`goal_CHART_${student._id}`}
                  type="chart"
                  pmName="Progress Monitoring Scores"
                  studentName={`${student.identity.name.firstName} ${student.identity.name.lastName}`}
                  timeRange={timeRange}
                  scoresClickable={this.context.userPrivilegedRole}
                  showSelectedScores={this.showSelectedScores}
                  studentGroup={this.state.studentGroup}
                />
              ) : (
                <h5 className="stamped">No new scores have been recorded since screening.</h5>
              )}
            </div>
            <div className="col-md-3 print-display">
              <IndividualGoalSkillList
                goalSkillList={this.state.skillHistoryHierarchy}
                setSelectedAssessment={this.selectGraphToDisplay}
                selectedGoal={selectedGoal}
                activeBMPeriodIds={this.state.activeBMPeriodIds}
              />
            </div>
          </div>
        )}
        {this.context.userPrivilegedRole && this.state.student.history.length ? (
          <React.Fragment>
            <div className="row">
              <div className="col-md-9">
                {this.state.isUpdatingScores ? (
                  <div className="pull-right">
                    <Loading inline={true} message="Updating scores..." />
                  </div>
                ) : (
                  <div className="pull-right mb-1">
                    {this.state.shouldShowSaveIndividualScores ? (
                      <button className="btn btn-success" onClick={this.saveNewScores}>
                        Save
                      </button>
                    ) : null}{" "}
                    <button className="btn btn-primary" onClick={this.handleEditScoresButton}>
                      {this.state.isEditing ? "Cancel Editing Scores" : "Edit Scores"}
                    </button>{" "}
                    <button className="btn btn-danger" onClick={this.toggleClearScoresModal}>
                      Clear Scores
                    </button>
                  </div>
                )}
              </div>
            </div>
            {this.renderEditScoreRows()}
            {this.renderConfirmModal()}
            {this.renderEditConfirmationModal()}
          </React.Fragment>
        ) : null}
      </div>
    );
  }
}

IndividualInterventionProgress.propTypes = {
  orgid: PropTypes.string,
  printAllIndividualInterventionGraphs: PropTypes.bool.isRequired,
  isFirstSection: PropTypes.bool,
  studentGroup: PropTypes.object,
  student: PropTypes.object
};

export default function IndividualInterventionProgressWithAppDataContext(props) {
  const { orgid } = useContext(AppDataContext);
  return <IndividualInterventionProgress {...props} orgid={orgid} />;
}

export function getDrillDownItemsPriorToIntervention(studentHistory, selectedGoalAssessmentId) {
  const itemsPriorToIntervention = [];
  let filteredItems = studentHistory;
  if (selectedGoalAssessmentId) {
    filteredItems = studentHistory.filter(h => h.benchmarkAssessmentId === selectedGoalAssessmentId);
  }
  filteredItems.forEach((item, index) => {
    if (item?.interventions.length && filteredItems[index + 1] && !filteredItems[index + 1]?.interventions.length) {
      itemsPriorToIntervention.push(filteredItems[index + 1]);
    }
  });
  return itemsPriorToIntervention;
}

export function getScoreAndDate(historyItem, type) {
  const assessmentId = type === "benchmark" ? historyItem.benchmarkAssessmentId : historyItem.assessmentId;
  const arm = historyItem.assessmentResultMeasures.find(am => am.assessmentId === assessmentId);
  const point = {
    x: historyItem.whenEnded.on,
    y: arm && arm.medianScore
  };
  if (historyItem.interventions && !historyItem.interventions.length) {
    point.name = `Drill-down - ${new Date(historyItem.whenEnded.on).toISOString().slice(0, 10)}`;
  }
  return [point];
}

function getRoiValue(scores) {
  const goalScores = scores.map(score => score[1]);
  const goalDates = scores.map(score => new Date(score[0]));
  return calculateRoI(goalDates, goalScores);
}

function updateGoalSkillsData(skillHistoryHierarchy = []) {
  // skillHistoryHierarchy and studentScores already sorted - prepared for graph so order will always be the same
  const allStudentScores = get(skillHistoryHierarchy, "skillHistoryHierarchy[0].studentScores[0]", []);
  const [firstGoalSkillTimestamp, firstGoalSkillScore] = allStudentScores;

  return skillHistoryHierarchy.map(goalSkill => {
    // eslint-disable-next-line no-param-reassign
    goalSkill.drillDownOnlyScore =
      typeof goalSkill.drillDownOnlyScore !== "undefined"
        ? goalSkill.drillDownOnlyScore
        : !goalSkill.interventions?.length;
    if (firstGoalSkillTimestamp && typeof firstGoalSkillScore !== "undefined") {
      const studentScores = goalSkill.studentScores ? goalSkill.studentScores : [];
      return {
        ...goalSkill,
        studentScores,
        roi: getRoiValue(studentScores, firstGoalSkillScore, firstGoalSkillTimestamp)
      };
    }
    return goalSkill;
  });
}

export function getSkillHistoryHierarchy(allActivities, goalSkillContainerSeed, currentSkill) {
  // activities get parsed into displayed scores
  const unsortedSkillHistoryHierarchy = allActivities.reduce((a, c) => {
    const goalSkillContainer = a.find(gsc => gsc.goalAssessmentId === c.benchmarkAssessmentId);
    if (!goalSkillContainer) {
      return a;
    }
    if (!goalSkillContainer.masteryTarget) {
      // eslint-disable-next-line prefer-destructuring
      goalSkillContainer.masteryTarget = c.benchmarkAssessmentTargets[1];
    }
    if (!goalSkillContainer.instructionalTarget) {
      // eslint-disable-next-line prefer-destructuring
      goalSkillContainer.instructionalTarget = c.benchmarkAssessmentTargets[0];
    }
    // need to add goalSkill?
    if (c.assessmentId === c.benchmarkAssessmentId || c.assessmentResultMeasures.length > 1) {
      if (!goalSkillContainer.studentScores) {
        goalSkillContainer.studentScores = [];
      }
      goalSkillContainer.studentScores.unshift(getScoreAndDate(c, "benchmark"));
      goalSkillContainer.studentScores = goalSkillContainer.studentScores.flat(1);
      goalSkillContainer.drillDownOnlyScore = !c.interventions.length;
    }
    // need to add intervention score?
    if (c.benchmarkAssessmentId !== c.assessmentId) {
      if (!goalSkillContainer.interventions) {
        goalSkillContainer.interventions = [];
      }
      let interventionContainer = goalSkillContainer.interventions.find(
        intervention => intervention.assessmentId === c.assessmentId
      );
      if (!interventionContainer) {
        const active = currentSkill && !currentSkill.whenEnded && currentSkill.assessmentId === c.assessmentId;
        interventionContainer = {
          active,
          complete: !active,
          drillDownOnlyScore: !c.interventions.length,
          assessmentId: c.assessmentId,
          masteryTarget: c.assessmentTargets[1],
          instructionalTarget: c.assessmentTargets[0],
          assessmentName: c.assessmentName
        };
        goalSkillContainer.interventions.unshift(interventionContainer);
      }
      if (!interventionContainer.studentScores) {
        interventionContainer.studentScores = [];
      }
      interventionContainer.studentScores.unshift(getScoreAndDate(c));
      interventionContainer.studentScores = interventionContainer.studentScores.flat(1);
      interventionContainer.drillDownOnlyScore = interventionContainer.drillDownOnlyScore && !c.interventions.length;
    }
    return a;
  }, goalSkillContainerSeed);
  return unsortedSkillHistoryHierarchy.sort((a, b) => (a.sortOrder < b.sortOrder ? -1 : 1));
}

export function getFoundationalSkillData({
  student,
  benchmarkPeriodId,
  rootRuleAssessments,
  screeningAssessmentIds,
  groupBenchmarkScores = []
}) {
  // Get All activities to process
  let allActivities = JSON.parse(JSON.stringify(student.history || []));
  allActivities = allActivities.filter(
    activity => activity.type !== "benchmark" && activity.benchmarkPeriodId === benchmarkPeriodId
  );
  // Make the skeleton structures for the goalSkills
  const goalSkillContainerSeed = rootRuleAssessments
    .filter(rule => screeningAssessmentIds.includes(rule.attributeValues.assessmentId))
    .map(rule => {
      const assessmentName = get(Assessments.findOne(rule.attributeValues.assessmentId), "name", rule.name) || "N/A";
      const active =
        student.currentSkill &&
        !student.currentSkill.whenEnded &&
        student.currentSkill.benchmarkAssessmentId === rule.attributeValues.assessmentId;
      const groupBenchmarkRuleScores = groupBenchmarkScores.find(
        item => item.assessmentId === rule.attributeValues.assessmentId
      );
      const hasCompletedSkillWithIndividualInterventionOrDrillDown = allActivities.some(
        h => h.benchmarkAssessmentId === rule.attributeValues.assessmentId
      );
      const hasCompletedFirstSkillWithScreening =
        screeningAssessmentIds?.[0] === rule.attributeValues.assessmentId &&
        groupBenchmarkRuleScores?.studentResults?.some(
          result => result.studentId === student._id && result.meetsTarget
        );
      const skillData = {
        goalAssessmentId: rule.attributeValues.assessmentId,
        benchmarkPeriodId: translateBenchmarkPeriod(rule.attributeValues.benchmarkPeriod).id,
        assessmentName,
        active,
        complete:
          (hasCompletedSkillWithIndividualInterventionOrDrillDown || hasCompletedFirstSkillWithScreening) && !active,
        sortOrder: screeningAssessmentIds.indexOf(rule.attributeValues.assessmentId)
      };
      if (groupBenchmarkRuleScores) {
        const [instructionalTarget, masteryTarget] = groupBenchmarkRuleScores.targetScores;
        return { ...skillData, instructionalTarget, masteryTarget };
      }
      return skillData;
    });
  return { allActivities, goalSkillContainerSeed };
}

export function getActiveBMPeriods(studentHistory = []) {
  return studentHistory.reduceRight((a, c) => {
    if (c.benchmarkPeriodId && a.indexOf(c.benchmarkPeriodId) < 0) {
      a.push(c.benchmarkPeriodId);
    }
    return a;
  }, []);
}

function getHistoryHierarchy(student, studentGroup, activeBMPeriodIds) {
  let skillHistoryHierarchy = [];
  // Build the final skillHistoryHierarchy by looping through each of the periods
  // in which the student was active in individual interventions
  const groupBenchmarkScores = [];
  const groupBenchmarkHistory = studentGroup.history
    ? studentGroup.history.filter(
        item => item.type === "benchmark" && activeBMPeriodIds.includes(item.benchmarkPeriodId)
      )
    : [];
  if (groupBenchmarkHistory.length) {
    groupBenchmarkHistory.map(item => groupBenchmarkScores.push(...item.assessmentResultMeasures));
  }

  activeBMPeriodIds.forEach(activeBMPeriodId => {
    const rootRuleAssessments = Rules.find({
      "attributeValues.grade": studentGroup.grade,
      "attributeValues.benchmarkPeriod": translateBenchmarkPeriod(activeBMPeriodId).label
    })
      .fetch()
      .filter(rule => rule._id === rule.rootRuleId);
    // Get the screening Assignments so that we can order the Goal Skills properly
    const screeningAssessmentIds = ScreeningAssignments.findOne({
      grade: student.grade,
      benchmarkPeriodId: activeBMPeriodId
    });
    const { allActivities, goalSkillContainerSeed } = getFoundationalSkillData({
      student,
      benchmarkPeriodId: activeBMPeriodId,
      rootRuleAssessments,
      screeningAssessmentIds: (screeningAssessmentIds && screeningAssessmentIds.assessmentIds) || [],
      groupBenchmarkScores
    });

    const hasCorrectSkillHistory = goalSkillContainerSeed.some(gs => gs.active || gs.complete);
    if (hasCorrectSkillHistory) {
      let newSkillHistoryHierarchy = getSkillHistoryHierarchy(
        allActivities,
        goalSkillContainerSeed,
        student.currentSkill
      );
      newSkillHistoryHierarchy = updateGoalSkillsData(newSkillHistoryHierarchy);
      skillHistoryHierarchy = [...skillHistoryHierarchy, ...newSkillHistoryHierarchy];
    }
  });
  return skillHistoryHierarchy;
}

function isScoreValid(score) {
  return parseInt(score) >= 0;
}
