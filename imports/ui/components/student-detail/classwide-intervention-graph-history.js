import React, { Component } from "react";
import { Loading } from "../loading.jsx";
import { Rules } from "/imports/api/rules/rules";
import ClasswidePMChart from "./student-detail-PM-Chart.jsx";
import {
  calculateClasswideROI,
  getPageBreakClassForEverySecondElementByIndex,
  MAX_SKILLS_FROM_NEXT_GRADE
} from "/imports/api/utilities/utilities";
import getSkillHistory from "./getSkillHistory";
import GraphTitle from "./GraphTitle";
import { ClassContext } from "../../pages/classContext";
import { isOnPrintPage, useContextOrProps } from "../../utilities";

export default class ClasswideInterventionGraphHistory extends Component {
  static contextType = ClassContext;

  constructor(props, context) {
    super(props, context);
    const student = useContextOrProps({ componentInstance: this, property: "student", verificationGetPath: "_id" });
    const students = useContextOrProps({ componentInstance: this, property: "students", verificationGetPath: "_id" });
    const studentGroup = useContextOrProps({
      componentInstance: this,
      property: "studentGroup",
      verificationGetPath: "_id"
    });

    this.isPrinting = isOnPrintPage();

    this.state = {
      classwideROI: calculateClasswideROI(studentGroup),
      studentROI: student ? calculateClasswideROI(studentGroup, student) : null,
      skillList: [],
      student,
      students,
      studentGroup,
      loading: true
    };
  }

  async componentDidMount() {
    await this.loadSkillData();
  }

  async loadSkillData() {
    const { students, studentGroup } = this.state;

    try {
      const additionalGradeForRules = studentGroup.grade === "K" ? "01" : null;
      const gradeLevelRules = Rules.findOne({ grade: studentGroup.grade });
      const additionalGradeLevelRules = additionalGradeForRules
        ? Rules.findOne({ grade: additionalGradeForRules })
        : undefined;

      const studentGroupHistory = JSON.parse(JSON.stringify(studentGroup.history));
      const additionalStudentGroupHistory = JSON.parse(JSON.stringify(studentGroup.additionalHistory || []));

      const defaultSkillList = await getSkillHistory(studentGroupHistory, gradeLevelRules, studentGroup, students);
      const isAdditionalSkillList = true;

      const additionalSkillListFull = await getSkillHistory(
        additionalStudentGroupHistory,
        additionalGradeLevelRules,
        studentGroup,
        students,
        undefined,
        isAdditionalSkillList
      );

      const additionalSkillList = (additionalSkillListFull || []).slice(0, MAX_SKILLS_FROM_NEXT_GRADE);
      const skillList = [...(defaultSkillList || []), ...(additionalSkillList || [])];

      this.setState({ skillList, loading: false });
    } catch (error) {
      console.error("Error loading skill data:", error);
      this.setState({ loading: false });
    }
  }

  getSkillsWithScores = () => {
    const skillsWithScores = [];
    let currentSkillIndex = 0;
    this.state.skillList.forEach(skill => {
      if (this.wasSkillPracticed(currentSkillIndex)) {
        skillsWithScores.push(skill);
      }
      currentSkillIndex += 1;
    });
    return skillsWithScores;
  };

  wasSkillPracticed(lastPracticedSkillIndex) {
    return (
      this.state.skillList[lastPracticedSkillIndex].allStudentsScores.length ||
      this.state.skillList[lastPracticedSkillIndex].studentScores.length
    );
  }

  render() {
    if (this.state.loading) {
      return <Loading />;
    }

    const chartOptions = {
      chartType: "line",
      title: "",
      height: 400,
      xAxisTitle: "",
      yAxisTitle: "Score",
      marginTop: 50,
      marginRight: 175
    };
    if (this.state.skillList && this.state.skillList.length > 0) {
      let name = "";
      let studentId = "";
      const { student } = this.state;
      if (student) {
        name = `${student.identity.name.firstName} ${student.identity.name.lastName}`;
        studentId = student._id;
      }
      return (
        <div data-testid="classwide-intervention-progress-section">
          <h3>Classwide Intervention Progress</h3>
          {this.getSkillsWithScores().map((skill, index) => (
            <section
              key={`${skill.id}_${index}`}
              className={getPageBreakClassForEverySecondElementByIndex(index, true)}
            >
              <div className="col-md-9 print-clear">
                <GraphTitle skill={skill} classwideROI={this.state.classwideROI} studentROI={this.state.studentROI} />
                <ClasswidePMChart
                  key={`${skill.id}_${studentId}`}
                  scores={skill}
                  chartId={`classwide_PM_CHART_${skill.id}_${studentId}`}
                  type="chart"
                  options={chartOptions}
                  pmName="Progress Monitoring Scores"
                  studentName={name}
                  shouldShowStudentsScores={this.isPrinting}
                  showSelectedScores={this.showSelectedScores}
                  selectedScoreDate={this.state.selectedDate}
                  scoresClickable={true}
                  studentGroup={this.state.studentGroup}
                />
              </div>
            </section>
          ))}
        </div>
      );
    }
    return <Loading />;
  }
}
