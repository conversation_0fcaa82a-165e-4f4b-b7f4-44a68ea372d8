import React, { useContext } from "react";
import PropTypes from "prop-types";
import { get, uniqBy } from "lodash";

import MissingScoreText from "./missingScoreText";
import * as utils from "/imports/api/utilities/utilities";
import { ClassContext } from "../../pages/classContext";
import { IndividualScreeningChart } from "./individual-screening-chart";

const ScreeningResultsCharts = props => {
  const student = get(useContext(ClassContext), "student") || props.student;
  const studentId = student._id;
  const studentName = student.identity.name.firstName;

  const renderScreeningResults = () => {
    if (props.classwideBenchmarkScores?.length > 0) {
      return uniqBy(props.classwideBenchmarkScores, "benchmarkPeriodId").map((bm, index) => {
        let chart;
        let groupName;
        const translatedBenchmarkPeriod = utils.translateBenchmarkPeriod(bm.benchmarkPeriodId);
        const benchmarkWindow = props.benchmarkWindows.find(bmw => bmw.benchmarkPeriodId === bm.benchmarkPeriodId);
        const wasStudentEnrolled = bm && bm.enrolledStudentIds.includes(studentId);
        if (wasStudentEnrolled) {
          const measureScores =
            bm &&
            bm.assessmentResultMeasures.map(arm => {
              const studentResult = arm.studentResults.find(sr => sr.studentId === studentId);
              return studentResult ? parseInt(studentResult.score) : null;
            });

          const finalClasswideInterventionScores =
            benchmarkWindow &&
            bm &&
            bm.assessmentResultMeasures.map(benchmarkARM => {
              const correspondingClasswideResult =
                props.studentGroupHistory &&
                props.studentGroupHistory.find(sgh => {
                  const correspondingAssessmentResultMeasure = sgh.assessmentResultMeasures.find(
                    classwideARM => classwideARM.assessmentId === benchmarkARM.assessmentId
                  );
                  return sgh.type === "classwide" && correspondingAssessmentResultMeasure;
                });
              if (!correspondingClasswideResult) {
                return null;
              }

              const assessmentResultMeasure = correspondingClasswideResult.assessmentResultMeasures.find(
                classwideARM => classwideARM.assessmentId === benchmarkARM.assessmentId
              );

              const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);
              return studentResult ? parseInt(studentResult.score) : null;
            });

          const finalIndividualInterventionScores =
            benchmarkWindow &&
            bm &&
            bm.assessmentResultMeasures.map(benchmarkARM => {
              const correspondingIndividualResult =
                props.studentHistory &&
                props.studentHistory.find(sh => {
                  const correspondingAssessmentResultMeasure = sh.assessmentResultMeasures.find(
                    individualARM => individualARM.assessmentId === benchmarkARM.assessmentId
                  );
                  return sh.type === "individual" && correspondingAssessmentResultMeasure;
                });

              if (!correspondingIndividualResult) {
                return null;
              }

              const assessmentResultMeasure = correspondingIndividualResult.assessmentResultMeasures.find(
                individualARM => individualARM.assessmentId === benchmarkARM.assessmentId
              );

              const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);
              return studentResult ? parseInt(studentResult.score) : null;
            });
          const chartSeries = [
            {
              name: `${translatedBenchmarkPeriod.title} Screening`,
              data: measureScores,
              color: translatedBenchmarkPeriod.color
            },
            {
              name: "Final Classwide Intervention Score",
              data: finalClasswideInterventionScores,
              color: "#800000"
            },
            {
              name: "Final Individual Intervention Score",
              data: finalIndividualInterventionScores,
              color: "#1A4261"
            }
          ];
          // eslint-disable-next-line no-restricted-globals
          const hasValidScores = measureScores.filter(s => s !== null && s !== undefined && !isNaN(s)).length > 0; // we do not want to filter out 0
          chart = hasValidScores && (
            <IndividualScreeningChart
              bmScores={bm}
              chartId={`${bm.assessmentResultId}_${studentId}`}
              measureScores={chartSeries}
            />
          );
        } else {
          const screeningResultFromOtherGroup = props.otherScreeningResults.find(
            bms => bms.benchmarkPeriodId === bm.benchmarkPeriodId
          );
          if (screeningResultFromOtherGroup) {
            const otherGroup = props.otherStudentGroups.find(
              sg => sg._id === screeningResultFromOtherGroup.studentGroupId
            );
            groupName = otherGroup ? otherGroup.name : "/group no longer available/";
          }
        }

        const screeningPeriodText = `${translatedBenchmarkPeriod.title} ${utils.getFormattedSchoolYear(
          props.schoolYear
        )}`;
        const screeningResultsContainerClassName = `${
          index % 2 === 0 && index !== 0 && props.classwideBenchmarkScores.length > 2 ? "page-break-before" : ""
        }`;
        return (
          <div key={`screening_${bm.assessmentResultId}_${index}`} className={screeningResultsContainerClassName}>
            <h4>
              {screeningPeriodText} Results
              {!chart && (
                <MissingScoreText
                  wasStudentEnrolled={wasStudentEnrolled}
                  studentName={studentName}
                  groupName={groupName}
                />
              )}
            </h4>
            {chart}
          </div>
        );
      });
    }

    return <div className="alert alert-info text-center">No data found for this student</div>;
  };

  return (
    <div id="screening-results-charts" data-testid="screening-results-charts">
      <h3 data-testid="screeningResults">Screening Results</h3>
      {renderScreeningResults()}
    </div>
  );
};

ScreeningResultsCharts.propTypes = {
  classwideBenchmarkScores: PropTypes.array,
  schoolYear: PropTypes.number,
  otherScreeningResults: PropTypes.array,
  otherStudentGroups: PropTypes.array,
  studentHistory: PropTypes.array,
  studentGroupHistory: PropTypes.array,
  benchmarkWindows: PropTypes.array,
  student: PropTypes.any
};

export default ScreeningResultsCharts;
