import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import { ListGroup } from "react-bootstrap";
import { groupBy } from "lodash";

import { Sites } from "/imports/api/sites/sites";
import { Loading } from "./loading.jsx";
import { renderZendeskWidget, sortByGradeAndName } from "/imports/api/utilities/utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { areSubscriptionsLoading } from "../utilities";
import { getUserRoles } from "../pages/data-admin/utilities";

class SideNav extends Component {
  state = {
    shouldDisplayZendeskWidget: false
  };

  // eslint-disable-next-line camelcase
  UNSAFE_componentWillMount() {
    const userRole = getUserRoles();
    const isAllowedToFetchZendeskFlag = userRole.includes("teacher") || userRole.includes("admin");
    if (!isAllowedToFetchZendeskFlag) {
      return;
    }
    Meteor.call("Settings:getZendeskWidgetFlag", this.props.siteId, (err, resp) => {
      if (!err) {
        this.setState({ shouldDisplayZendeskWidget: resp });
      }
    });
  }

  isActive = groupId => {
    return this.props.match.params.studentGroupId === groupId ? " active" : "";
  };

  getNavHeading = (headerClass, grade) => {
    const title = `Grade: ${grade}`;
    const headingClass = `${headerClass} list-group-item`;
    return <span className={headingClass}>{title}</span>;
  };

  getSideNavItems = groups =>
    groups.map(group => (
      <Link
        className={`${this.isActive(group._id)} list-group-item`}
        key={group._id}
        to={`/site/${group.siteId}/student-groups/${group._id}`}
      >
        {group.name}
      </Link>
    ));

  renderStudentGroupList = () => {
    const studentGroupList = groupBy(this.props.studentGroups, "grade");
    const entries = Object.entries(studentGroupList);

    entries.sort(([, [classA]], [, [classB]]) => sortByGradeAndName(classA, classB));

    return entries.map(([grade, groups]) => {
      const headerClass = "list-group-header-static";
      return (
        <div key={grade}>
          {this.getNavHeading(headerClass, grade)}
          {this.getSideNavItems(groups)}
        </div>
      );
    });
  };

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    const { studentGroups, isAdmin, isSupport, isUniversalCoach, siteName, organizationName } = this.props;
    let backToDashboardButton = null;

    if (isAdmin || isSupport || isUniversalCoach) {
      const firstGroup = studentGroups[0];
      const backToDashboardLink = `/school-overview/${firstGroup.orgid}/all/${firstGroup.siteId}`;
      backToDashboardButton = (
        <Link className="lkBck2Dashboard list-group-item" to={backToDashboardLink} key="adminLink">
          Back to Dashboard
        </Link>
      );
    }
    return (
      <aside className="side-nav">
        <div className="site-selector" data-testid="siteSelectorId">
          {siteName}
        </div>
        <ListGroup className="student-group-list">
          {backToDashboardButton}
          {this.renderStudentGroupList()}
        </ListGroup>
        {this.state.shouldDisplayZendeskWidget && renderZendeskWidget(organizationName, siteName)}
      </aside>
    );
  }
}

SideNav.propTypes = {
  loading: PropTypes.bool,
  studentGroups: PropTypes.array.isRequired,
  isAdmin: PropTypes.bool,
  isSupport: PropTypes.bool,
  isUniversalCoach: PropTypes.bool,
  organizationName: PropTypes.string,
  siteName: PropTypes.string,
  siteId: PropTypes.string,
  match: PropTypes.object
};

export default withTracker(({ studentGroups, isAdmin, isSupport, orgid, siteId }) => {
  const sitesHandle = Meteor.subscribe("Sites", orgid, siteId);
  const studentGroupHandle = Meteor.subscribe(
    "StudentGroupsAssociatedWithUser",
    studentGroups?.[0]?.schoolYear,
    siteId,
    orgid
  );
  const loading = areSubscriptionsLoading(sitesHandle, studentGroupHandle);
  let siteName = "Loading...";
  const organization = Organizations.findOne(orgid, { fields: { name: 1 } });
  const organizationName = (organization && organization.name) || "";
  let groupsThatCanBeLinkedTo = [];
  if (!loading) {
    const site = Sites.findOne({ _id: siteId });
    const extendedStudentGroups = StudentGroups.find(
      { grade: { $exists: true } },
      { fields: { grade: 1, orgid: 1, siteId: 1, name: 1 } }
    ).fetch();
    siteName = site && site.name;
    groupsThatCanBeLinkedTo = extendedStudentGroups.sort(sortByGradeAndName);
  }

  return {
    loading,
    studentGroups: groupsThatCanBeLinkedTo,
    isAdmin,
    isSupport,
    organizationName,
    siteName
  };
})(withRouter(SideNav));
