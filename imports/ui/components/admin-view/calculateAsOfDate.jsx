import { Meteor } from "meteor/meteor";
import moment from "moment";
import React, { useState, useEffect } from "react";
import DatePicker from "react-date-picker";
import PropTypes from "prop-types";
import get from "lodash/get";
import { isSupportUser } from "/imports/api/roles/methods";

export function getStartDateOfInterventions({ history = [], currentSkill, type, benchmarkPeriodId }) {
  let oldestIntervention;
  const historyCopy = history && [...history];
  oldestIntervention =
    history && historyCopy.reverse().find(h => h.type === type && h.benchmarkPeriodId === benchmarkPeriodId);
  if (!oldestIntervention) {
    oldestIntervention = currentSkill;
  }
  return oldestIntervention?.whenStarted?.date;
}

function CalculateAsOfDate(props) {
  const [isSupport, setIsSupport] = useState(false);

  useEffect(() => {
    let isMounted = true;
    const checkSupportUser = async () => {
      try {
        const supportUser = await isSupportUser();
        if (isMounted) {
          setIsSupport(!!supportUser);
        }
      } catch (error) {
        console.error("Error checking support user:", error);
        if (isMounted) {
          setIsSupport(false);
        }
      }
    };
    checkSupportUser();

    return () => {
      isMounted = false;
    };
  }, []);

  const handleDateChange = (value, studentGroupId, studentId) => {
    if (!value) return;
    const calculateStatsAsOfDate = new Date(moment(value));
    let methodName;
    let entityId;
    if (props.type === "classwide") {
      methodName = "StudentGroups:updateCalculateAsOfDate";
      entityId = studentGroupId;
    } else if (props.type === "individual") {
      methodName = "Students:updateCalculateAsOfDate";
      entityId = studentId;
    }
    Meteor.call(
      methodName,
      {
        orgid: props.group.orgid,
        siteId: props.group.siteId,
        entityId,
        calculateStatsAsOfDate,
        benchmarkPeriodId: props.benchmarkPeriodId
      },
      err => {
        if (!err) {
          props.updateStats(calculateStatsAsOfDate);
        }
      }
    );
  };

  const getDatePickerDefaultValues = () => {
    const { type, benchmarkPeriodId } = props;
    const currentDate = new Date();
    let entity;
    let currentSkill;
    let statsDatePath;
    if (type === "individual" && props.student) {
      entity = props.student;
      currentSkill = entity.currentSkill;
      statsDatePath = `individualStatsAsOfDate[${benchmarkPeriodId}]`;
    } else if (type === "classwide") {
      entity = props.group;
      currentSkill = entity.currentClasswideSkill;
      statsDatePath = "classwideStatsAsOfDate";
    } else {
      return { asOfDate: null, startDateOfInterventions: null };
    }
    const startDateOfInterventions =
      getStartDateOfInterventions({
        history: entity.history,
        currentSkill,
        type,
        benchmarkPeriodId
      }) || currentDate;
    const asOfDate = get(entity, statsDatePath);
    return {
      asOfDate: asOfDate?.toISOString(),
      startDateOfInterventions: startDateOfInterventions.toISOString()
    };
  };

  const studentId = props.student?._id;
  const { asOfDate, startDateOfInterventions } = getDatePickerDefaultValues();

  if (asOfDate || startDateOfInterventions) {
    const currentDate = new Date().toISOString();
    const summerBreakStartDate = moment(`15.05.${props.group.schoolYear}`, "DD.MM.YYYY hh:mm:ss").toDate();
    const minDate = startDateOfInterventions || currentDate;
    let maxDate =
      new Date().valueOf() > summerBreakStartDate.valueOf() ? summerBreakStartDate.toISOString() : currentDate;
    maxDate = minDate.valueOf() > maxDate.valueOf() ? minDate : maxDate;

    const opts = {
      ...(isSupport ? { clearIcon: null, disableCalendar: true } : {})
    };

    return (
      <DatePicker
        id={props.group._id}
        value={new Date(asOfDate || startDateOfInterventions)}
        format={"MM/dd/y"}
        maxDate={new Date(maxDate)}
        minDate={new Date(minDate)}
        className={`custom-date-picker${isSupport ? " cursor-not-allowed" : ""}`}
        onChange={value => handleDateChange(value || startDateOfInterventions, props.group._id, studentId)}
        disabled={isSupport}
        {...opts}
      />
    );
  }
  return null;
}

CalculateAsOfDate.propTypes = {
  group: PropTypes.object,
  student: PropTypes.object,
  type: PropTypes.string,
  updateStats: PropTypes.func,
  benchmarkPeriodId: PropTypes.string
};

export default CalculateAsOfDate;
