import { Meteor } from "meteor/meteor";
import React, { useState, useEffect, useContext } from "react";
import PropTypes from "prop-types";

import { useTracker } from "meteor/react-meteor-data";
import { range } from "lodash";
import BenchmarkPeriodHelpers from "/imports/api/benchmarkPeriods/methods";
import { areSubscriptionsLoading, isOnPrintPage } from "/imports/ui/utilities";
import * as utils from "/imports/api/utilities/utilities";

// Data
import { BenchmarkPeriods } from "/imports/api/benchmarkPeriods/benchmarkPeriods";
import { Sites } from "/imports/api/sites/sites";

// Components
import GradeOverview from "./grade-overview.jsx";
import SchoolOverview from "./school-overview.jsx";
import { AppDataContext } from "/imports/ui/routing/AppDataContext";
import NewsBanner from "/imports/ui/components/dashboard/news-banner";
import Loading from "/imports/ui/components/loading";
import { getMeteorUserSync } from "/imports/api/utilities/utilities";

export const AdminOverview = ({
  loading,
  isPrinting,
  gradeId,
  bmPeriods,
  siteName,
  currentBMPeriod,
  orgid,
  siteId,
  screeningHidden,
  schoolYear
}) => {
  const [message, setMessage] = useState(null);
  const { schoolYear: contextSchoolYear, orgid: contextOrgid, siteId: contextSiteId } =
    useContext(AppDataContext) || {};

  const getActiveMessage = () => {
    Meteor.call("News:getActiveMessage", { siteId, orgid }, (err, result) => {
      if (!err) {
        setMessage(result);
      }
    });
  };

  useEffect(() => {
    getActiveMessage();
  }, [siteId, orgid]); // getActiveMessage is stable, no need to include in deps

  if (loading) {
    return (
      <div className="overviewContainer">
        <Loading />
      </div>
    );
  }

  const shouldDisplayNewsBanner = message;

  return (
    <div className={!isPrinting ? "overviewContainer" : null}>
      <div className={`news-admin-dashboard-offset ${shouldDisplayNewsBanner ? "withNewsBanner" : ""}`}>
        {shouldDisplayNewsBanner ? <NewsBanner message={message} /> : <div className="conNewsBannerPlaceholder" />}
      </div>
      {gradeId === "all" ? (
        <SchoolOverview
          bmPeriods={bmPeriods}
          siteName={siteName}
          currentBMPeriod={currentBMPeriod}
          orgid={orgid}
          siteId={siteId}
          isPrinting={isPrinting}
          screeningHidden={screeningHidden === "true"}
          schoolYear={schoolYear}
        />
      ) : (
        <GradeOverview
          grade={gradeId}
          bmPeriods={bmPeriods}
          siteName={siteName}
          currentBMPeriod={currentBMPeriod}
          schoolYear={contextSchoolYear || schoolYear}
          orgid={contextOrgid || orgid}
          siteId={contextSiteId || siteId}
        />
      )}
    </div>
  );
};

AdminOverview.propTypes = {
  gradeId: PropTypes.string,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  siteName: PropTypes.string,
  loading: PropTypes.bool,
  grades: PropTypes.array,
  studentGroups: PropTypes.array,
  bmPeriods: PropTypes.array,
  currentBMPeriod: PropTypes.object,
  isPrinting: PropTypes.bool,
  screeningHidden: PropTypes.string,
  schoolYear: PropTypes.number
};

export const AdminOverviewWithTracker = ({
  siteId,
  orgid,
  schoolYear: contextSchoolYear,
  updateAppDataContext,
  ...props
}) => {
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(contextSchoolYear);

  // Handle async school year resolution
  useEffect(() => {
    const resolveSchoolYear = async () => {
      const user = getMeteorUserSync();
      if (user && orgid) {
        const currentSchoolYear = await utils.getCurrentSchoolYear(user, orgid);
        const latestAvailableSchoolYear = await utils.getLatestAvailableSchoolYear(user, orgid);

        let schoolYear = contextSchoolYear || currentSchoolYear;
        const allSchoolYears = range(2017, latestAvailableSchoolYear + 1) || [contextSchoolYear];
        if (!allSchoolYears.includes(currentSchoolYear)) {
          allSchoolYears.push(currentSchoolYear);
        }
        const validSchoolYears = allSchoolYears.filter(year => year && year > 0);
        if (!validSchoolYears.includes(schoolYear)) {
          schoolYear = Math.max(...validSchoolYears);
        }
        if (schoolYear > currentSchoolYear) {
          schoolYear = currentSchoolYear;
        }

        if (contextSchoolYear !== schoolYear && updateAppDataContext) {
          updateAppDataContext({ schoolYear });
        }

        setResolvedSchoolYear(schoolYear);
      }
    };

    if (!contextSchoolYear || !resolvedSchoolYear) {
      resolveSchoolYear();
    } else {
      setResolvedSchoolYear(contextSchoolYear);
    }
  }, [orgid, contextSchoolYear, updateAppDataContext]);

  const trackerData = useTracker(() => {
    if (!resolvedSchoolYear) {
      return { loading: true };
    }

    // Subs for parent and child components
    const isPrinting = isOnPrintPage();
    Meteor.subscribe("Users", { siteId, orgid });
    const bmpSub = Meteor.subscribe("BenchmarkPeriods");
    const studentGroupHandle = Meteor.subscribe("StudentGroups:PerSite", orgid, siteId, resolvedSchoolYear);
    const arProjection = {
      studentGroupId: 1,
      type: 1,
      status: 1,
      benchmarkPeriodId: 1,
      grade: 1,
      classwideResults: 1,
      ruleResults: 1
    };
    const assResultsSub = Meteor.subscribe("AssessmentResults:ForSite", siteId, resolvedSchoolYear, arProjection);
    const gradesSub = Meteor.subscribe("GradesWithStudentGroupsInSite", siteId, resolvedSchoolYear);
    const sitesSub = Meteor.subscribe("Sites", orgid, siteId);
    const orgSub = Meteor.subscribe("Organizations", orgid);
    const loading = areSubscriptionsLoading(gradesSub, studentGroupHandle, assResultsSub, bmpSub, orgSub, sitesSub);

    let bmPeriods;
    let currentBMPeriod;
    let siteName = "";

    if (!loading) {
      bmPeriods = BenchmarkPeriods.find().fetch();
      if (siteId) {
        const site = Sites.findOne({ _id: siteId });
        if (site) {
          siteName = site.name;
        }
      }
      currentBMPeriod = BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
    }

    return {
      loading,
      bmPeriods,
      siteName,
      currentBMPeriod,
      orgid,
      siteId,
      isPrinting,
      schoolYear: resolvedSchoolYear
    };
  }, [resolvedSchoolYear, siteId, orgid]);

  return <AdminOverview {...props} {...trackerData} />;
};

AdminOverviewWithTracker.propTypes = {
  siteId: PropTypes.string,
  orgid: PropTypes.string,
  schoolYear: PropTypes.number,
  updateAppDataContext: PropTypes.func
};

export const AdminOverviewWithContext = props => {
  const { schoolYear, updateAppDataContext } = useContext(AppDataContext) || {};

  return <AdminOverviewWithTracker {...props} schoolYear={schoolYear} updateAppDataContext={updateAppDataContext} />;
};

export { AdminOverview as PureAdminOverview };
export default AdminOverviewWithTracker;
