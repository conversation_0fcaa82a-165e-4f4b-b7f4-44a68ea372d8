import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import Dropzone from "react-dropzone";
import Papa from "papaparse";
import { withRouter } from "react-router-dom";
import difference from "lodash/difference";
import union from "lodash/union";
import uniq from "lodash/uniq";
import each from "lodash/each";
import Alert from "react-s-alert";
import { Loading } from "../../components/loading.jsx";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import PageHeader from "../../components/page-header.jsx";
import { Organizations } from "/imports/api/organizations/organizations";
import { Sites } from "/imports/api/sites/sites";
import { areSubscriptionsLoading, requiredAssessmentScoreUploadFields } from "../../utilities";
import { assessmentScoresUploadRequiredFieldsExample, getCSV } from "./upload/file-upload-utils";
import FileUploadErrors from "./upload/file-upload-errors";
import { normalizeAssessmentScoreItem } from "/imports/api/assessmentScoresUpload/normalizeAssessmentScoresItem";
import { AssessmentScoresUpload } from "/imports/api/assessmentScoresUpload/assessmentScoresUpload";
import AssessmentScoreUploadHelpers from "/imports/api/assessmentScoresUpload/helpers";
import { AssessmentScoresUploadGuidelines } from "./upload/assessment-scores-upload-guidelines";
import { getMeteorUser } from "/imports/api/utilities/utilities";

class AssessmentScoreUpload extends Component {
  constructor(props) {
    super(props);

    this.state = {
      data: [],
      parseMeta: {},
      fileName: "",
      errors: [],
      hasUploadFailed: false,
      subtotals: {},
      fileDropped: false,
      shouldAddToExistingData: false
    };
  }

  handleAssessmentScoreUploadSubmit = data => {
    const { orgid, history } = this.props;
    Meteor.call("AssessmentScoresUpload:insert", { data, orgid }, err => {
      if (err) {
        Alert.error(err.message);
        decWaitingOn();
      } else {
        decWaitingOn();
        history.push(`/data-admin/dashboard/${orgid}`);
      }
    });
    incWaitingOn(1, "Inserting into the db!");
  };

  onDrop = files => {
    this.fileName = files[0]?.name || "No Name Found";
    incWaitingOn(1, "Parsing CSV!");
    Papa.parse(files[0], {
      header: true,
      skipEmptyLines: true,
      complete: this.parsingComplete
    });
  };

  parsingComplete = results => {
    const assessmentResultsData = { ...results };

    decWaitingOn();
    if (!(results.data && results.data.length > 0)) {
      this.setState({ fileDropped: true });
      return;
    }
    const missingFields = difference(requiredAssessmentScoreUploadFields, results.meta.fields);
    const unsupportedFields = difference(results.meta.fields, [...requiredAssessmentScoreUploadFields]);

    if (missingFields.length) {
      this.setInvalidFieldsError(missingFields);
      return;
    }
    if (unsupportedFields.length) {
      this.setInvalidFieldsError(unsupportedFields, true);
      return;
    }
    const assessmentResultsDataSchoolYears = uniq(
      assessmentResultsData.data.filter(d => !!d.AssessmentYear).map(d => parseInt(d.AssessmentYear))
    ).sort();
    if (assessmentResultsDataSchoolYears.length > 1) {
      const errorText = [
        `Your CSV file contains different values: ${assessmentResultsDataSchoolYears.join(
          ", "
        )} for the AssessmentYear field.\n\nPlease use the same year for the AssessmentYear field in the provided file.`
      ];
      this.setState({ fileDropped: true, errors: errorText });
      return;
    }

    let errors = results.errors ? results.errors : [];

    this.validateAndSetData(assessmentResultsData.data, validationErrors => {
      errors = union(results.errors, validationErrors);
      this.setState({
        parseMeta: results.meta,
        data: assessmentResultsData.data,
        fileName: this.fileName,
        errors,
        fileDropped: true
      });
    });
  };

  setInvalidFieldsError(invalidFields, unsupportedFields = false) {
    const errorText = unsupportedFields
      ? "Unsupported fields found:\n"
      : "Your CSV file is missing the following fields:\n";
    const errors = [
      `${errorText}${invalidFields.join(
        "\n"
      )}\n\nPlease make sure you are using the latest CSV Template for Assessment Score File Uploads.`
    ];
    this.setState({ errors, fileDropped: true, fileName: this.fileName });
  }

  normalizeData = (item, errors) => normalizeAssessmentScoreItem(item, errors);

  setSubtotalsAndCallbackWithErrors({ numStudents, numStateScores, numDistrictScores, errors, callback }) {
    decWaitingOn();
    if (!errors.length) {
      this.setState({
        subtotals: {
          students: numStudents,
          stateScores: numStateScores,
          districtScores: numDistrictScores
        }
      });
    }
    callback(errors);
  }

  validateAndSetData(data, cb) {
    const errors = [];
    let numStudents = 0;
    let numStateScores = 0;
    let numDistrictScores = 0;
    const processAssessmentScoreItemsFunctions = [];
    const schema = AssessmentScoresUpload.schemaAssessmentScore;
    incWaitingOn(1, "Validating!");

    const processAssessmentScoreItem = (datum, index) => {
      return new Promise(resolve => {
        const assessmentScoresUploadItem = AssessmentScoreUploadHelpers.createFromCSVDataRow(datum);
        assessmentScoresUploadItem.data = this.normalizeData(assessmentScoresUploadItem.data, errors);
        try {
          schema.validate(assessmentScoresUploadItem.data);
        } catch (e) {
          e.details.forEach(error => {
            const message = schema.messageForError(error);
            errors.push(`${message}. Please see row: ${index + 2}.`);
          });

          resolve(false);
        }

        numStudents += 1;
        if (assessmentScoresUploadItem.data.stateAssessmentName) {
          numStateScores += 1;
        }

        if (assessmentScoresUploadItem.data.districtAssessmentName) {
          numDistrictScores += 1;
        }

        resolve(true);
      });
    };

    each(data, (datum, index) => {
      processAssessmentScoreItemsFunctions.push(processAssessmentScoreItem(datum, index));
    });

    const schoolYear = parseInt(data[0].AssessmentYear);

    Promise.all(processAssessmentScoreItemsFunctions).then(() => {
      const studentLocalStateIds = data.map(student => ({
        localId: student.StudentLocalID,
        stateId: student.StudentStateID,
        firstName: student.StudentFirstName,
        lastName: student.StudentLastName
      }));

      Meteor.call(
        "AssessmentScoresUpload:CheckIfLocalAndStateIdsExist",
        studentLocalStateIds,
        this.props.orgid,
        schoolYear,
        (err, res) => {
          if (!err) {
            res.forEach(missingIdError => {
              errors.push(missingIdError);
            });
            this.setSubtotalsAndCallbackWithErrors({
              numStudents,
              numStateScores,
              numDistrictScores,
              errors,
              callback: cb
            });
          }
        }
      );
    });
  }

  handleCancel = () => {
    this.setState({ data: [], fileName: "", fileDropped: false });
    this.goBackToSchool();
  };

  handleSubmit = event => {
    event.preventDefault();
    const { parseMeta, data, shouldAddToExistingData } = this.state;
    this.handleAssessmentScoreUploadSubmit({ parseMeta, data, shouldAddToExistingData });
  };

  goBackToSchool = () => {
    this.props.history.push(`/data-admin/dashboard/${this.props.orgid}`);
  };

  getSubtotalColumn = (subtotal, label, idPrefix) => (
    <div className="col-sm-4">
      <div className="card card-header text-center">
        <h2 id={`${idPrefix}Subtotal`} data-testid={`${idPrefix}Subtotal`}>
          {subtotal}
        </h2>
        <p>{label}</p>
      </div>
    </div>
  );

  displayFileUploadResults() {
    const {
      errors,
      hasUploadFailed,
      fileName,
      data,
      subtotals: { students, stateScores, districtScores }
    } = this.state;
    const { orgid } = this.props;

    if (errors.length > 0 || hasUploadFailed) {
      return (
        <FileUploadErrors
          validationErrors={errors}
          hasUploadFailed={hasUploadFailed}
          fileName={fileName}
          orgid={orgid}
        />
      );
    }
    if (data.length < 1) {
      return this.displayNoDataNotice();
    }
    return (
      <div>
        <h2 id="congratulationsHeader" className="w7">
          Congratulations!
        </h2>
        <p>
          {" "}
          Nice job on the upload. The data looks good to us! Below is a quick overview of what we are going to insert
          into the app.
        </p>
        <div className="w7">
          <label
            className="cursor-pointer"
            onClick={() => this.setState(state => ({ shouldAddToExistingData: !state.shouldAddToExistingData }))}
          >
            <div className="d-flex">
              <span className="fa-stack m-r-5">
                <i className="fa fa-square-o fa-stack-2x" data-testid={"unchecked_data_replace"} />
                {this.state.shouldAddToExistingData ? (
                  <i className="text-success fa fa-check fa-stack-1x" data-testid={"checked_data_replace"} />
                ) : null}
              </span>
              <div>
                This upload will delete and replace prior uploads. If you want to add this data to your previous uploads
                click the box here.
              </div>
            </div>
          </label>
        </div>
        <div className="row">
          {this.getSubtotalColumn(students, "Students", "student")}
          {this.getSubtotalColumn(stateScores, "State Scores", "state-scores")}
          {this.getSubtotalColumn(districtScores, "District Scores", "district-scores")}
        </div>

        <fieldset className="form-group mt-3">
          <div className="row justify-content-between">
            <div className="col-4">
              <button type="button" className="btn btn-danger form-control" onClick={this.handleCancel}>
                Cancel
              </button>
            </div>
            <div className="col-4">
              <button type="submit" className="btn btn-primary form-control" onClick={this.handleSubmit}>
                Finalize Upload
              </button>
            </div>
          </div>
        </fieldset>
      </div>
    );
  }

  displayNoDataNotice = () => (
    <div>
      <h2 className="w7">Sorry</h2>
      <p className="text-danger"> The upload tool found no parsable data.</p>
    </div>
  );

  render() {
    const { loading, isOrgFound, orgName } = this.props;
    if (loading) {
      return <Loading inline message="Loading..." />;
    }

    if (!isOrgFound) {
      return (
        <div className="conFullScreen">
          <PageHeader title="Organization not found" />
        </div>
      );
    }

    const { fileName, fileDropped } = this.state;

    return (
      <div className="conFullScreen">
        <PageHeader title={orgName} description="Upload Your Data" />
        <div className="container animated fadeIn">
          <div className="row">
            <div className="col-3">
              <div className="card-box">
                <div className="row">
                  <div className="col-12" data-testid="roster-upload-dropzone-testid">
                    <Dropzone
                      id="dz1"
                      className="alert alert-success text-xs-center"
                      onDrop={this.onDrop}
                      disablePreview
                    >
                      <div className="text-center">
                        {fileName ? (
                          <h3 className="animated fadeIn drop-zone-file-name">{fileName}</h3>
                        ) : (
                          <div className="animated fadeIn">
                            <span>
                              Try dropping your file here,
                              <br />
                              or click to select a file to upload.
                            </span>
                          </div>
                        )}
                      </div>
                    </Dropzone>
                  </div>
                </div>
              </div>
              <p htmlFor="exampleSelect1">Would you like a helper file to know how to layout your data?</p>
              <a
                download="CSVHelperFile.csv"
                href={`data:application/octet-stream,${getCSV([assessmentScoresUploadRequiredFieldsExample])}`}
              >
                Download CSV Template for File Uploads
              </a>
              <br />
              <a href="/assets/Description of Variables_External Assessment Scores Fields.pdf" target="_blank">
                Description of variables
              </a>
            </div>
            <div id="resultsArea" className="col-9">
              <div className="animated fadeIn">
                {fileDropped ? this.displayFileUploadResults() : <AssessmentScoresUploadGuidelines />}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

AssessmentScoreUpload.propTypes = {
  importedRecords: PropTypes.array.isRequired,
  orgid: PropTypes.string,
  loading: PropTypes.bool,
  isOrgFound: PropTypes.bool,
  orgName: PropTypes.string,
  sites: PropTypes.array,
  history: PropTypes.object
};

export default withTracker(props => {
  const curUser = getMeteorUser();
  const orgid = props.orgid || curUser?.profile?.orgid;

  const organizationsHandler = Meteor.subscribe("Organizations", orgid);
  const usersHandler = Meteor.subscribe("Users", { orgid });
  const sitesHandler = Meteor.subscribe("Sites", orgid);

  let orgName = "";
  let isOrgFound = false;
  let sites = [];

  const importedRecords = [
    {
      name: "InitalUpload.csv",
      uploadDate: new Date()
    }
  ];

  const loading = areSubscriptionsLoading(organizationsHandler, usersHandler, sitesHandler);
  if (!loading) {
    const org = Organizations.findOne(orgid);
    sites = Sites.find().fetch();
    if (org) {
      isOrgFound = true;
      orgName = org.name || "";
    }
  }

  return { loading, isOrgFound, orgName, importedRecords, sites };
})(withRouter(AssessmentScoreUpload));
