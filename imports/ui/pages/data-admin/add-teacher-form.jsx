import React, { Component } from "react";
import PropTypes from "prop-types";
import { isEmailValid } from "/imports/api/utilities/utilities";

export default class AddTeacherForm extends Component {
  state = {
    errors: {
      lastName: null,
      firstName: null,
      localId: null,
      email: null
    }
  };

  onChange = field => e => {
    const { value } = e.target;
    this.props.onChange({ [field]: value });
  };

  updateIsFormValid = () => {
    this.props.onChange(
      {},
      Object.values(this.state.errors).every(e => e === "")
    );
  };

  setError = (field, error) => {
    this.setState(
      state => ({
        errors: {
          ...state.errors,
          [field]: error
        }
      }),
      () => {
        this.updateIsFormValid();
      }
    );
  };

  clearError = field => () => {
    this.setError(field, "");
  };

  validateLength = field => e => {
    const targetValue = e.target.value;
    if (!targetValue.trim().length) {
      this.setError(field, `${e.target.placeholder} cannot be empty`);
      return false;
    }
    this.clearError(field)();
    return true;
  };

  validateEmail = () => e => {
    const { email } = this.props.teacher;
    const emailField = "email";
    if (!this.validateLength(emailField)(e)) {
      return;
    }
    if (isEmailValid(email)) {
      Meteor.call("checkIfEmailAlreadyExists", email, this.props.orgid, (err, resp) => {
        if (resp) {
          const errorMessage = `Email already exists for ${resp.profile.name.last}, ${resp.profile.name.first}${
            resp.isArchived ? ", \n  Use the manage class tab to unarchive this teacher." : ""
          }`;
          this.setError(emailField, errorMessage);
        } else {
          this.clearError(emailField)();
        }
      });
    } else {
      this.setError(emailField, "Email is not valid");
    }
  };

  renderErrorMessage = field => {
    const error = this.state.errors[field];
    if (error && error.length) {
      return <span className="help-block help-block-override text-danger animated bounceIn">{error}</span>;
    }
    return null;
  };

  render() {
    const { teacher } = this.props;
    const firstName = "firstName";
    const lastName = "lastName";
    const localId = "localId";
    const email = "email";

    return (
      <div>
        <h4>Add Teacher</h4>
        <div className="row mb-1">
          <div className="col-sm-4">
            <small>Last Name</small>
            <br />
            <input
              onChange={this.onChange(lastName)}
              value={teacher.lastName}
              onFocus={this.clearError(lastName)}
              onBlur={this.validateLength(lastName)}
              className="form-control input-sm m-b-5"
              placeholder="Last name"
            />
            {this.renderErrorMessage(lastName)}
          </div>
          <div className="col-sm-3">
            <small>First Name</small>
            <br />
            <input
              onChange={this.onChange(firstName)}
              value={teacher.firstName}
              onFocus={this.clearError(firstName)}
              onBlur={this.validateLength(firstName)}
              className="form-control input-sm m-b-5"
              placeholder="First name"
            />
            {this.renderErrorMessage(firstName)}
          </div>
          <div className="col-sm-2">
            <small>Teacher ID</small>
            <br />
            <input
              onChange={this.onChange(localId)}
              value={teacher.localId}
              onBlur={this.validateLength(localId)}
              onFocus={this.clearError(localId)}
              className="form-control input-sm m-b-5"
              placeholder="Teacher ID"
            />
            {this.renderErrorMessage(localId)}
          </div>
          <div className="col-sm-3">
            <small>Email</small>
            <br />
            <input
              onChange={this.onChange(email)}
              value={teacher.email}
              onBlur={this.validateEmail()}
              onFocus={this.clearError(email)}
              className="form-control input-sm m-b-5"
              placeholder="Email"
            />
            {this.renderErrorMessage(email)}
          </div>
        </div>
      </div>
    );
  }
}

AddTeacherForm.propTypes = {
  teacher: PropTypes.object,
  onChange: PropTypes.func,
  orgid: PropTypes.string
};

AddTeacherForm.defaultProps = {
  onChange: () => {}
};
