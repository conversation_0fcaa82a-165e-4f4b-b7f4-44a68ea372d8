import { Meteor } from "meteor/meteor";
import { get, groupBy, keyBy, orderBy, uniqBy } from "lodash";
import moment from "moment";
import Alert from "react-s-alert";

import {
  getClassSectionIdForOneRoster,
  getGradeByGradeTranslation,
  getGradeFromDescriptor,
  getGradeLabel,
  getGradesFromOneRosterStudents,
  getGradeTranslationsByGrade,
  getMeteorUser,
  getSectionGradeMajority,
  mapExternalRosteringStudentGrades,
  normalizeExternalRosteringId
} from "/imports/api/utilities/utilities";
import { getExternalRosteringClassDetails } from "/imports/api/rostering/getExternalRosteringClassDetails";
import { getCurrentDate } from "/imports/api/helpers/getCurrentDate";

export async function fetchGradeLevelDescriptors({ orgid }) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid,
        resource: "GradeLevelDescriptors",
        useComposites: false
      },
      (err, data) => {
        if (!err) {
          resolve(data.flat(1));
        } else {
          reject(err);
        }
      }
    );
  });
}

export async function fetchStudents({ orgid, sectionIds }) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid,
        resource: "students",
        useComposites: true,
        temporaryFilters: {
          sectionIds
        }
      },
      (err, data) => {
        if (!err) {
          resolve(data.flat(1));
        } else {
          reject(err);
        }
      }
    );
  });
}

export async function fetchStudentDemographics({ orgid, studentIds }) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid,
        resource: "demographics",
        fields: [],
        query: {},
        temporaryFilters: {
          studentIds
        }
      },
      (err, data) => {
        if (!err) {
          resolve(data.flat(1));
        } else {
          reject(err);
        }
      }
    );
  });
}

export async function fetchStudentsForSchool({ orgid, classIds, schoolIds, enrollments }) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid,
        resource: "studentsForSchool",
        fields: [],
        query: {},
        temporaryFilters: {
          schoolIds,
          classIds,
          enrollments
        }
      },
      (err, data) => {
        if (!err) {
          resolve(data);
        } else {
          reject(err);
        }
      }
    );
  });
}

export async function fetchEnrollmentsInSchool({ orgid, schoolIds }) {
  return new Promise((resolve, reject) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid,
        resource: "enrollments",
        fields: [],
        query: {},
        temporaryFilters: {
          schoolIds
        }
      },
      (err, data) => {
        if (!err) {
          resolve(data);
        } else {
          reject(err);
        }
      }
    );
  });
}

export function setUnique(setter, ids) {
  setter([...new Set(ids)]);
}

function setMissing(savedFilterIds, existingIds, setter) {
  if (savedFilterIds?.length) {
    const missingIds = savedFilterIds.filter(id => !existingIds.includes(id));
    if (missingIds.length) {
      setter(missingIds);
    }
  }
}

export class DataProviderForRosteringFilters {
  rosteringSettings;

  rosteringType;

  orgid;

  orgName;

  constructor(args = {}) {
    ({
      rosteringSettings: this.rosteringSettings,
      rosteringType: this.rosteringType,
      orgid: this.orgid,
      orgName: this.orgName
    } = args);
  }

  searchUsers(query, callback = () => {}) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "usersByQuery",
        queryText: query
      },
      (err, users = []) => {
        if (callback) {
          callback(users);
        }
      }
    );
  }

  getEnrollmentsByUserId = (userId, callback = () => {}) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "enrollmentsByUserId",
        query: { filter: `user.sourcedId='${userId}'` }
      },
      (err, enrollments = []) => {
        if (callback) {
          callback(enrollments);
        }
      }
    );
  };

  searchClasses(query, callback = () => {}) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "classesByQuery",
        queryText: query
      },
      (err, classes = []) => {
        if (callback) {
          callback(classes);
        }
      }
    );
  }

  getEnrollmentsByClassId = (classId, callback = () => {}) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "enrollmentsByClassId",
        query: { filter: `class.sourcedId='${classId}'` }
      },
      (err, enrollments = []) => {
        if (callback) {
          callback(enrollments);
        }
      }
    );
  };

  searchSchools(query, callback = () => {}) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "schoolsByQuery",
        queryText: query
      },
      (err, schools = []) => {
        if (callback) {
          callback(schools);
        }
      }
    );
  }

  getEnrollmentsBySchoolId = (schoolsId, callback = () => {}) => {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "enrollmentsBySchoolId",
        query: { filter: `school.sourcedId='${schoolsId}'` }
      },
      (err, enrollments = []) => {
        if (callback) {
          callback(enrollments);
        }
      }
    );
  };

  getSchools({ setAvailableSchools, setSchoolsError, setMissingSavedSchools }) {
    setSchoolsError(false);
    setAvailableSchools(null);
    const fields =
      this.rosteringType === "rosterEdFi"
        ? ["id", "schoolId", "nameOfInstitution", "localEducationAgencyReference"]
        : [];
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        fields,
        resource: "schools"
      },
      (err, schools) => {
        if (schools) {
          setMissing(
            this.rosteringSettings?.filters?.schools,
            schools.map(elem => elem.schoolId),
            setMissingSavedSchools
          );
          if (this.rosteringType === "rosterEdFi") {
            // eslint-disable-next-line no-param-reassign
            schools = schools.map(val => ({ ...val, name: val.nameOfInstitution }));
          }
          setAvailableSchools(schools);
        } else {
          Alert.error(err.reason || err.message);
          setSchoolsError(true);
        }
      }
    );
  }

  getTeachersEdFi({
    selectedSchools,
    availableSchools,
    selectedTeachers,
    setAvailableTeachers,
    setSelectedTeachers,
    setTeachersError,
    callback,
    setMissingSavedTeachers
  }) {
    const chosenSchools = availableSchools.filter(as => selectedSchools.includes(as.schoolId));
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "staffs",
        useComposites: true,
        temporaryFilters: {
          schoolIds: chosenSchools.map(s => s.id)
        }
      },
      (error, staffs) => {
        if (typeof callback === "function") {
          callback();
        }
        if (staffs) {
          setMissing(
            this.rosteringSettings?.filters?.teachers,
            staffs.map(elem => elem.staffUniqueId),
            setMissingSavedTeachers
          );
          const teachers = uniqBy(
            staffs.map(({ id, staffUniqueId: _id, firstName, lastSurname, electronicMails, ancestorItemId }) => ({
              _id,
              docId: id,
              name: `${firstName} ${lastSurname}`,
              firstName,
              lastSurname,
              electronicMails: electronicMails.length ? electronicMails[0] : {},
              schoolId: chosenSchools.find(val => val.id === ancestorItemId).schoolId
            })),
            "_id"
          );
          setAvailableTeachers(teachers);
          const availableTeacherIds = teachers.map(teacher => teacher._id);
          setUnique(
            setSelectedTeachers,
            selectedTeachers.filter(selectedTeacherId => availableTeacherIds.includes(selectedTeacherId))
          );
        } else {
          Alert.error(`API error: ${error.reason || error.message}`);
          setTeachersError(true);
        }
      }
    );
  }

  getTeachersOneRoster({
    selectedSchools,
    setAvailableTeachers,
    callback,
    setMissingSavedTeachers,
    setSelectedTeachers,
    setTeachersError,
    selectedTeachers
  }) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        fields: [],
        resource: "teachersForSchool",
        query: {
          offset: 0
        },
        temporaryFilters: {
          schoolIds: selectedSchools
        }
      },
      (err, teachers) => {
        if (typeof callback === "function") {
          callback();
        }
        if (err) {
          Alert.error(`API error: ${err.reason || err.message}`);
          setTeachersError(true);
        } else {
          const parsedTeachers = teachers.map(
            ({ sourcedId: _id, givenName: firstName, familyName: lastName, email, schoolId, role }) => ({
              _id,
              name: `${firstName} ${lastName}${role?.length && role !== "teacher" ? ` (${role})` : ""}`,
              firstName,
              lastSurname: lastName,
              electronicMails: email,
              schoolId
            })
          );

          if (parsedTeachers) {
            setAvailableTeachers(parsedTeachers);
            const availableTeacherIds = parsedTeachers.map(availableTeacher => availableTeacher._id);
            setUnique(
              setSelectedTeachers,
              selectedTeachers.filter(selectedTeachersId => availableTeacherIds.includes(selectedTeachersId))
            );
            setMissing(
              this.rosteringSettings?.filters?.teachers,
              parsedTeachers.map(elem => elem._id),
              setMissingSavedTeachers
            );
          }
        }
      }
    );
  }

  getTeachers({
    selectedSchools,
    availableSchools,
    selectedTeachers,
    setAvailableTeachers,
    setSelectedTeachers,
    setTeachersError,
    setMissingSavedTeachers,
    callback = () => {}
  }) {
    setAvailableTeachers(null);
    setTeachersError(false);
    // scrollToLastFilter();
    // eslint-disable-next-line default-case
    switch (this.rosteringType) {
      case "rosterEdFi":
        this.getTeachersEdFi({
          availableSchools,
          selectedSchools,
          callback,
          setSelectedTeachers,
          selectedTeachers,
          setAvailableTeachers,
          setTeachersError,
          setMissingSavedTeachers
        });
        break;
      case "rosterOR":
        this.getTeachersOneRoster({
          selectedSchools,
          callback,
          setSelectedTeachers,
          selectedTeachers,
          setAvailableTeachers,
          setTeachersError,
          setMissingSavedTeachers
        });
        break;
    }
    // scrollToLastFilter();
  }

  getClassesOneRoster({
    selectedTeachers,
    selectedSchools,
    setAvailableClasses,
    setSelectedClasses,
    selectedClasses,
    setClassesError,
    setMissingSavedClasses,
    availableTeachers,
    callback
  }) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        // fields: ["sourcedId", "title", "classCode", "location", "school", "grades"],
        fields: [],
        resource: "classes",
        query: {
          offset: 0
        },
        temporaryFilters: {
          schoolIds: selectedSchools,
          shouldFetchEnrollments: true,
          teacherIds: availableTeachers.map(t => t._id)
        }
      },
      (err, classes) => {
        if (typeof callback === "function") {
          callback();
        }
        if (classes) {
          const parsedClasses = classes
            .filter(
              c =>
                selectedSchools.includes(c.school?.sourcedId) &&
                (selectedTeachers.includes(c.teacherId) || selectedClasses.includes(c.sourcedId))
            )
            .map(({ sourcedId: _id, classCode, title, location, school, grades, teacherId, course }) => {
              const name = `${title}${location ? ` - ${location}` : ""}`;
              const classSectionId = getClassSectionIdForOneRoster({
                classId: _id,
                school,
                course,
                classCode,
                grades
              });
              const classFullName = `${name} (${classSectionId})`;
              return {
                _id,
                name,
                classSectionId,
                classFullName,
                schoolId: school.sourcedId,
                grades,
                teacherId
              };
            });
          setMissing(
            this.rosteringSettings?.filters?.classes,
            parsedClasses.map(elem => elem._id),
            setMissingSavedClasses
          );
          setAvailableClasses(parsedClasses);
          const availableClassIds = parsedClasses.map(availableClass => availableClass._id);
          setUnique(
            setSelectedClasses,
            selectedClasses.filter(selectedClassId => availableClassIds.includes(selectedClassId))
          );
          // scrollToLastFilter();
        } else {
          Alert.error(`API error: ${err.reason || err.message}`);
          setClassesError(true);
        }
      }
    );
  }

  getClassesEdFi({
    availableTeachers,
    selectedTeachers,
    setCompositeClasses,
    selectedSchools,
    setAvailableClasses,
    setSelectedClasses,
    selectedClasses,
    setClassesError,
    setMissingSavedClasses,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate,
    callback
  }) {
    Meteor.call(
      "getRosteringData",
      {
        orgid: this.orgid,
        resource: "sections",
        useComposites: true,
        temporaryFilters: {
          teacherIds: (availableTeachers || []).filter(as => selectedTeachers.includes(as._id)).map(s => s.docId)
        }
      },
      (err, compositeSections) => {
        if (typeof callback === "function") {
          callback();
        }
        if (err) {
          Alert.error(`API error: ${err.reason || err.message}`);
          setClassesError(true);
        } else {
          setCompositeClasses(compositeSections);
          const sectionIdentifiers = (compositeSections || []).map(s => get(s, "sectionIdentifier", ""));
          if (!sectionIdentifiers.length) {
            return;
          }
          const compositeSectionsById = keyBy(compositeSections, cs => normalizeExternalRosteringId(cs.id));
          Meteor.call(
            "getEdFiData",
            {
              orgid: this.orgid,
              itemsToFetch: "sections",
              fields: [],
              query: {
                schoolId: selectedSchools
              }
            },
            (error, sections) => {
              if (sections) {
                const customDate = get(getMeteorUser(), "profile.customDate");
                const currentDate = moment(getCurrentDate(customDate, this.orgid) || new Date());
                const classes = sections
                  .filter(section => Object.keys(compositeSectionsById).includes(section.id))
                  .map(
                    ({
                      sectionIdentifier,
                      sectionName = "",
                      classPeriods,
                      courseOfferingReference,
                      id,
                      locationReference,
                      offeredGradeLevels
                    }) => {
                      const { classroomCode, className, classFullName } = getExternalRosteringClassDetails({
                        locationReference,
                        classPeriods,
                        courseOfferingReference,
                        sectionName,
                        sectionIdentifier
                      });
                      const teachersForClass = get(compositeSectionsById[id], "staff", []);
                      const students = get(compositeSectionsById[id], "students", []).filter(
                        student =>
                          (!student.beginDate ||
                            shouldIgnoreEnrollmentStartDate ||
                            moment(student.beginDate) <= currentDate) &&
                          (!student.endDate || shouldIgnoreEnrollmentEndDate || moment(student.endDate) >= currentDate)
                      );

                      return teachersForClass.map(teacher => ({
                        _id: id,
                        sectionIdentifier,
                        classroomCode,
                        name: className,
                        classFullName,
                        courseOfferingReference,
                        sectionName,
                        locationReference,
                        grade: get(offeredGradeLevels, 0, ""),
                        teachers: [teacher],
                        teacherId: teacher.staffUniqueId,
                        students
                      }));
                    }
                  )
                  .flat(1);
                /*
                    Special case for EdFi:
                     We use _id field for filtering, but it's not saved in the StudentGroup object which instead is using classroomCode as sectionId.
                     That means that it's impossible to determine which _id belongs to which StudentGroup for a class that might be in SM,
                     but that no longer exists in the EdFi endpoint.
                  */
                setAvailableClasses(classes);
                setMissing(
                  this.rosteringSettings?.filters?.classes,
                  classes.map(elem => elem._id),
                  setMissingSavedClasses
                );
                const availableClassIds = classes.map(availableClass => availableClass._id);
                setUnique(
                  setSelectedClasses,
                  selectedClasses.filter(selectedClassId => availableClassIds.includes(selectedClassId))
                );
                // scrollToLastFilter();
              } else {
                Alert.error(`API error: ${error.reason || error.message}`);
                setClassesError(true);
              }
            }
          );
        }
      }
    );
  }

  getClasses({
    setAvailableClasses,
    setClassesError,
    availableTeachers,
    selectedTeachers,
    setCompositeClasses,
    selectedSchools,
    setSelectedClasses,
    selectedClasses,
    setMissingSavedClasses,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate,
    callback
  }) {
    setAvailableClasses(null);
    setClassesError(false);
    // eslint-disable-next-line default-case
    switch (this.rosteringType) {
      case "rosterEdFi":
        this.getClassesEdFi({
          availableTeachers,
          selectedTeachers,
          setCompositeClasses,
          selectedSchools,
          setAvailableClasses,
          setSelectedClasses,
          selectedClasses,
          setClassesError,
          setMissingSavedClasses,
          shouldIgnoreEnrollmentStartDate,
          shouldIgnoreEnrollmentEndDate,
          callback
        });
        break;
      case "rosterOR":
        this.getClassesOneRoster({
          selectedTeachers,
          selectedSchools,
          setAvailableClasses,
          setSelectedClasses,
          selectedClasses,
          setClassesError,
          setMissingSavedClasses,
          availableTeachers,
          callback
        });
        break;
    }
    // scrollToLastFilter();
  }

  async getRosterEdFi({
    compositeClasses,
    filteredAvailableClasses,
    availableSchools,
    availableTeachers,
    rosteringSettings,
    isFullFetch,
    importData,
    allowMultipleGradeLevels,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate,
    students
  }) {
    let studentsByUniqueId = {};
    const descriptors = await fetchGradeLevelDescriptors({ orgid: this.orgid });
    const parsedDescriptors = descriptors.reduce((a, d) => {
      let { description } = d;
      const { codeValue } = d;
      if (["13", ">12"].includes(codeValue)) {
        description = "Grade 13";
      }
      // eslint-disable-next-line no-param-reassign
      a[codeValue] = description.toLowerCase();
      return a;
    }, {});
    const studentDocIds = compositeClasses.map(c => c.students.map(s => s.id) || []).flat(3);
    const studentsInSection = students.filter(s => studentDocIds.includes(s.id));
    studentsByUniqueId = keyBy(students, "studentUniqueId");

    const schoolNameBySchoolId = availableSchools.reduce((result, school) => {
      // eslint-disable-next-line no-param-reassign
      result[school.schoolId] = {
        nameOfInstitution: school.nameOfInstitution,
        localEducationAgencyId: get(school, "localEducationAgencyReference.localEducationAgencyId")
      };
      return result;
    }, {});

    const customGradeTranslations = rosteringSettings?.translations || {};
    const gradeTranslationsByGrade = getGradeTranslationsByGrade(customGradeTranslations);
    const gradeByGradeTranslation = getGradeByGradeTranslation(gradeTranslationsByGrade);

    return filteredAvailableClasses.reduce((filteredSections, section) => {
      const {
        courseOfferingReference,
        locationReference,
        name: className,
        classFullName,
        _id: sectionId,
        classroomCode,
        teachers: teacherAssociations,
        students: studentSectionAssociations
      } = section;
      const dateNow = Date.now();
      const studentSectionAssociationsWithoutInactiveStudents = studentSectionAssociations.filter(
        s =>
          (!s.enrollmentBeginDate || shouldIgnoreEnrollmentStartDate || new Date(s.enrollmentBeginDate) <= dateNow) &&
          (!s.enrollmentEndDate || shouldIgnoreEnrollmentEndDate || new Date(s.enrollmentEndDate) >= dateNow)
      );
      // NOTE(fmazur) - Get first teacher for now
      const { staffUniqueId = "" } = get(teacherAssociations, 0, {});
      const staffByStaffUniqueId = keyBy(availableTeachers, "_id");
      const studentAssociationsByStudentUniqueId = keyBy(
        studentSectionAssociationsWithoutInactiveStudents,
        "studentUniqueId"
      );
      const studentAssociations = Object.values(studentAssociationsByStudentUniqueId);
      const { name: teacherName = "N/A", firstName, lastSurname, electronicMails = [] } = get(
        staffByStaffUniqueId,
        staffUniqueId,
        {}
      );

      const schoolName = get(schoolNameBySchoolId[courseOfferingReference.schoolId], "nameOfInstitution", "N/A");
      const numberOfKids = get(studentAssociations, "length", 0);

      const studentIdsFromSectionAssociations = studentAssociations.map(sa => sa.studentUniqueId);
      const studentsInSectionWithGrades = studentsInSection
        .filter(s => studentIdsFromSectionAssociations.includes(s.studentUniqueId))
        .map(s => {
          const { entryGradeLevelDescriptor = "" } =
            (s.studentSchoolEnrollments || s.schools).find(se => se.schoolId === locationReference.schoolId) ||
            (s.studentSchoolEnrollments || s.schools)[0] ||
            {};
          return {
            studentUniqueId: s.studentUniqueId,
            studentGrade: getGradeFromDescriptor(entryGradeLevelDescriptor, parsedDescriptors, gradeByGradeTranslation)
          };
        });
      const sectionGrade = getSectionGradeMajority(
        mapExternalRosteringStudentGrades(
          studentsInSectionWithGrades.map(s => getGradeLabel(s.studentGrade) || s.studentGrade)
        )
      );
      const studentsInSectionWithGradeByUniqueId = keyBy(studentsInSectionWithGrades, "studentUniqueId");

      const filteredSectionFields = {
        _id: sectionId,
        schoolName,
        className: classFullName,
        teacherName,
        grade: sectionGrade || "N/A",
        numberOfKids
      };
      if (isFullFetch) {
        const teacherEmail = get(electronicMails, "electronicMailAddress", "");
        importData.push(
          ...studentAssociations.map(({ studentUniqueId }) => {
            const student = studentsByUniqueId[studentUniqueId] || {};
            // NOTE(fmazur) - use grade majority for students that are missing grade
            const studentGrade = studentsInSectionWithGradeByUniqueId[studentUniqueId]?.studentGrade || sectionGrade;
            return {
              DistrictID: get(
                schoolNameBySchoolId[courseOfferingReference.schoolId],
                "localEducationAgencyId",
                "N/A"
              ).toString(),
              DistrictName: this.orgName, // SpringMath side organization name
              SchoolID: courseOfferingReference.schoolId.toString(),
              SchoolName: schoolName,
              TeacherID: staffUniqueId,
              TeacherLastName: lastSurname,
              TeacherFirstName: firstName,
              TeacherEmail: teacherEmail,
              ClassName: className,
              ClassSectionID: classroomCode,
              StudentLocalID: studentUniqueId,
              StudentStateID: studentUniqueId,
              StudentFirstName: student.firstName,
              StudentBirthDate: student.birthDate || "",
              StudentLastName: student.lastSurname,
              StudentGrade: studentGrade,
              SpringMathGrade: allowMultipleGradeLevels ? sectionGrade : studentGrade
            };
          })
        );
      }
      filteredSections.push(filteredSectionFields);
      return filteredSections;
    }, []);
  }

  async getEnrollmentsInSchools(schoolIds) {
    return fetchEnrollmentsInSchool({
      orgid: this.orgid,
      schoolIds
    });
  }

  async getStudentsInSchools({ classIds, schoolIds, enrollments }) {
    return fetchStudentsForSchool({
      orgid: this.orgid,
      classIds,
      schoolIds,
      enrollments
    });
  }

  async getEdFiStudentsInSchools(sectionIds) {
    return fetchStudents({
      orgid: this.orgid,
      sectionIds
    });
  }

  async getRosterOneRoster({
    availableSchools,
    availableTeachers,
    importData,
    ncesBySchoolId,
    selectedSchools,
    rosteringSettings,
    isFullFetch,
    filteredAvailableClasses,
    allowMultipleGradeLevels,
    enrollments,
    students
  }) {
    const customGradeTranslations = rosteringSettings?.translations || {};
    const gradeTranslationsByGrade = getGradeTranslationsByGrade(customGradeTranslations);

    const teacherEnrollments = enrollments.filter(e => e.role !== "student");
    const availableTeacherIds = availableTeachers.map(a => a._id);
    const teacherEnrollmentsByClassId = groupBy(
      teacherEnrollments.filter(te => availableTeacherIds.includes(te?.user?.sourcedId)),
      "classId"
    );
    const studentsByClassId = groupBy(students, "classId");
    let demographics = [];
    if (isFullFetch) {
      demographics = await fetchStudentDemographics({
        orgid: this.orgid,
        studentIds: students.map(s => s.sourcedId)
      }).catch(e => {
        console.error("fetchStudentDemographics error", e);
      });

      availableSchools
        .filter(as => selectedSchools.includes(as.sourcedId))
        .forEach(school => {
          if (school.ncesSchoolId && school.name) {
            // eslint-disable-next-line no-param-reassign
            ncesBySchoolId[school.schoolId] = {
              ncesId: school.ncesSchoolId,
              name: school.name
            };
          }
        });
    }

    const filteredSchoolsBySchoolId = keyBy(
      availableSchools.filter(as => selectedSchools.includes(as.sourcedId)),
      "sourcedId"
    );
    const teachersByTeacherId = keyBy(availableTeachers, "_id");

    const uniqueClasses = uniqBy(filteredAvailableClasses, "_id");
    return uniqueClasses
      .map(({ _id: classId, name: className, schoolId, classSectionId, classFullName }) => {
        const orderedTeacherEnrollmentsForClass = orderBy(
          uniqBy(teacherEnrollmentsByClassId[classId], "user.sourcedId") || [],
          ["primary"],
          ["desc"]
        );
        const studentsInClass = studentsByClassId[classId] || [];
        const studentGradesList = mapExternalRosteringStudentGrades(
          getGradesFromOneRosterStudents(studentsInClass, gradeTranslationsByGrade)
        );
        const sectionGrade = getSectionGradeMajority(studentGradesList);
        return orderedTeacherEnrollmentsForClass.map(teacherEnrollment => {
          const teacherId = teacherEnrollment.user.sourcedId;
          const parsedRow = {
            _id: classId,
            schoolName: get(filteredSchoolsBySchoolId[schoolId], "name", "N/A"),
            className: classFullName,
            teacherName: get(teachersByTeacherId[teacherId], "name", "N/A"),
            grade: sectionGrade,
            numberOfKids: studentsInClass.length
          };

          if (isFullFetch && studentsByClassId[classId]?.length) {
            const demographicsByStudentId = keyBy(demographics, "sourcedId");
            const {
              firstName: teacherFirstName,
              lastSurname: teacherLastName,
              electronicMails: email
            } = teachersByTeacherId[teacherId];
            const { parent: parentOrgEntity } = filteredSchoolsBySchoolId[schoolId] || {};
            (studentsByClassId[classId] || []).forEach(student => {
              const { sourcedId: studentId, givenName: studentFirstName, familyName: studentLastName } = student;
              const studentDemographics = demographicsByStudentId[studentId];
              const [studentGrade = sectionGrade] = getGradesFromOneRosterStudents([student], gradeTranslationsByGrade);
              importData.push({
                DistrictID: this.orgid || parentOrgEntity?.sourcedId, // random number ?
                DistrictName: this.orgName,
                SchoolID: (get(filteredSchoolsBySchoolId[schoolId], "ncesSchoolId") || schoolId).toString().trim(),
                SchoolName: parsedRow.schoolName,
                TeacherID: teacherId,
                TeacherLastName: teacherLastName,
                TeacherFirstName: teacherFirstName,
                TeacherEmail: email,
                ClassName: className, // TODO make name consistent with cronjob import
                ClassSectionID: classSectionId,
                StudentLocalID: studentId,
                StudentStateID: studentId,
                StudentFirstName: studentFirstName.toString(),
                StudentBirthDate: studentDemographics?.birthDate || studentDemographics?.birthdate || "",
                StudentLastName: studentLastName.toString(),
                StudentGrade: studentGrade,
                SpringMathGrade: allowMultipleGradeLevels ? sectionGrade : studentGrade
              });
            });
          }
          return parsedRow;
        });
      })
      .flat(1);
  }

  getRoster({
    compositeClasses,
    filteredAvailableClasses,
    availableSchools,
    availableTeachers,
    selectedSchools,
    rosteringSettings,
    isFullFetch,
    importData,
    ncesBySchoolId,
    allowMultipleGradeLevels,
    enrollments,
    students,
    shouldIgnoreEnrollmentStartDate,
    shouldIgnoreEnrollmentEndDate
  }) {
    // eslint-disable-next-line default-case
    switch (this.rosteringType) {
      case "rosterEdFi":
        return this.getRosterEdFi({
          compositeClasses,
          filteredAvailableClasses,
          availableSchools,
          availableTeachers,
          rosteringSettings,
          isFullFetch,
          importData,
          allowMultipleGradeLevels,
          shouldIgnoreEnrollmentStartDate,
          shouldIgnoreEnrollmentEndDate,
          students
        });
      case "rosterOR":
        return this.getRosterOneRoster({
          availableSchools,
          availableTeachers,
          importData,
          ncesBySchoolId,
          selectedSchools,
          rosteringSettings,
          isFullFetch,
          filteredAvailableClasses,
          allowMultipleGradeLevels,
          enrollments,
          students
        });
    }
    return [];
  }
}
