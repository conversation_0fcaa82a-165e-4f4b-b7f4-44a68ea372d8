import flatten from "lodash/flatten";
import each from "lodash/each";
import { RosterImportItems } from "/imports/api/rosterImportItems/rosterImportItems";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import RosterImportItemsHelpers from "/imports/api/rosterImportItems/methods";
import RosterImportItemsValidator from "/imports/api/rosterImportItems/rosterImportItemsValidator";
import { displayStudentData, ninjalog } from "/imports/api/utilities/utilities";
import { getNormalizedGrade } from "/imports/api/rosterImportItems/normalizeRosterImportItems";

export function validateAndSetSubtotals({ data, callback, isCSV = true, shouldDisplayLoading = true }) {
  const failedRows = [];
  const errors = [];
  const schools = new Map();
  const teachers = new Map();
  const classes = new Map();
  const students = new Map();
  const rosterImportItems = [];
  const processFileUploadLineItemFunctions = [];
  const schema = RosterImportItems.schemaStudentData;
  if (shouldDisplayLoading) {
    incWaitingOn(1, "Validating!");
  }
  function processRosterImportItem(datum, index) {
    return new Promise(resolve => {
      const rosterItem = RosterImportItemsHelpers.createFromCSVDataRow(datum);
      rosterImportItems.push(rosterItem);
      try {
        schema.validate(rosterItem.data);
      } catch (e) {
        e.details.forEach(error => {
          const message = schema.messageForError(error);
          errors.push(`${message}.\n${isCSV ? `Please see row: ${index + 2}` : displayStudentData(rosterItem.data)}`);
          failedRows.push(index);
        });

        resolve(false);
      }
      schools.set(rosterItem.data.schoolID, true);
      teachers.set(
        `${rosterItem.data.teacherID}${rosterItem.data.teacherLastName}${rosterItem.data.teacherFirstName}`,
        true
      );
      classes.set(
        `${rosterItem.data.classSectionID}${getNormalizedGrade(rosterItem.data.springMathGrade)}${
          rosterItem.data.schoolID
        }`,
        true
      );
      students.set(
        `${rosterItem.data.studentStateID}` +
          `${rosterItem.data.studentFirstName}` +
          `${rosterItem.data.studentLastName}` +
          `${rosterItem.data.studentLocalID}` +
          `${rosterItem.data.studentBirthDate}`,
        true
      );

      resolve(true);
    });
  }

  each(data, (datum, index) => {
    processFileUploadLineItemFunctions.push(processRosterImportItem(datum, index));
  });

  Promise.all(processFileUploadLineItemFunctions).then(() => {
    const rosterImportItemsValidator = new RosterImportItemsValidator(rosterImportItems, isCSV);
    let itemsValidationResult = null;
    let validationErrors = {};
    let validationFailedRows = new Set();
    try {
      ({
        success: itemsValidationResult,
        errors: validationErrors,
        failedRows: validationFailedRows
      } = rosterImportItemsValidator.validate());
    } catch (e) {
      ninjalog.error({
        msg: e.message,
        context: "RosterImportItemsValidator",
        val: e
      });
      errors.push(e.message);
    }
    if (itemsValidationResult === false) {
      errors.push(...flatten(Object.values(validationErrors)));
      failedRows.push(...Array.from(validationFailedRows));
    }
    if (shouldDisplayLoading) {
      decWaitingOn();
    }
    callback({ schools, teachers, classes, students, errors, failedRows });
  });
}
