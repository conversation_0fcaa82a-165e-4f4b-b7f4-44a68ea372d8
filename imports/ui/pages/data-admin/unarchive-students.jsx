import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { useTracker } from "meteor/react-meteor-data";
import { Link } from "react-router-dom";
import get from "lodash/get";

import Header from "/imports/ui/components/admin-view/admin-header";
import Loading from "/imports/ui/components/loading";
import { isExternalRostering } from "/imports/ui/utilities";
import { getMeteorUserSync, getCurrentSchoolYear } from "/imports/api/utilities/utilities";
import { Organizations } from "/imports/api/organizations/organizations";
import { Students } from "/imports/api/students/students";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { OrganizationContext } from "/imports/contexts/OrganizationContext";
import { SchoolYearContext } from "/imports/contexts/SchoolYearContext";

import ManageStudents from "./manage-students";
import { isSingleSchoolDataAdmin } from "./utilities";

const UnarchiveStudents = ({ orgid, siteId, students, rostering, loading }) => {
  if (loading) {
    return <Loading />;
  }

  return (
    <div className="workspace-container">
      <div className="d-flex mt-1">
        {!isSingleSchoolDataAdmin() ? (
          <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${orgid}`}>
            <i className="fa fa-caret-left" /> Back to All Schools
          </Link>
        ) : null}
        <Link className="btn btn-outline-blue btn-xs" to={`/data-admin/manage-group/students/${orgid}/site/${siteId}`}>
          <i className="fa fa-caret-left" /> Back to School
        </Link>
      </div>

      <Header keyLabel="schoolOverview" headerTitle="Unarchive Students" />

      <div className="card-box-wrapper mt-2">
        <ManageStudents
          students={students}
          siteId={siteId}
          orgid={orgid}
          manageView="unarchive"
          isExternalRostering={isExternalRostering(rostering)}
        />
      </div>
    </div>
  );
};

UnarchiveStudents.propTypes = {
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  students: PropTypes.array,
  rostering: PropTypes.string
};

const UnarchiveStudentsWithData = ({ orgid = "", siteId, schoolYear: contextSchoolYear }) => {
  const { orgId } = useContext(OrganizationContext);
  const { schoolYear: schoolYearFromContext } = useContext(SchoolYearContext);
  const [resolvedSchoolYear, setResolvedSchoolYear] = useState(null);

  // Use orgid from props, fallback to context
  const resolvedOrgId = orgid || orgId;

  // Handle async school year resolution
  useEffect(() => {
    const resolveSchoolYear = async () => {
      if (contextSchoolYear) {
        setResolvedSchoolYear(contextSchoolYear);
      } else if (schoolYearFromContext) {
        setResolvedSchoolYear(schoolYearFromContext);
      } else {
        const user = getMeteorUserSync();
        if (user && resolvedOrgId) {
          const currentSchoolYear = await getCurrentSchoolYear(user, resolvedOrgId);
          setResolvedSchoolYear(currentSchoolYear);
        }
      }
    };

    resolveSchoolYear();
  }, [contextSchoolYear, schoolYearFromContext, resolvedOrgId]);

  const { loading, rostering, students } = useTracker(() => {
    if (!resolvedSchoolYear) {
      return { loading: true, rostering: "", students: [] };
    }

    const orgHandler = Meteor.subscribe("Organizations", resolvedOrgId);
    const studentGroupEnrollmentsHandler = Meteor.subscribe("StudentGroupEnrollments:PerOrg:Archived", resolvedOrgId);
    const studentsHandler = Meteor.subscribe("Students:PerSiteInYear", {
      orgid: resolvedOrgId,
      siteId,
      schoolYear: resolvedSchoolYear
    });

    const isLoading = !orgHandler.ready() || !studentGroupEnrollmentsHandler.ready() || !studentsHandler.ready();

    let rosteringType = "";
    let studentsData = [];

    if (!isLoading) {
      rosteringType = get(Organizations.findOne(), "rostering");

      let studentEnrollments = StudentGroupEnrollments.find({}).fetch();
      studentEnrollments = studentEnrollments.filter(studentEnrollment => {
        const thisStudentIsActive = studentEnrollments.find(
          se => studentEnrollment.studentId === se.studentId && se.isActive
        );
        return !thisStudentIsActive;
      });

      studentEnrollments = studentEnrollments.filter(studentEnrollment => studentEnrollment.isActive === false);

      const studentIds = studentEnrollments.map(enrollment => enrollment.studentId);
      studentsData = Students.find(
        { _id: { $in: studentIds } },
        { sort: { "identity.name.lastName": 1, "identity.name.firstName": 1 } }
      ).fetch();
    }

    return {
      loading: isLoading,
      rostering: rosteringType,
      students: studentsData
    };
  }, [resolvedOrgId, siteId, resolvedSchoolYear]);

  return (
    <UnarchiveStudents
      orgid={resolvedOrgId}
      siteId={siteId}
      students={students}
      rostering={rostering}
      loading={loading}
    />
  );
};

UnarchiveStudentsWithData.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  schoolYear: PropTypes.number
};

export default UnarchiveStudentsWithData;
