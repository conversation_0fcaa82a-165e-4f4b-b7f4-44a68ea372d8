import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import Select from "react-select";
import get from "lodash/get";
import Alert from "react-s-alert";
import { withRouter } from "react-router-dom";
import StudentUploadWrapper from "./add-class-student-upload-wrapper";
import AddTeacherForm from "./add-teacher-form";
import { getTeacherLabel, getUserRoles } from "./utilities";
import { getReactSelectCustomHeightStyle } from "../../utilities";

const STEPS = {
  addClass: 1,
  uploadStudents: 2
};

class AddClassForm extends Component {
  constructor(props) {
    super(props);
    const defaultGrade = this.getDefaultGrade();
    this.state = {
      newClass: { name: "", sectionId: "", grade: get(props.grades, "[0].display", defaultGrade), teacher: "" },
      newTeacher: { lastName: "", firstName: "", localId: "", email: "" },
      isTeacherFormValid: false,
      isNewTeacher: false,
      isSavingForm: false,
      step: STEPS.addClass,
      studentGroupId: null,
      rostering: "rosterUpload"
    };
  }

  componentDidMount() {
    const userRole = getUserRoles();
    const isSuperAdminOrUniversalDataAdmin = userRole.includes("universalDataAdmin") || userRole.includes("superAdmin");
    Meteor.call("Organizations:getOrganizationFieldValues", this.props.orgid, ["rostering"], (err, resp) => {
      if (!err) {
        if (resp.rostering === "rosterUpload" && isSuperAdminOrUniversalDataAdmin) {
          // SuperAdmin or UniversalDataAdmin needs access to import when organization has blocked roster imports
          this.setState({ rostering: "rosterImport" });
        } else {
          this.setState({ rostering: resp.rostering });
        }
      }
    });
  }

  getDefaultGrade = () => {
    return "01";
  };

  isAddClassFormValid = () => {
    const isNewClassValid = Object.values(this.state.newClass).every(value => value.trim().length);
    return isNewClassValid && (!this.state.isNewTeacher || (this.state.isNewTeacher && this.isAddTeacherFormValid()));
  };

  isAddTeacherFormValid = () => Object.values(this.state.newTeacher).every(value => value.trim().length);

  resetNewClass = () => {
    this.setState({
      newClass: { name: "", sectionId: "", grade: this.getDefaultGrade(), teacher: "" }
    });
  };

  resetNewTeacher = () => {
    this.setState({
      isNewTeacher: false,
      newTeacher: { lastName: "", firstName: "", localId: "", email: "" }
    });
  };

  onChangeNewClass = field => e => {
    const newClass = { ...this.state.newClass, [field]: e.target.value };
    this.setState({ newClass });
  };

  onChangeTeacher = e => {
    const newClass = { ...this.state.newClass, teacher: e.value };
    const isNewTeacher = e.value === "add_new_teacher";
    this.setState({ isNewTeacher, newClass, isTeacherFormValid: !isNewTeacher });
  };

  saveNewClass = () => {
    if (!this.state.isTeacherFormValid) {
      return Alert.error("Please fix teacher form before proceeding", { timeout: 3000 });
    }
    const { orgid, siteId } = this.props;
    const studentGroup = {};
    const teacher = {};
    Object.entries(this.state.newClass).forEach(([key, value]) => {
      studentGroup[key] = value.trim();
    });
    if (this.state.isNewTeacher) {
      Object.entries(this.state.newTeacher).forEach(([key, value]) => {
        teacher[key] = value.trim();
      });
    }

    this.setState({ isSavingForm: true });

    Meteor.call("addClassToSite", { studentGroup, teacher, orgid, siteId }, (error, result) => {
      if (error) {
        Alert.error(error.reason || "There was a problem while adding a new class", {
          timeout: 5000
        });
      } else {
        Alert.success("Class has been added successfully", {
          timeout: 3000
        });
        this.setState({
          studentGroupId: result
        });
        if (this.state.rostering === "rosterImport") {
          this.goToNextStep();
        } else {
          this.goToManageStudentsPage();
        }
      }
      this.setState({ isSavingForm: false });
    });
    return true;
  };

  goToNextStep = () => {
    this.setState({
      step: STEPS.uploadStudents
    });
    this.resetNewClass();
    this.resetNewTeacher();
  };

  goToManageStudentsPage = () => {
    this.props.history.push(
      `/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}/${this.state.studentGroupId}`
    );
  };

  onTeacherChange = (teacher, isTeacherFormValid = false) => {
    const newTeacher = { ...this.state.newTeacher, ...teacher };
    this.setState({ newTeacher, isTeacherFormValid });
  };

  handleCancel = () => {
    this.props.history.push(`/data-admin/manage-group/students/${this.props.orgid}/site/${this.props.siteId}`);
  };

  render() {
    const { newClass, newTeacher, isNewTeacher, isSavingForm } = this.state;
    const { teachers, grades } = this.props;

    return (
      <div className="p-3">
        <div className="text-center">
          <div className="wizard">
            <a className={this.state.step === 1 ? "current" : ""}>
              <span className="badge rounded-pill bg-secondary">1</span> Add class & teacher
            </a>
            <a className={this.state.step === 2 ? "current" : ""}>
              <span className="badge rounded-pill bg-secondary">2</span>{" "}
              {this.state.rostering === "rosterImport" ? `Upload` : `Manually add`} students
            </a>
          </div>
        </div>

        {this.state.step === STEPS.addClass ? (
          <React.Fragment>
            <div className="row mb-3">
              <div className="col-sm-5">
                <small>Class Name</small>
                <br />
                <input
                  onChange={this.onChangeNewClass("name")}
                  value={newClass.name}
                  className="form-control form-control-sm"
                  placeholder="Class Name"
                />
              </div>
              <div className="col-sm-3">
                <small>Class Section ID</small>
                <br />
                <input
                  onChange={this.onChangeNewClass("sectionId")}
                  value={newClass.sectionId}
                  className="form-control form-control-sm"
                  placeholder="Class Section ID"
                />
              </div>
              <div className="col-sm-1">
                <small>Grade</small>
                <br />
                <select
                  id="select-grade"
                  className="form-select form-select-sm"
                  value={newClass.grade}
                  onChange={this.onChangeNewClass("grade")}
                >
                  {grades.map(grade => (
                    <option key={grade._id} value={grade.display}>
                      {grade.display}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-sm-3">
                <small>Teacher</small>
                <br />
                <Select
                  className="form-control form-select-sm"
                  classNamePrefix="react-select"
                  value={{
                    value: newClass.teacher,
                    label:
                      newClass.teacher === "add_new_teacher"
                        ? "Add new teacher"
                        : getTeacherLabel(newClass.teacher, teachers)
                  }}
                  id="select-teacher"
                  onChange={this.onChangeTeacher}
                  styles={getReactSelectCustomHeightStyle(21)}
                  options={[
                    { value: "add_new_teacher", label: "Add new teacher" },
                    ...teachers.map(teacher => ({
                      value: teacher._id,
                      label: getTeacherLabel(teacher._id, teachers)
                    }))
                  ]}
                />
              </div>
            </div>
            {isNewTeacher && (
              <div className="card card-header mb-3">
                <AddTeacherForm teacher={newTeacher} onChange={this.onTeacherChange} orgid={this.props.orgid} />
              </div>
            )}
            <fieldset className="form-group">
              <div className="row justify-content-between">
                <div className="col-4">
                  <button type="button" className="btn btn-danger form-control" onClick={this.handleCancel}>
                    Cancel
                  </button>
                </div>
                <div className="col-4">
                  <button
                    type="submit"
                    className="btn btn-primary form-control"
                    onClick={this.saveNewClass}
                    disabled={!this.isAddClassFormValid() || isSavingForm}
                  >
                    {isSavingForm ? "Saving..." : "Next"}
                  </button>
                </div>
              </div>
            </fieldset>
          </React.Fragment>
        ) : (
          <StudentUploadWrapper orgid={this.props.orgid} studentGroupId={this.state.studentGroupId} />
        )}
      </div>
    );
  }
}

AddClassForm.propTypes = {
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  teachers: PropTypes.array,
  grades: PropTypes.array,
  history: PropTypes.object
};

export default withRouter(AddClassForm);
