import React, { Component } from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";

export default class ConfirmModal extends Component {
  close = () => {
    this.props.onCloseModal();
  };

  confirmAction = () => {
    if (this.props.confirmAction) {
      this.props.confirmAction();
    }
    this.close();
  };

  renderHeader = () => {
    const {
      customProps: { useCustomHeader },
      headerText
    } = this.props;

    if (useCustomHeader) {
      return headerText;
    }

    return (
      <ModalHeader>
        <h3 className="w9 d-flex">
          <i className="fa fa-2x fa-warning text-warning pull-left" />
          <div>{headerText}</div>
        </h3>
      </ModalHeader>
    );
  };

  renderBodyText = () => {
    const { bodyText, isWarning } = this.props;
    if (
      (typeof bodyText === "string" && !bodyText.length) ||
      (typeof bodyText === "object" && !Object.keys(bodyText))
    ) {
      return null;
    }
    return <div className={isWarning ? "alert-error text-center" : ""}>{bodyText}</div>;
  };

  renderBodyQuestion = () => {
    const { bodyQuestion, isWarning } = this.props;
    if (!bodyQuestion.length) {
      return null;
    }
    return (
      <p>
        <strong className={isWarning ? "alert-error" : ""}>{bodyQuestion}</strong>
      </p>
    );
  };

  renderBody = () => {
    return (
      <ModalBody>
        {this.renderBodyText()}
        {this.renderBodyQuestion()}
      </ModalBody>
    );
  };

  renderConfirmButton = () => {
    const { confirmText, confirmButtonClassName } = this.props;
    if (!confirmText.length) {
      return null;
    }

    const buttonProps = {};

    if (confirmButtonClassName) {
      buttonProps.className = confirmButtonClassName;
    } else {
      buttonProps.variant = "success";
    }

    return (
      <Button {...buttonProps} onClick={this.confirmAction} data-testid="confirm-modal-btn">
        {confirmText}
      </Button>
    );
  };

  renderCancelButton = () => {
    const { cancelText, cancelButtonClassName } = this.props;
    if (!cancelText.length) {
      return null;
    }

    const buttonProps = {};

    if (cancelButtonClassName) {
      buttonProps.className = cancelButtonClassName;
    } else {
      buttonProps.variant = "default";
    }

    return (
      <Button {...buttonProps} onClick={this.close} data-testid="cancel-modal-btn">
        {cancelText}
      </Button>
    );
  };

  renderFooter = () => {
    return (
      <ModalFooter className="d-flex justify-content-center">
        {this.renderConfirmButton()}
        {this.renderCancelButton()}
      </ModalFooter>
    );
  };

  getModalClassName = () => {
    const { shouldUseCustomShadow, customModalClassName } = this.props.customProps;
    let modalClassName = `confirm-modal`;
    if (shouldUseCustomShadow) {
      modalClassName += " modal-shadow m-t-50";
    }
    if (customModalClassName) {
      modalClassName += ` ${customModalClassName}`;
    }
    return modalClassName;
  };

  render() {
    const {
      showModal,
      confirmText,
      size,
      customProps: { canCloseUsingBackdrop }
    } = this.props;
    const modalClassName = this.getModalClassName();
    return (
      <Modal
        show={showModal}
        onHide={this.close}
        dialogClassName={modalClassName}
        backdrop={!confirmText.length || canCloseUsingBackdrop ? true : "static"}
        data-testid="confirmModalDialog"
        size={size}
      >
        {this.renderHeader()}
        {this.renderBody()}
        {this.renderFooter()}
      </Modal>
    );
  }
}

ConfirmModal.propTypes = {
  showModal: PropTypes.bool.isRequired,
  confirmAction: PropTypes.func.isRequired,
  onCloseModal: PropTypes.func.isRequired,
  headerText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  bodyText: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  bodyQuestion: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  confirmText: PropTypes.string,
  cancelText: PropTypes.string,
  cancelButtonClassName: PropTypes.string,
  confirmButtonClassName: PropTypes.string,
  isWarning: PropTypes.bool,
  size: PropTypes.string,
  customProps: PropTypes.object
};

ConfirmModal.defaultProps = {
  headerText: "Are you sure?",
  bodyText: "",
  bodyQuestion: "Do you wish to continue?",
  confirmText: "Yes",
  cancelText: "No, Cancel",
  isWarning: false,
  customProps: {},
  cancelButtonClassName: "",
  confirmButtonClassName: "",
  confirmAction: () => {},
  onCloseModal: () => {}
};
