import { Meteor } from "meteor/meteor";
import React from "react";
import Dropzone from "react-dropzone";
import { difference, union, flatten, each } from "lodash";
import Papa from "papaparse";
import PropTypes from "prop-types";
import { withRout<PERSON>, <PERSON> } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import { getCSV } from "./upload/file-upload-utils";
import { decWaitingOn, incWaitingOn } from "/imports/api/loadingCounter/methods";
import { requiredStudentUploadFields, optionalStudentUploadFields, download } from "../../utilities";
import { RosterImportItems } from "/imports/api/rosterImportItems/rosterImportItems";
import RosterImportItemsHelpers from "/imports/api/rosterImportItems/methods";
import RosterImportItemsValidator from "/imports/api/rosterImportItems/rosterImportItemsValidator";
import { Users } from "/imports/api/users/users";
import { Sites } from "/imports/api/sites/sites";
import { Organizations } from "/imports/api/organizations/organizations";
import Loading from "../../components/loading";

const requiredFieldsExample = {
  StudentLocalID: "685123",
  StudentStateID: "3999000685555",
  StudentLastName: "Anderson",
  StudentFirstName: "Gail",
  StudentBirthDate: "2005-01-03"
};

class StudentsUpload extends React.Component {
  state = {
    data: [],
    parseMeta: {},
    fileName: "",
    errors: [],
    fileDropped: false
  };

  onDrop = files => {
    this.fileName = files[0] ? files[0].name : "No Name Found";
    incWaitingOn(1, "Parsing CSV!");
    Papa.parse(files[0], {
      header: true,
      skipEmptyLines: true,
      complete: this.parsingComplete
    });
  };

  parsingComplete = results => {
    const resultsWithTeacherData = { ...results };

    let { grade } = this.props.studentGroup;
    if (grade.includes("0") && grade !== "10") {
      [, grade] = this.props.studentGroup.grade;
    }

    resultsWithTeacherData.data = results.data.map(student => ({
      ...student,
      DistrictID: this.props.site.stateInformation.districtNumber,
      DistrictName: this.props.org.name,
      SchoolID: this.props.site.stateInformation.schoolNumber,
      SchoolName: this.props.site.name,
      TeacherID: this.props.teacher.profile.localId,
      TeacherLastName: this.props.teacher.profile.name.last,
      TeacherFirstName: this.props.teacher.profile.name.first,
      TeacherEmail: this.props.teacher.emails[0].address,
      ClassName: this.props.studentGroup.name,
      ClassSectionID: this.props.studentGroup.sectionId,
      SpringMathGrade: grade
    }));
    decWaitingOn();
    if (!(results.data && results.data.length > 0)) {
      this.setState({ fileDropped: true });
      return;
    }
    const missingFields = difference(requiredStudentUploadFields, results.meta.fields);
    const unsupportedFields = difference(results.meta.fields, [
      ...requiredStudentUploadFields,
      ...optionalStudentUploadFields
    ]);
    if (missingFields.length) {
      this.setInvalidFieldsError(missingFields);
      return;
    }
    if (unsupportedFields.length) {
      this.setInvalidFieldsError(unsupportedFields, true);
      return;
    }

    let errors = results.errors ? results.errors : [];

    this.validateAndSetData(resultsWithTeacherData.data, validationErrors => {
      this.setParseMeta(results.meta);
      this.setData(resultsWithTeacherData.data);
      this.setFileName(this.fileName);
      errors = union(results.errors, validationErrors);
      this.setErrors(errors);
      this.setState({ fileDropped: true });
    });
  };

  setInvalidFieldsError(invalidFields, unsupportedFields = false) {
    this.setFileName(this.fileName);
    const errorText = unsupportedFields
      ? "Unsupported fields found:\n"
      : "Your CSV file is missing the following fields:\n";
    this.setErrors([
      `${errorText}${invalidFields.join(
        "\n"
      )}\n\nPlease make sure you are using the latest CSV Template for File Uploads.`
    ]);
    this.setState({ fileDropped: true });
  }

  setParseMeta(parseMeta) {
    this.setState({ parseMeta });
  }

  setData(data) {
    this.setState({ data });
  }

  setFileName(fileName) {
    this.setState({ fileName });
  }

  setErrors(errors) {
    this.setState({ errors });
  }

  validateAndSetData(data, cb) {
    const errors = [];
    const students = new Map();
    const fileUploadLineItems = [];
    const processFileUploadLineItemFunctions = [];
    const schema = RosterImportItems.schemaStudentData;
    incWaitingOn(1, "Validating!");

    function processFileUploadLineItem(datum, index) {
      return new Promise(resolve => {
        const fuli = RosterImportItemsHelpers.createFromCSVDataRow(datum);
        fileUploadLineItems.push(fuli);
        try {
          schema.validate(fuli.data);
        } catch (e) {
          e.details.forEach(error => {
            const message = schema.messageForError(error);
            errors.push(`${message}. Please see row: ${index + 2}`);
          });

          resolve(false);
        }
        students.set(
          `${fuli.data.studentStateID}` +
            `${fuli.data.studentFirstName}` +
            `${fuli.data.studentLastName}` +
            `${fuli.data.studentLocalID}` +
            `${fuli.data.studentBirthDate}`,
          true
        );

        resolve(true);
      });
    }

    each(data, (datum, index) => {
      processFileUploadLineItemFunctions.push(processFileUploadLineItem(datum, index));
    });

    Promise.all(processFileUploadLineItemFunctions).then(async () => {
      const result = await new RosterImportItemsValidator(fileUploadLineItems).validate();

      if (!result.success) {
        const extraErrors = flatten(Object.values(result.errors));
        errors.push(...extraErrors);
      }
      const studentLocalStateIds = data.map(student => ({
        localId: student.StudentLocalID,
        stateId: student.StudentStateID
      }));

      Meteor.call(
        "RosterImports:CheckIfLocalAndStateIdsAreUnique",
        studentLocalStateIds,
        this.props.site.orgid,
        (err, res) => {
          if (!err) {
            res.forEach(duplicatedIdError => {
              errors.push(duplicatedIdError);
            });
            decWaitingOn();
            cb(errors);
          }
        }
      );
    });
  }

  handleCancel = () => {
    this.setData([]);
    this.setFileName("");
    this.setState({ fileDropped: false });
    if (this.props.isNewGroup) {
      this.goBackToSchool();
    }
  };

  handleSubmit = event => {
    event.preventDefault();
    this.props.uploadStudents({
      parseMeta: this.state.parseMeta,
      data: this.state.data
    });
  };

  goBackToSchool = () => {
    this.props.history.push(
      `/data-admin/manage-group/students/${this.props.studentGroup.orgid}/site/${this.props.studentGroup.siteId}/${this.props.studentGroup._id}`
    );
  };

  renderUploadGuidelines = () => (
    <div id="resultsArea" className="col-12">
      <div className="animated fadeIn">
        <h3 className="w7">Student file guidelines:</h3>
        <ol className="file-upload-guidelines">
          <li>Student files must be in a .CSV (comma separated value text file) format.</li>
          <li>
            <strong>Upload will allow you to add new students, without changing the current list.</strong>
          </li>
          <li>
            The file must contain values in every record for the mandatory fields. See the{" "}
            <Link to="/assets/Description of Variables_Manage Upload Fields.pdf" target="_blank">
              Description of variables
            </Link>{" "}
            for more information.
          </li>
        </ol>
      </div>
      {this.props.isNewGroup ? (
        <fieldset className="form-group">
          <div className="row">
            <div className="col-4 offset-8 pull-right">
              <button type="submit" className="btn btn-primary form-control" onClick={this.goBackToSchool}>
                Manually Add Students
              </button>
            </div>
          </div>
        </fieldset>
      ) : null}
    </div>
  );

  saveErrorsToFile() {
    const fileNameWithoutExtension =
      this.state.fileName.substring(0, this.state.fileName.lastIndexOf(".")) || this.state.fileName;
    const filename = `${fileNameWithoutExtension}_ERRORS.txt`;
    const errors = this.state.errors.map(error => error).join("\n\n");
    const hrefData = `data:text/plain;charset=utf-8,${encodeURIComponent(errors)}`;
    download({ filename, hrefData });
  }

  render() {
    if (this.props.loading) {
      return <Loading />;
    }

    return (
      <div>
        <div className="row">
          <div className="col-6 offset-3">
            <div className="card-box">
              <div className="row">
                <div className="col-12">
                  <Dropzone id="dz1" className="alert alert-success text-xs-center" onDrop={this.onDrop} disablePreview>
                    <div className="text-center">
                      {this.state.fileName ? (
                        <h3 className="animated fadeIn drop-zone-file-name">{this.state.fileName}</h3>
                      ) : (
                        <div className="animated fadeIn">
                          <span>
                            Try dropping your file here,
                            <br />
                            or click to select a file to upload.
                          </span>
                        </div>
                      )}
                    </div>
                  </Dropzone>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="row">
          <div className="col-12">
            <p htmlFor="exampleSelect1">Would you like a helper file to know how to layout your data?</p>
            <a download="CSVHelperFile.csv" href={`data:application/octet-stream,${getCSV([requiredFieldsExample])}`}>
              Download CSV Template for File Uploads
            </a>
            <br />
            <Link to="/assets/Description of Variables_Manage Upload Fields.pdf" target="_blank">
              Description of variables
            </Link>
          </div>
          {!this.state.fileDropped
            ? this.renderUploadGuidelines()
            : (() => {
                if (this.state.data.length < 1 || this.state.errors.length > 0) {
                  if (this.state.errors.length > 0) {
                    const errors = this.state.errors.map(error => error).join("\n\n");
                    const newLinesCount = errors.match(/\n/g) ? errors.match(/\n/g).length : 0;
                    const rows = Math.min(newLinesCount + 1, 40);
                    return (
                      <div id="resultsArea" className="col-12">
                        <div className="animated fadeIn">
                          <div className="pull-right">
                            <button className="btn btn-primary" onClick={this.saveErrorsToFile.bind(this)}>
                              Save errors to file
                            </button>
                          </div>
                          <h2 className="w7">Sorry</h2>
                          <p> The upload contained errors: </p>
                          <textarea disabled className="form-control alert alert-danger" rows={rows} value={errors} />
                        </div>
                      </div>
                    );
                  }
                  return (
                    <div id="resultsArea" className="col-9">
                      <div className="">
                        <h2 className="w7">Sorry</h2>
                        <p> The upload tool found no parsable data. </p>
                      </div>
                    </div>
                  );
                }
                return (
                  <div id="resultsArea" className="col-12">
                    <div className="animated fadeIn">
                      <h3 id="congratulationsHeader" className="w7">
                        Congratulations!
                      </h3>
                      <p>
                        {" "}
                        Nice job on the upload. The data looks good to us! Below is a list of students who we are going
                        to insert into the app.
                      </p>
                      <div className="row">
                        <table className="table table-condensed">
                          <thead>
                            <tr>
                              <th>Name</th>
                              <th>Date of Birth</th>
                              <th>Local ID</th>
                              <th>State ID</th>
                              <th />
                            </tr>
                          </thead>
                          <tbody>
                            {this.state.data.map(student => (
                              <tr key={student.StudentLocalID} data-testid="studentUploadRow">
                                <td>
                                  {student.StudentFirstName} {student.StudentLastName}
                                </td>
                                <td>{student.StudentBirthDate}</td>
                                <td>{student.StudentLocalID}</td>
                                <td>{student.StudentStateID}</td>
                                <td />
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <fieldset className="form-group">
                        <div className="row justify-content-between">
                          <div className="col-4">
                            <button type="button" className="btn btn-danger form-control" onClick={this.handleCancel}>
                              Cancel
                            </button>
                          </div>
                          <div className="col-4">
                            <button type="submit" className="btn btn-primary form-control" onClick={this.handleSubmit}>
                              Finalize Upload
                            </button>
                          </div>
                        </div>
                      </fieldset>
                    </div>
                  </div>
                );
              })()}
        </div>
      </div>
    );
  }
}

export default withTracker(props => {
  const { orgid, siteId } = props.studentGroup;
  const usersHandler = Meteor.subscribe("Users", { orgid });
  const sitesHandler = Meteor.subscribe("Sites", orgid, siteId);
  const orgHandler = Meteor.subscribe("Organizations", orgid);
  const loading = !usersHandler.ready() && !sitesHandler.ready() && !orgHandler.ready();
  let teacher = {};
  let site = {};
  let org = {};
  if (!loading) {
    teacher = Users.findOne(props.studentGroup.ownerIds[0]);
    site = Sites.findOne(siteId);
    org = Organizations.findOne(orgid);
  }
  return { teacher, loading, site, org };
})(withRouter(StudentsUpload));

StudentsUpload.propTypes = {
  history: PropTypes.object,
  isNewGroup: PropTypes.bool,
  loading: PropTypes.bool,
  site: PropTypes.object,
  studentGroup: PropTypes.object,
  teacher: PropTypes.object.isRequired,
  uploadStudents: PropTypes.func,
  org: PropTypes.PropTypes.object
};
