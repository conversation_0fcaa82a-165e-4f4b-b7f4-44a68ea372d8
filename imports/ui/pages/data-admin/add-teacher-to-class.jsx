import React, { Component } from "react";
import PropTypes from "prop-types";
import { with<PERSON>out<PERSON>, <PERSON> } from "react-router-dom";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import { every } from "lodash";
import Alert from "react-s-alert";

import { isExternalRostering } from "/imports/ui/utilities";
import Header from "/imports/ui/components/admin-view/admin-header";

import { Sites } from "/imports/api/sites/sites";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { Organizations } from "/imports/api/organizations/organizations";
import { getCurrentSchoolYear, getMeteorUser, trimValuesInObject } from "/imports/api/utilities/utilities";

import AddTeacherForm from "./add-teacher-form";
import SelectClassForm from "./select-class-form";
import ConfirmModal from "./confirm-modal";
import { isSingleSchoolDataAdmin } from "./utilities";
import Loading from "../../components/loading";

const defaultTeacher = { lastName: "", firstName: "", localId: "", email: "" };

class AddTeacherToClass extends Component {
  constructor(props) {
    super(props);
    this.state = {
      newTeacher: defaultTeacher,
      currentStudentGroup: {
        name: "Select a class",
        sectionId: "",
        grade: ""
      },
      teacherType: "",
      isSending: false,
      isModalOpen: false,
      isTeacherFormValid: false
    };
  }

  backToManageTeachers = () => {
    this.props.history.push(`/data-admin/manage-accounts/${this.props.orgid}`);
  };

  updateNewTeacherData = (updatedData, isTeacherFormValid = false) => {
    this.setState(state => ({
      ...state,
      newTeacher: { ...state.newTeacher, ...updatedData },
      isTeacherFormValid
    }));
  };

  updateCurrentStudentGroup = updatedData => {
    this.setState(state => ({
      ...state,
      currentStudentGroup: { ...state.currentStudentGroup, ...updatedData }
    }));
  };

  updateTeacherType = updatedTeacherType => {
    this.setState({ teacherType: updatedTeacherType });
  };

  isFormValid = () => {
    const { currentStudentGroup, newTeacher, teacherType } = this.state;
    const isValidTeacher = every(newTeacher);
    const isValidClass =
      currentStudentGroup.name && currentStudentGroup.sectionId && currentStudentGroup.grade && teacherType;
    return isValidTeacher && isValidClass;
  };

  addTeacher = () => {
    if (this.isFormValid()) {
      const { newTeacher, currentStudentGroup, teacherType } = this.state;
      const teacher = trimValuesInObject(newTeacher);
      Meteor.call(
        "addTeacher",
        {
          teacher,
          orgid: currentStudentGroup.orgid,
          siteId: currentStudentGroup.siteId,
          schoolYear: currentStudentGroup.schoolYear
        },
        (addTeacherErr, userId) => {
          if (!addTeacherErr) {
            const currentSecondaryTeachers = currentStudentGroup.secondaryTeachers || [];
            if (teacherType === "Primary") {
              currentSecondaryTeachers.push(currentStudentGroup.ownerIds[0]);
            } else if (teacherType === "Secondary") {
              currentSecondaryTeachers.push(userId);
            }

            Meteor.call(
              "saveGroupData",
              {
                studentGroupId: currentStudentGroup._id,
                newTeacherId: userId,
                orgid: currentStudentGroup.orgid,
                siteId: currentStudentGroup.siteId,
                studentGroupName: currentStudentGroup.name,
                secondaryTeachers: currentSecondaryTeachers.filter(t => t), // filter out falsy values
                grade: currentStudentGroup.grade,
                hasGradeChanged: false,
                hasPrimaryTeacherChanged: teacherType === "Primary"
              },
              saveGroupDataErr => {
                if (!saveGroupDataErr) {
                  Alert.success(
                    `New ${teacherType.toLowerCase()} teacher successfully added to ${
                      currentStudentGroup.name
                    } student group`,
                    { timeout: 5000 }
                  );
                  this.backToManageTeachers();
                } else {
                  Alert.error(saveGroupDataErr.message, { timeout: 5000 });
                }
              }
            );
          } else {
            Alert.error(addTeacherErr.message, { timeout: 5000 });
          }
          this.setState({ isSending: false });
        }
      );
      this.setState({ isSending: true });
    }
  };

  closeModal = () => {
    this.setState({ isModalOpen: false });
  };

  openModal = () => {
    this.setState({ isModalOpen: true });
  };

  onAddTeacher = () => {
    if (!this.state.isTeacherFormValid) {
      Alert.error("Please fix teacher form before proceeding", { timeout: 3000 });
    } else if (this.isFormValid()) {
      if (this.state.teacherType === "Primary") {
        this.openModal();
      } else {
        this.addTeacher();
      }
    }
  };

  render() {
    const { loading } = this.props;
    if (loading) {
      return <Loading />;
    }
    const { studentGroups, orgid, org, siteId } = this.props;
    return (
      <div className="workspace-container">
        <div className="d-flex mt-1">
          {!isSingleSchoolDataAdmin() ? (
            <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/dashboard/${orgid}`}>
              <i className="fa fa-caret-left" /> Back to All Schools
            </Link>
          ) : null}
          {siteId && siteId !== "undefined" ? (
            <Link
              className="btn btn-outline-blue btn-xs me-2"
              to={`/data-admin/manage-group/students/${orgid}/site/${siteId}`}
            >
              <i className="fa fa-caret-left" /> Back to School
            </Link>
          ) : null}
          <Link className="btn btn-outline-blue btn-xs me-2" to={`/data-admin/manage-accounts/${orgid}`}>
            <i className="fa fa-caret-left" /> Back to Manage Accounts
          </Link>
        </div>

        <Header keyLabel="schoolOverview" headerTitle="Add Teacher" />

        <div className="card-box-wrapper mt-2">
          <div className="p-3">
            <div className="card-box">
              {isExternalRostering(org?.rostering) && (
                <div className="alert alert-warning">
                  Auto-rostering is currently enabled. The teacher being added may be removed during the next roster
                  sync.
                </div>
              )}
              <div className="addTeacherBackground">
                <AddTeacherForm teacher={this.state.newTeacher} onChange={this.updateNewTeacherData} orgid={orgid} />
                <SelectClassForm
                  currentStudentGroup={this.state.currentStudentGroup}
                  studentGroups={studentGroups}
                  teacherType={this.state.teacherType}
                  onSelectTeacherType={this.updateTeacherType}
                  onSelectCurrentStudentGroup={this.updateCurrentStudentGroup}
                />
              </div>
              {this.state.isSending ? (
                <React.Fragment>
                  <Loading inline={true} />
                </React.Fragment>
              ) : (
                <React.Fragment>
                  <button
                    className="btn btn-outline-blue btn-wide btn-xs me-2"
                    onClick={this.onAddTeacher}
                    disabled={!this.isFormValid()}
                  >
                    Add teacher to the selected student group
                  </button>
                  {this.state.isModalOpen ? (
                    <ConfirmModal
                      showModal={this.state.isModalOpen}
                      onCloseModal={this.closeModal}
                      confirmAction={this.addTeacher}
                      headerText={`Are you sure you want to add a new ${this.state.teacherType.toLowerCase()} to ${
                        this.state.currentStudentGroup.name
                      } student group?`}
                      bodyQuestion={
                        "By making a new primary teacher, the current primary teacher will become a secondary teacher."
                      }
                      confirmText="Yes, add new teacher"
                    />
                  ) : null}
                </React.Fragment>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

AddTeacherToClass.propTypes = {
  loading: PropTypes.bool,
  orgid: PropTypes.string,
  siteId: PropTypes.string,
  teachers: PropTypes.array,
  grades: PropTypes.array,
  studentGroups: PropTypes.array,
  org: PropTypes.object,
  history: PropTypes.object
};

export default withTracker(({ orgid, siteId }) => {
  const curUser = getMeteorUser();
  const schoolYear = getCurrentSchoolYear(curUser, orgid);
  const sitesHandler = Meteor.subscribe("Sites", orgid);
  const studentGroupsHandler = Meteor.subscribe("StudentGroups:PerOrg", orgid, schoolYear);
  const org = Organizations.findOne({ _id: orgid }, { fields: { rostering: 1 } });

  const loading = sitesHandler.ready() && !studentGroupsHandler.ready();

  let grades = [];
  let studentGroups = [];

  if (!loading) {
    const site = Sites.findOne({ _id: siteId });

    grades = site && site.grades;
    studentGroups = StudentGroups.find({ siteId }, { fields: { history: 0 } }).fetch();
  }

  return {
    loading,
    grades,
    org,
    studentGroups
  };
})(withRouter(AddTeacherToClass));
