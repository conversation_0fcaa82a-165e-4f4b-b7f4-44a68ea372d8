import PropTypes from "prop-types";
import React from "react";
import { Link } from "react-router-dom";
import * as utils from "/imports/api/utilities/utilities";

export default function ContinueScreeningNotice(props) {
  const { screeningTitle, studentGroup, benchmarkPeriodId } = props;
  const link =
    `/site/${studentGroup.siteId}/student-groups/` +
    `${studentGroup._id}/${utils.dashboardNavs.screening}/form${benchmarkPeriodId ? `/${benchmarkPeriodId}` : ""}`;
  return (
    <div
      className="information-box information-box-notice"
      key={benchmarkPeriodId ? `continuePreviousScreeningNotice-${benchmarkPeriodId}` : "continueScreeningNotice"}
    >
      <h4 className="w7 text-center">{screeningTitle}</h4>
      <div className="text-center">
        <p className="small-details">
          {`Screening has been started, please continue screening the class to determine your students' specific needs.`}
        </p>
        <Link data-testid="continue-button" className="btn btn-success btn-center" to={link}>
          Continue {screeningTitle}
        </Link>
      </div>
    </div>
  );
}

ContinueScreeningNotice.defaultProps = {
  benchmarkPeriodId: null
};

ContinueScreeningNotice.propTypes = {
  screeningTitle: PropTypes.string,
  studentGroup: PropTypes.object,
  benchmarkPeriodId: PropTypes.string
};
