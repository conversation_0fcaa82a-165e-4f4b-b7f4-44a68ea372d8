import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import chunk from "lodash/chunk";
import { Meteor } from "meteor/meteor";
import Alert from "react-s-alert";
import { isEmpty } from "lodash";
import { AssessmentGrowth } from "/imports/api/assessmentGrowth/assessmentGrowth";
import { Assessments } from "/imports/api/assessments/assessments";
import { getGrowthResults } from "/imports/api/assessmentGrowth/utilities";
import { Loading } from "../../components/loading";
import { GrowthChartWrapper } from "./growth-chart-wrapper";
import Classroom from "../classroom/classroom";
import PageHeader from "../../components/page-header";
import { isOnPrintPage } from "../../utilities";

function Growth(props) {
  // eslint-disable-next-line prefer-const
  let { growthChartResults, loading, error } = props;
  if (loading) {
    return <Loading />;
  }
  if (error) {
    return <h3 className="text-center">{error}</h3>;
  }

  if (isEmpty(growthChartResults) && props.studentGroup.grade !== "HS") {
    return <h3 className="text-center">No growth data available</h3>;
  }

  const [growthStats, setGrowthStats] = useState(growthChartResults);

  useEffect(() => {
    if (props.studentGroup.grade === "HS") {
      Meteor.call(
        "Growth:getHSClasswideInterventionProgress",
        {
          orgid: props.studentGroup.orgid,
          studentGroupIds: [props.studentGroup._id],
          siteId: props.studentGroup.siteId
        },
        (err, res) => {
          if (err) {
            Alert.error(err.message);
            error = "Error getting Growth chart data";
          } else {
            setGrowthStats(res);
          }
        }
      );
    }
  }, []);

  const isPrinting = isOnPrintPage();
  const title = `Growth for ${props.studentGroup.name} in ${props.schoolYear - 1}-${props.schoolYear % 100}`;
  return (
    <React.Fragment>
      {isPrinting ? <PageHeader title={title} /> : null}
      <div className="studentDetailContent">
        <Classroom
          shouldDisplayClasswideInterventionTableOnly={true}
          studentGroup={props.studentGroup}
          inActiveSchoolYear={props.inActiveSchoolYear}
          isReadOnly={props.isReadOnly}
          schoolYear={props.schoolYear}
        />
        {props.studentGroup.grade !== "HS" ? (
          <>
            <GrowthChartWrapper
              comparisonPeriod={"fall"}
              growthChartResults={growthStats}
              grade={props.studentGroup.grade}
            />
            <GrowthChartWrapper
              comparisonPeriod={"spring"}
              growthChartResults={growthStats}
              grade={props.studentGroup.grade}
            />
          </>
        ) : (
          chunk(growthStats, 4).map((statChunk, index) => {
            return (
              <GrowthChartWrapper
                comparisonPeriod={"all"}
                growthChartResults={statChunk}
                grade={props.studentGroup.grade}
                key={`growthStats${index}`}
                chartId={`growth_All_HS${index}`}
                noChartTitleAndLegend={index > 0}
              />
            );
          })
        )}
      </div>
    </React.Fragment>
  );
}

Growth.propTypes = {
  studentGroup: PropTypes.object,
  growthChartResults: PropTypes.object,
  loading: PropTypes.bool,
  error: PropTypes.string,
  inActiveSchoolYear: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  schoolYear: PropTypes.number
};

export default withTracker(({ studentGroup }) => {
  let error = null;
  let growthChartResults = {};
  const growthSub = Meteor.subscribe("AssessmentGrowth", studentGroup.grade);
  const assessmentSub = Meteor.subscribe("Assessment:Names");
  const loading = !growthSub.ready() || !assessmentSub.ready();
  if (!loading) {
    const assessments = Assessments.find({}, { fields: { name: 1 } }).fetch();
    const assessmentComparisonMap = AssessmentGrowth.findOne();
    if (studentGroup.grade !== "HS") {
      try {
        growthChartResults = getGrowthResults({
          history: studentGroup.history,
          assessmentComparisonMap,
          assessments
        });
      } catch (e) {
        if (new RegExp(/getGrowthResults/).test(e.message)) {
          error = e.message;
        } else {
          error = "Error getting Growth chart data";
        }
      }
    }
  }
  return {
    loading,
    growthChartResults,
    error
  };
})(Growth);
