import React from "react";
import MockDate from "mockdate";
import { fireEvent, cleanup, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import _ from "lodash";
import { Meteor } from "meteor/meteor";
import { Tracker } from "meteor/tracker";
import StudentList from "./student-list.jsx";
import { ClassContext } from "../classContext";
import * as helpers from "../../components/student-groups/helperFunction";
import { getStudentListContext, getStudentListProps } from "../../../test-helpers/data/student-list.testData";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";
import { user } from "../../../test-helpers/data/users";

// Mock only what's needed for AppDataUserWrapper and StudentList
jest.mock("../../../api/utilities/utilities", () => ({
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => user("Test", "User", "teacher")),
  getMeteorUserSync: jest.fn(() => user("Test", "User", "teacher")),
  getOrgIdSync: jest.fn(() => "test_organization_id")
}));

jest.mock("../../../api/roles/methods", () => ({
  isAdminOrUniversalCoach: jest.fn(() => true)
}));

// Mock Meteor methods used by components
Meteor.user = jest.fn(() => user("Test", "User", "teacher"));
Meteor.call = jest.fn((method, params, callback) => {
  const cb = typeof params === "function" ? params : callback;
  cb(null, method === "getEnvironmentVariables" ? { METEOR_ENVIRONMENT: "test" } : []);
});

// Make Tracker.autorun work synchronously in tests
Tracker.autorun = jest.fn(fn => {
  fn();
  return { stop: jest.fn() };
});

// Mock localStorage for AppDataUserWrapper
Object.defineProperty(window, "localStorage", {
  value: { getItem: jest.fn(() => null) },
  writable: true
});

// Helper function to render StudentList and wait for loading to complete
async function renderStudentListAndWaitForLoad(
  studentListProps,
  context = getStudentListContext("defaultStudentGroup")
) {
  const renderResult = renderWithRouter(
    <ClassContext.Provider value={context}>
      <StudentList {...studentListProps} />
    </ClassContext.Provider>
  );

  // Wait for the loading spinner to disappear
  await waitFor(
    () => {
      expect(renderResult.queryByTestId("loading-icon")).not.toBeInTheDocument();
    },
    { timeout: 5000 }
  );

  return renderResult;
}

describe("StudentList", () => {
  beforeAll(() => {
    MockDate.set("2018-12-20");
  });
  afterAll(() => {
    jest.restoreAllMocks();
    MockDate.reset();
  });
  afterEach(cleanup);
  describe("Roster", () => {
    const suggestedPairingButtonText = "View Suggested Student Pairings";
    const newPairsButtonText = "New Pairs";

    it("should render all students in group", async () => {
      const { students } = getStudentListContext("defaultStudentGroup");
      const studentListProps = getStudentListProps("defaultStudentGroup");
      const { getByText, getAllByText } = await renderStudentListAndWaitForLoad(studentListProps);

      expect(getByText("Roster")).toBeVisible();
      students.forEach(student => {
        getAllByText(helpers.getStudentName(student)).forEach(el => {
          expect(el).toBeVisible();
        });
        expect(getByText(student.identity.identification.localId)).toBeVisible();
      });
    });
    describe("for new small group pairings", () => {
      describe("student group with benchmark and classwide history items", () => {
        it("should display small group pairing based on classwide history item by default", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");

          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4]
          ];
          const customClasswideScores = [[3, 4, 5, 2, 1]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, customStudentGroup.history[0], 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Prather, Alexis", "Turner, Taylor"], ["Barber, Wesley", "Teacher"]);
        });
        it("should display new pairs button if there are new pairs available", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4]
          ];
          const customClasswideScores = [[5, 4, 3, 2, 1]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          expect(getByText(newPairsButtonText)).toBeVisible();
        });
        it("should not display new pairs button if there are no new pairs available", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [2, 3, 4, 5, 6]
          ];
          const customClasswideScores = [[1, 2, 3, 4, 5]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, queryByText } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          expect(queryByText(newPairsButtonText)).toBe(null);
        });
        it("should get new pairs between most recent classwide history item and measure 1", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [3, 2, 4, 5, 0]
          ];
          const customClasswideScores = [[3, 2, 4, 5, 0]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, customStudentGroup.history[0], 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Turner, Taylor", "Griffes, Liem"], ["Barber, Wesley", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          const sortedStudentScoresUpdated = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 0);
          assertCorrectStudentPairs(sortedStudentScoresUpdated, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Turner, Taylor", "Griffes, Liem"], ["Barber, Wesley", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);
        });
        it("should get new pairs between most recent classwide history item and measure 2", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
          const customBenchmarkScores = [
            [4, 3, 5, 6, 1],
            [0, 1, 2, 3, 4]
          ];
          const customClasswideScores = [[3, 2, 4, 5, 0]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, customStudentGroup.history[0], 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Turner, Taylor", "Griffes, Liem"], ["Barber, Wesley", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          const sortedStudentScoresUpdated = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 1);
          assertCorrectStudentPairs(sortedStudentScoresUpdated, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Turner, Taylor", "Griffes, Liem"], ["Barber, Wesley", "Teacher"]);
        });
        it("should get new pairs between most recent classwide history item and measure 1 - edge case 1", async () => {
          const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
          const customBenchmarkScores = [
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0]
          ];
          const customClasswideScores = [[1, 2, 3, 4, 5]];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores,
            classwideScores: customClasswideScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, customStudentGroup.history[0], 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          const sortedStudentScoresUpdated = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 1);
          assertCorrectStudentPairs(sortedStudentScoresUpdated, getByTestId);
          comparePairs(getByTestId, ["Barber, Wesley", "Turner, Taylor"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);
        });
      });
      describe("student group with benchmark history item and without classwide history item", () => {
        it("should display small group pairing based on benchmark history item by default", async () => {
          const { students, studentGroup } = getStudentListContext("studentGroupWithBenchmarkHistory");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4],
            [0, 1, 2, 3, 4]
          ];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);
        });
        it("should display new pairs button if there are new pairs available", async () => {
          const { students, studentGroup } = getStudentListContext("studentGroupWithBenchmarkHistory");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [6, 5, 4, 3, 2],
            [1, 2, 3, 4, 5]
          ];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          expect(getByText(newPairsButtonText)).toBeVisible();
        });
        it("should not display new pairs button if there are no new pairs available", async () => {
          const { students, studentGroup } = getStudentListContext("studentGroupWithBenchmarkHistory");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [2, 3, 4, 5, 6],
            [1, 2, 3, 4, 5]
          ];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, queryByText } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          expect(queryByText(newPairsButtonText)).toBe(null);
        });
        it("should get new pairs between measure 1 and measure 2", async () => {
          const { students, studentGroup } = getStudentListContext("studentGroupWithBenchmarkHistory");
          const customBenchmarkScores = [
            [0, 1, 2, 3, 4],
            [6, 2, 1, 4, 5],
            [1, 2, 3, 4, 5]
          ];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          const sortedStudentScoresUpdated = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 1);
          assertCorrectStudentPairs(sortedStudentScoresUpdated, getByTestId);
          comparePairs(getByTestId, ["Barber, Wesley", "Griffes, Liem"], ["Turner, Taylor", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Barber, Wesley", "Griffes, Liem"], ["Turner, Taylor", "Teacher"]);
        });
        it("should get new pairs between measure 1 and measure 3", async () => {
          const { students, studentGroup } = getStudentListContext("studentGroupWithBenchmarkHistory");
          const customBenchmarkScores = [
            [1, 2, 3, 4, 5],
            [0, 1, 2, 3, 4],
            [6, 2, 1, 4, 5] // different order
          ];
          const customStudentGroup = updateStudentGroupHistoryScores({
            studentGroup,
            benchmarkScores: customBenchmarkScores
          });
          const studentListProps = getStudentListProps("", customStudentGroup);
          const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, {
            students,
            studentGroup: customStudentGroup,
            studentsWithIndividualRuleNotProcessed: []
          });

          fireEvent.click(getByText(suggestedPairingButtonText));
          const sortedStudentScoresInitial = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 0);
          assertCorrectStudentPairs(sortedStudentScoresInitial, getByTestId);
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          const sortedStudentScoresUpdated = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory, 2);
          assertCorrectStudentPairs(sortedStudentScoresUpdated, getByTestId);
          comparePairs(getByTestId, ["Barber, Wesley", "Griffes, Liem"], ["Turner, Taylor", "Teacher"]);

          fireEvent.click(getByText(newPairsButtonText));
          comparePairs(getByTestId, ["Ward, Ethan", "Griffes, Liem"], ["Prather, Alexis", "Teacher"]);
        });
      });
    });
    describe("for an odd number of students", () => {
      const { students, studentGroup } = getStudentListContext("defaultStudentGroup");
      const studentListProps = getStudentListProps("defaultStudentGroup");
      it("should display suggested student pairings in the correct order (i.e. class is sorted from best to worst score; pair up best student from upper half with best student from bottom half, etc.)", async () => {
        const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps);

        fireEvent.click(getByText(suggestedPairingButtonText));

        const sortedStudentScores = getSortedScoresFor(students, studentGroup.history[0]);
        assertCorrectStudentPairs(sortedStudentScores, getByTestId);
      });
      it("should display different student pairings upon request if a recent classwide history is available", async () => {
        const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps);

        fireEvent.click(getByText(suggestedPairingButtonText));
        fireEvent.click(getByText(newPairsButtonText));

        const sortedStudentScores = getSortedScoresFor(students, studentListProps.lastBenchmarkHistory);
        assertCorrectStudentPairs(sortedStudentScores, getByTestId);
      });
    });
    describe("for an even number of students", () => {
      const { studentGroup } = getStudentListContext("defaultStudentGroup");
      const studentListProps = getStudentListProps("defaultStudentGroup");
      const currentContext = getStudentListContext();
      const filteredStudents = currentContext.students.filter(student => student._id !== "testStudentId4");
      const updatedContext = {
        ...currentContext,
        students: filteredStudents,
        studentGroup: { ...studentGroup, flaggedForRemovalStudentIds: [] }
      };
      it("should display suggested student pairings in the correct order (i.e. class is sorted from best to worst score; pair up best student from upper half with best student from bottom half, etc.)", async () => {
        const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, updatedContext);

        fireEvent.click(getByText(suggestedPairingButtonText));

        const sortedStudentScores = getSortedScoresFor(filteredStudents, studentGroup.history[0]);
        assertCorrectStudentPairs(sortedStudentScores, getByTestId);
      });
      it("should display different student pairings upon request if a recent classwide history is available", async () => {
        const { getByText, getByTestId } = await renderStudentListAndWaitForLoad(studentListProps, updatedContext);

        fireEvent.click(getByText(suggestedPairingButtonText));
        fireEvent.click(getByText(newPairsButtonText));

        const sortedStudentScores = getSortedScoresFor(filteredStudents, studentListProps.lastBenchmarkHistory);
        assertCorrectStudentPairs(sortedStudentScores, getByTestId);
      });
    });
  });
  describe("Recommendation queue", () => {
    describe("Should order students correctly", () => {
      const instructionalTargets = [4, 7, 10];
      it("when there are no measures below target", () => {
        const studentRecommendationData = generateStudentRecommendationData(
          [
            { studentName: "B", scores: [5, 8, 11] },
            { studentName: "A", scores: [6, 9, 12] },
            { studentName: "C", scores: [7, 10, 13] }
          ],
          instructionalTargets
        );
        expect(helpers.sortStudentsByRecommendationData(studentRecommendationData)).toEqual(
          generateStudentRecommendationData(
            [
              { studentName: "A", scores: [6, 9, 12] },
              { studentName: "B", scores: [5, 8, 11] },
              { studentName: "C", scores: [7, 10, 13] }
            ],
            instructionalTargets
          )
        );
      });
      it("when all of them failed the same measure", () => {
        const studentRecommendationData = generateStudentRecommendationData(
          [
            { studentName: "A", scores: [3, 9, 12] },
            { studentName: "C", scores: [1, 7, 10] },
            { studentName: "B", scores: [2, 8, 11] }
          ],
          instructionalTargets
        );
        expect(helpers.sortStudentsByRecommendationData(studentRecommendationData)).toEqual(
          generateStudentRecommendationData(
            [
              { studentName: "C", scores: [1, 7, 10] },
              { studentName: "B", scores: [2, 8, 11] },
              { studentName: "A", scores: [3, 9, 12] }
            ],
            instructionalTargets
          )
        );
      });
      it("when all measures are below target", () => {
        const studentRecommendationData = generateStudentRecommendationData(
          [
            { studentName: "B", scores: [2, 4, 9] },
            { studentName: "A", scores: [3, 6, 8] },
            { studentName: "C", scores: [1, 5, 7] }
          ],
          instructionalTargets
        );
        expect(helpers.sortStudentsByRecommendationData(studentRecommendationData)).toEqual(
          generateStudentRecommendationData(
            [
              { studentName: "C", scores: [1, 5, 7] },
              { studentName: "B", scores: [2, 4, 9] },
              { studentName: "A", scores: [3, 6, 8] }
            ],
            instructionalTargets
          )
        );
      });
      it("when all of them failed different measures", () => {
        const studentRecommendationData = generateStudentRecommendationData(
          [
            { studentName: "A", scores: [5, 6, 11] },
            { studentName: "C", scores: [6, 9, 9] },
            { studentName: "B", scores: [2, 8, 12] }
          ],
          instructionalTargets
        );
        expect(helpers.sortStudentsByRecommendationData(studentRecommendationData)).toEqual(
          generateStudentRecommendationData(
            [
              { studentName: "B", scores: [2, 8, 12] },
              { studentName: "A", scores: [5, 6, 11] },
              { studentName: "C", scores: [6, 9, 9] }
            ],
            instructionalTargets
          )
        );
      });
      it("when one of them failed a different measure than others", () => {
        const studentRecommendationData = generateStudentRecommendationData(
          [
            { studentName: "A", scores: [6, 4, 12] },
            { studentName: "C", scores: [5, 6, 11] },
            { studentName: "B", scores: [2, 8, 11] }
          ],
          instructionalTargets
        );
        expect(helpers.sortStudentsByRecommendationData(studentRecommendationData)).toEqual(
          generateStudentRecommendationData(
            [
              { studentName: "B", scores: [2, 8, 11] },
              { studentName: "A", scores: [6, 4, 12] },
              { studentName: "C", scores: [5, 6, 11] }
            ],
            instructionalTargets
          )
        );
      });
    });
  });
});

function updateStudentGroupHistoryScores({ studentGroup, benchmarkScores = [], classwideScores = [] }) {
  return {
    ...studentGroup,
    history: studentGroup.history
      .map(historyItem => {
        const scoresToUse = historyItem.type === "benchmark" ? benchmarkScores : classwideScores;
        return {
          ...historyItem,
          assessmentResultMeasures: historyItem.assessmentResultMeasures
            .map((arm, armIndex) => {
              const studentResultScores = scoresToUse[armIndex];
              return {
                ...arm,
                studentResults: arm.studentResults.map((sr, srIndex) => {
                  return { ...sr, score: studentResultScores[srIndex].toString() };
                })
              };
            })
            .flat(1)
        };
      })
      .flat(1)
  };
}

function getStudentGroupsForPairings(students, maxGroupSize) {
  const [firstGroup = [], secondGroup = []] = [students.slice(0, maxGroupSize), students.slice(maxGroupSize)];
  return {
    firstGroup,
    secondGroup
  };
}

function assertCorrectStudentPairs(students, getByTestId) {
  const maxGroupSize = Math.ceil(students.length / 2);
  const { firstGroup, secondGroup } = getStudentGroupsForPairings(students, maxGroupSize);
  firstGroup.forEach((student, index) => {
    const studentPair = getByTestId(`studentPair_${index}`);
    expect(studentPair).toBeVisible();
    const firstStudent = getStudentName(student);
    const secondStudent = secondGroup[index] ? getStudentName(secondGroup[index]) : "Teacher";
    expect(studentPair.textContent.includes(firstStudent)).toEqual(true);
    expect(studentPair.textContent.includes(secondStudent)).toEqual(true);
  });
}

function getStudentName(student) {
  const { firstName, lastName } = student;
  return `${lastName}, ${firstName}`;
}

function getSortedScoresFor(students, historyItem, assessmentResultMeasuresIndex) {
  const studentScores = _.get(
    historyItem,
    `assessmentResultMeasures[${assessmentResultMeasuresIndex || 0}].studentResults`,
    []
  ).filter(studentScore => students.some(student => student._id === studentScore.studentId));
  return _.sortBy(studentScores, ({ score }) => -score);
}

function generateStudentRecommendationData(studentData, instructionalTargets) {
  return studentData.map(({ studentName, scores }) => {
    return {
      studentName,
      recommendationReasonData: scores.map((score, scoreIndex) => ({
        reasonTitle: `Measure ${scoreIndex + 1}`,
        columnData: {
          Score: score,
          Target: instructionalTargets[scoreIndex]
        }
      }))
    };
  });
}

function comparePairs(getByTestId, firstPair, lastPair) {
  expect(getByTestId(`studentPair_0`).textContent).toContain(firstPair.join(""));
  expect(getByTestId(`studentPair_2`).textContent).toContain(lastPair.join(""));
}
