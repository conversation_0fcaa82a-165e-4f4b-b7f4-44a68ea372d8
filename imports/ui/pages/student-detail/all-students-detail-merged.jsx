import React from "react";
import PropTypes from "prop-types";
import allStudentDetailContainerWithContext from "./all-student-detail-context";
import Loading from "../../components/loading";
import { PureStudentDetail as StudentDetail } from "./student-detail";

export class AllStudentDetailsMerged extends React.Component {
  render() {
    if (this.props.loading) {
      return <Loading />;
    }
    const {
      allClasswideScores,
      benchmarkWindows,
      classwideBenchmarkScores,
      classwideInterventionScores,
      individualInterventionScoresByStudentId,
      loading,
      otherResults,
      otherStudentGroupsByStudentId,
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      schoolYear,
      siteId,
      studentEnrollmentsByStudentId,
      studentGroup,
      studentGroupId,
      studentsInGroup,
      selectedSkillAssessmentId
    } = this.props;
    return (
      <div id="allStudentDetail">
        {this.props.studentIdsInGroup.map((studentId, index) => {
          const studentDetailTrackerData = {
            allClasswideScores,
            benchmarkWindows,
            classwideBenchmarkScores,
            classwideInterventionScores,
            individualInterventionScores: individualInterventionScoresByStudentId[studentId],
            loading,
            otherResults,
            otherStudentGroups: otherStudentGroupsByStudentId[studentId],
            printCurrentClasswideInterventionGraph,
            printAllClasswideInterventionGraphs,
            printAllIndividualInterventionGraphs,
            printCurrentStudentProfilePage,
            printAllStudentDetailsIncludingAllGraphs,
            printOnlyClasswideInterventionSkillGraphs,
            student: studentsInGroup.find(s => s._id === studentId),
            studentEnrollments: studentEnrollmentsByStudentId[studentId],
            studentGroup,
            students: studentsInGroup,
            shouldShowStudentsScores: true,
            selectedSkillAssessmentId
          };
          const className = `printContainer ${index !== 0 ? "page-break-before" : ""}`;
          return (
            <div key={studentId} id={`print_${studentId}`} className={className}>
              <StudentDetail
                siteId={siteId}
                studentGroupId={studentGroupId}
                studentId={studentId}
                activeNavName={null}
                schoolYear={schoolYear}
                {...studentDetailTrackerData}
              />
            </div>
          );
        })}
      </div>
    );
  }
}

AllStudentDetailsMerged.propTypes = {
  allClasswideScores: PropTypes.array,
  benchmarkWindows: PropTypes.array,
  classwideBenchmarkScores: PropTypes.array,
  classwideInterventionScores: PropTypes.array,
  individualInterventionScoresByStudentId: PropTypes.object,
  loading: PropTypes.bool,
  otherResults: PropTypes.array,
  otherStudentGroupsByStudentId: PropTypes.object,
  printCurrentClasswideInterventionGraph: PropTypes.bool,
  printAllClasswideInterventionGraphs: PropTypes.bool,
  printAllIndividualInterventionGraphs: PropTypes.bool,
  printCurrentStudentProfilePage: PropTypes.bool,
  printAllStudentDetailsIncludingAllGraphs: PropTypes.bool,
  printOnlyClasswideInterventionSkillGraphs: PropTypes.bool,
  schoolYear: PropTypes.number,
  siteId: PropTypes.string,
  studentEnrollmentsByStudentId: PropTypes.object,
  studentGroup: PropTypes.object,
  studentGroupId: PropTypes.string,
  studentIdsInGroup: PropTypes.array,
  studentsInGroup: PropTypes.array,
  selectedSkillAssessmentId: PropTypes.string
};

export default allStudentDetailContainerWithContext(<AllStudentDetailsMerged />);
