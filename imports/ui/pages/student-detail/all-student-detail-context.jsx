import React, { Component } from "react";
import PropTypes from "prop-types";
import { withTracker } from "meteor/react-meteor-data";
import { Meteor } from "meteor/meteor";
import { groupBy } from "lodash";
import { ClassContext } from "../classContext";
import Loading from "../../components/loading";
import { areSubscriptionsLoading } from "../../utilities";
import { Students } from "/imports/api/students/students";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { BenchmarkWindows } from "/imports/api/benchmarkWindows/benchmarkWindows";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { getPrintOptionsFrom } from "./student-detail";

class AllStudentDetailContext extends Component {
  static contextType = ClassContext;

  state = { canMountStudentDetail: false };

  componentDidMount() {
    this.context.setContext({ studentGroup: this.props.studentGroup, student: null }, () => {
      this.setState({ canMountStudentDetail: true });
    });
  }

  componentDidUpdate(prevProps) {
    if (prevProps.studentGroup !== this.context.studentGroup) {
      this.context.setContext({ studentGroup: this.props.studentGroup });
    }
  }

  componentWillUnmount() {
    this.context.setStudent({});
  }

  render() {
    if (this.state.canMountStudentDetail) {
      return React.Children.map(this.props.children, child => React.cloneElement(child, this.props));
    }
    return <Loading />;
  }
}

AllStudentDetailContext.propTypes = {
  loading: PropTypes.bool,
  studentGroup: PropTypes.object,
  children: PropTypes.elementType
};

export default function AllStudentDetailContainerWithContext(children) {
  return withTracker(props => {
    const { studentGroupId, schoolYear, orgid, siteId, grade, selectedSkillAssessmentId, selectedSkillIndex } = props;

    let allClasswideScores = [];
    let classwideBenchmarkScores = [];
    let classwideInterventionScores = [];
    let individualInterventionScoresByStudentId = {};
    let otherStudentGroupsByStudentId = {};
    let otherStudentAssessmentResults = [];
    let studentEnrollments = [];
    let benchmarkWindows = [];
    let studentGroup = {};
    let studentIdsInGroup = [];
    let studentsInGroup = [];
    let studentEnrollmentsByStudentId = {};

    const assessmentsHandle = Meteor.subscribe("AssessmentsForGrade", grade);
    const benchmarkWindowsSub = Meteor.subscribe("BenchmarkWindowsForSchoolWithSchoolYear", siteId, schoolYear);
    const individualRulesHandle = Meteor.subscribe("Rules:IndividualRootRulesByGrade", grade);
    const rulesSub = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroupId);
    const screeningAssignmentsHandle = Meteor.subscribe("ScreeningAssignmentsByGrade", grade);
    const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId, orgid);
    const studentsSub = Meteor.subscribe("StudentsInStudentGroup", studentGroupId);
    const enrollmentsHandle = Meteor.subscribe("StudentGroupEnrollmentsInStudentGroup", studentGroupId);

    const loading = areSubscriptionsLoading(
      assessmentsHandle,
      benchmarkWindowsSub,
      individualRulesHandle,
      rulesSub,
      screeningAssignmentsHandle,
      enrollmentsHandle,
      studentGroupsSub,
      studentsSub
    );

    if (!loading) {
      studentsInGroup = Students.find(
        {},
        { fields: { identity: 1, history: 1, demographic: 1, grade: 1 }, sort: { "identity.name.lastName": 1 } }
      ).fetch();
      studentIdsInGroup = studentsInGroup.map(s => s._id);
      studentGroup = StudentGroups.findOne(
        { _id: studentGroupId },
        {
          fields: {
            name: 1,
            history: 1,
            schoolYear: 1,
            grade: 1,
            currentClasswideSkill: 1,
            orgid: 1,
            additionalHistory: 1
          }
        }
      );
      benchmarkWindows = BenchmarkWindows.find().fetch();
      allClasswideScores = (studentGroup.history || []).filter(sgh => !!sgh.whenEnded);
      const assessmentResultIdsOfAllClasswideScores = allClasswideScores.map(
        ({ assessmentResultId }) => assessmentResultId
      );
      const assessmentResults = AssessmentResults.find({
        _id: { $nin: assessmentResultIdsOfAllClasswideScores },
        status: "COMPLETED",
        type: { $in: ["classwide", "benchmark"] }
      }).fetch();
      otherStudentAssessmentResults = reformatAssessmentResults(assessmentResults);
      studentEnrollments = StudentGroupEnrollments.find().fetch();
      classwideInterventionScores = (studentGroup.history || []).filter(
        sgh => sgh.type === "classwide" && sgh.whenEnded
      );
      classwideBenchmarkScores = (studentGroup.history || []).filter(sgh => sgh.type === "benchmark" && sgh.whenEnded);
      individualInterventionScoresByStudentId = Object.values(studentsInGroup).reduce((acc, student) => {
        acc[student._id] = (student.history || []).filter(sh => !!sh.whenEnded);
        return acc;
      }, {});
      studentEnrollmentsByStudentId = groupBy(studentEnrollments, "studentId");
      otherStudentGroupsByStudentId = Object.entries(studentEnrollmentsByStudentId).reduce(
        (acc, [studentId, enrollments]) => {
          acc[studentId] = StudentGroups.find(
            { _id: { $in: enrollments.map(enr => enr.studentGroupId) } },
            { fields: { name: 1 } }
          ).fetch();
          return acc;
        },
        {}
      );
    }

    const {
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults
    } = getPrintOptionsFrom(props);

    return {
      ...props,
      loading,
      allClasswideScores,
      benchmarkWindows,
      classwideBenchmarkScores,
      classwideInterventionScores,
      individualInterventionScoresByStudentId,
      otherResults: otherStudentAssessmentResults,
      otherStudentGroupsByStudentId,
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults,
      studentEnrollmentsByStudentId,
      studentGroup,
      studentIdsInGroup,
      studentsInGroup,
      selectedSkillAssessmentId,
      selectedSkillIndex,
      children
    };
  })(AllStudentDetailContext);
}
