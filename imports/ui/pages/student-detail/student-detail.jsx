/* eslint-disable max-classes-per-file */
import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { withTracker } from "meteor/react-meteor-data";
import Highcharts from "highcharts/highstock";
import { Link } from "react-router-dom";

import moment from "moment";
import { PRINT_OPTIONS } from "/imports/api/constants";
import { Students } from "/imports/api/students/students";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { AssessmentResults } from "/imports/api/assessmentResults/assessmentResults";
import { StudentGroupEnrollments } from "/imports/api/studentGroupEnrollments/studentGroupEnrollments";
import { BenchmarkWindows } from "/imports/api/benchmarkWindows/benchmarkWindows";

import { Loading } from "../../components/loading.jsx";
import IndividualInterventionProgress from "../../components/student-detail/individual-intervention-progress.jsx";
import ClasswideInterventionProgress from "../../components/student-detail/classwide-intervention-progress.jsx";
import ClasswideInterventionGraphHistory from "../../components/student-detail/classwide-intervention-graph-history";
import StudentLog from "../../components/student-detail/student-log.jsx";
import ScreeningResultsCharts from "../../components/student-detail/screening-results-charts";
import * as utils from "/imports/api/utilities/utilities";
import { getMeteorUserSync, sortByGradeAndName } from "/imports/api/utilities/utilities";
import { reformatAssessmentResults } from "/imports/api/assessmentResults/helpers";
import DetailHeaderText from "../../layouts/detail-header-text";
import ScrollIndicator from "../../components/scrollIndicator";
import ScrollIndicatorView from "../../components/scrollIndicatorView";
import { ClassContext } from "../classContext";
import { areSubscriptionsLoading, isHighSchoolGrade, isOnPrintPage } from "../../utilities";
import DetailTable from "./skillProgress";

function scroll(idToScrollTo) {
  document.getElementById(idToScrollTo).scrollIntoView({ behavior: "smooth" });
}

function disableChartAnimations() {
  Highcharts.setOptions({
    chart: {
      animation: false
    },
    series: {
      column: {
        animation: false
      },
      line: {
        animation: false
      }
    },
    plotOptions: {
      series: {
        animation: false
      }
    }
  });
}

class StudentDetail extends Component {
  static contextType = ClassContext;

  constructor(props) {
    super(props);
    this.state = {
      scroll: {
        lastReload: new Date().valueOf()
      },
      fetchingData: false,
      individualStudentProgressData: {}
    };
    this.isPrinting = isOnPrintPage();
  }

  componentDidMount() {
    if (this.isPrinting) {
      disableChartAnimations();
    }
    this.getStats(this.props.studentGroup);
    this.getIndividualProgress(this.props.studentGroup._id, this.props.student._id);
  }

  componentDidUpdate(prevProps) {
    if (this.props.student?._id !== prevProps.student?._id) {
      this.getStats(this.props.studentGroup);
      this.getIndividualProgress(this.props.studentGroup._id, this.props.student._id);
    }
  }

  refreshScroll = () => {
    const state = { ...this.state };
    state.scroll.lastReload = new Date().valueOf();
    this.setState(state);
  };

  getStats(studentGroup) {
    if (studentGroup) {
      this.setState({ fetchingData: true });
      Meteor.call("CalculateIndividualStats", studentGroup, (err, res) => {
        if (!err) {
          this.context.setStats(res);
        }
        this.setState({ fetchingData: false });
      });
    }
  }

  getIndividualProgress(studentGroupId, studentId) {
    if (studentGroupId && studentId) {
      this.setState({ fetchingData: true });
      Meteor.call(
        "getIndividualStudentDetailData",
        { studentGroupId, shouldIncludeAllSkills: false, studentId: this.props.student._id },
        (err, r) => {
          if (!err) {
            this.setState({ individualStudentProgressData: r?.rowData?.[studentId] || {} });
          }
          this.setState({ fetchingData: false });
        }
      );
    }
  }

  shouldRenderClasswideInterventionProgress() {
    const { studentGroup } = this.props;

    const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];
    return studentGroupHistory?.some(sgh => sgh.type === "classwide");
  }

  shouldRenderIndividualProgress() {
    const {
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults
    } = this.props;
    return (
      !printCurrentClasswideInterventionGraph &&
      !printAllClasswideInterventionGraphs &&
      !printOnlyClasswideInterventionSkillGraphs &&
      !printStudentClasswideProgressAndScreeningResults
    );
  }

  printIndividualProgress(hasStudentHistory) {
    if (!this.shouldRenderIndividualProgress()) {
      return null;
    }

    const { printAllIndividualInterventionGraphs, printAllStudentDetailsIncludingAllGraphs } = this.props;

    if (!hasStudentHistory) {
      return (
        <div className={`${printAllIndividualInterventionGraphs ? "" : "page-break-before"}`}>
          <h3>Individual Intervention Progress</h3>
          <div className="alert alert-info text-center">No data found for this student</div>
        </div>
      );
    }

    return (
      <IndividualInterventionProgress
        printAllIndividualInterventionGraphs={
          printAllIndividualInterventionGraphs || printAllStudentDetailsIncludingAllGraphs
        }
        isFirstSection={printAllIndividualInterventionGraphs}
        studentGroup={this.props.studentGroup}
        student={this.props.student}
      />
    );
  }

  printIndividualStudentProgressAndScreeningResults(isHS) {
    const { printStudentClasswideProgressAndScreeningResults } = this.props;
    if (!printStudentClasswideProgressAndScreeningResults) {
      return null;
    }
    return (
      <React.Fragment>
        {this.renderScreeningResults(isHS)}
        {this.renderIndividualStudentProgressSection(isHS)}
      </React.Fragment>
    );
  }

  printClasswideInterventionProgress() {
    if (
      this.props.printAllIndividualInterventionGraphs ||
      this.props.printStudentClasswideProgressAndScreeningResults
    ) {
      return null;
    }

    if (!this.shouldRenderClasswideInterventionProgress()) {
      return (
        <div data-testid="classwide-intervention-progress-section">
          <h3>Classwide Intervention Progress</h3>
          <div className="alert alert-info text-center">No data found for this student</div>
        </div>
      );
    }

    const {
      studentGroup,
      student,
      studentsInGroup = student ? [student] : [],
      selectedSkillAssessmentId,
      shouldShowStudentsScores = true,
      printAllClasswideInterventionGraphs,
      printCurrentStudentProfilePage,
      printAllStudentDetailsIncludingAllGraphs
    } = this.props;

    return (
      <div>
        {printAllClasswideInterventionGraphs || printAllStudentDetailsIncludingAllGraphs ? (
          <ClasswideInterventionGraphHistory studentGroup={studentGroup} student={student} students={studentsInGroup} />
        ) : (
          // TODO get rid of passing student group and student to Classwide Intervention Progress once we move group context to the top (where SG is chosen)
          <ClasswideInterventionProgress
            student={student}
            students={studentsInGroup}
            studentGroup={studentGroup}
            refreshScroll={this.refreshScroll}
            shouldShowSkillTreeProgress={!this.isPrinting || printCurrentStudentProfilePage}
            shouldShowStudentsScores={shouldShowStudentsScores || this.isPrinting}
            selectedSkillAssessmentId={selectedSkillAssessmentId}
          />
        )}
      </div>
    );
  }

  renderScreeningResults = isHS => {
    if (isHS) {
      return null;
    }

    const { student, studentGroup, printStudentClasswideProgressAndScreeningResults } = this.props;
    const otherScreeningResults = this.props.otherResults.filter(result => result.type === "benchmark");
    const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];

    return (
      <section
        id="student-detail-screening"
        className={printStudentClasswideProgressAndScreeningResults ? "" : "page-break-before"}
      >
        <ScreeningResultsCharts
          classwideBenchmarkScores={this.props.classwideBenchmarkScores}
          schoolYear={this.props.schoolYear || this.props.studentGroup.schoolYear}
          otherScreeningResults={otherScreeningResults}
          otherStudentGroups={this.props.otherStudentGroups}
          studentHistory={this.props.student.history}
          studentGroupHistory={studentGroupHistory}
          benchmarkWindows={this.props.benchmarkWindows}
          student={student}
        />
      </section>
    );
  };

  renderIndividualStudentProgressSection = isHS => {
    if (!this.state.individualStudentProgressData?.columns?.find(c => c.outcome !== null)) {
      return (
        <React.Fragment>
          <h3>Student Skill Progress </h3>
          <div className="alert alert-info text-center">No skill progress data found for this student</div>
        </React.Fragment>
      );
    }

    const { student } = this.props;
    return (
      <section id="student-detail-individual-progress" className={isHS ? "" : "page-break-before"}>
        <h3>Student Skill Progress </h3>
        <DetailTable
          rowData={this.state.individualStudentProgressData}
          componentContext={"studentDetail"}
          studentId={student._id}
        />
      </section>
    );
  };

  renderStudentLog = () => {
    const {
      student,
      otherStudentGroups,
      allClasswideScores,
      otherResults,
      studentEnrollments,
      schoolYear,
      individualInterventionScores
    } = this.props;
    return (
      <section id="conStudentActivityLog" data-testid="conStudentActivityLog" className="page-break-before">
        <h3>Student Activity Log</h3>
        <StudentLog
          individualInterventionScores={individualInterventionScores}
          allClasswideScores={allClasswideScores}
          studentEnrollments={studentEnrollments}
          otherStudentGroups={otherStudentGroups}
          otherResults={otherResults}
          schoolYear={schoolYear}
          student={student}
        />
      </section>
    );
  };

  render() {
    if (this.props.loading || this.state.fetchingData) {
      return <Loading />;
    }
    const isHS = isHighSchoolGrade(this.props.studentGroup.grade);
    const containerClass = this.isPrinting ? "container" : "main-content p-2";
    const {
      student,
      printOption,
      printCurrentClasswideInterventionGraph,
      printAllClasswideInterventionGraphs,
      printAllIndividualInterventionGraphs,
      printOnlyClasswideInterventionSkillGraphs,
      printStudentClasswideProgressAndScreeningResults,
      selectedSkillAssessmentId
    } = this.props;

    const studentGroupHistory = [
      ...(this.props.studentGroup.additionalHistory || []),
      ...(this.props.studentGroup.history || [])
    ];

    if (this.isPrinting) {
      const currentDateString = moment().format("DD-MM-YY");
      if (printOnlyClasswideInterventionSkillGraphs) {
        document.title = `${this.props.studentGroup.name.match(/\((.*)\)/).pop()}_${studentGroupHistory.find(
          classwideInterventionScore => classwideInterventionScore.assessmentId === selectedSkillAssessmentId
        )?.assessmentName ||
          this.props.studentGroup.currentClasswideSkill?.assessmentName ||
          studentGroupHistory[studentGroupHistory.length - 1]?.assessmentName}_${currentDateString}`;
      } else {
        const studentName = student
          ? `${student.identity.name.lastName} ${student.identity.name.firstName}`
          : "Student";
        const documentName = printOption ? printOption.replace("Print ", "") : "Detail Page";
        const pdfTitle = `${studentName} - ${documentName} ${currentDateString}`;
        document.title = pdfTitle;
        // Set title for iframe parent page
        window.parent.document.querySelector("title").innerHTML = pdfTitle;
        // Set title for iframe page
        window.document.querySelector("title").innerHTML = pdfTitle;
      }
    }

    const hasStudentHistory = student?.history?.length > 0;

    return (
      <div className={containerClass}>
        {this.isPrinting ? (
          <div className="profile-view m-t-10">
            <DetailHeaderText
              displayStats={hasStudentHistory && this.shouldRenderIndividualProgress()}
              student={student}
              studentGroup={this.props.studentGroup}
            />
          </div>
        ) : null}
        <ul className="nav nav-pills middle-sub-nav">
          {this.shouldRenderClasswideInterventionProgress() || student.history ? (
            <li>
              <Link to={"#"} onClick={() => scroll("student-detail-intervention-progress")}>
                Intervention Progress
              </Link>
            </li>
          ) : null}
          {!isHS ? (
            <li>
              <Link to={"#"} onClick={() => scroll("student-detail-screening")}>
                Screening Results
              </Link>
            </li>
          ) : null}
          <li>
            <Link to={"#"} onClick={() => scroll("student-detail-individual-progress")}>
              Individual Progress
            </Link>
          </li>
          <li>
            <Link to={"#"} onClick={() => scroll("conStudentActivityLog")}>
              Activity Log
            </Link>
          </li>
        </ul>
        <ScrollIndicator
          container={this}
          targetSelector={".studentDetailContent"}
          indicatorComponent={<ScrollIndicatorView />}
          uniqKey={this.state.scroll.lastReload}
        >
          <div id="studentDetailContainer" className="studentDetailContent">
            <section id="student-detail-intervention-progress" data-testid="student-detail-intervention-progress">
              {this.printClasswideInterventionProgress()}
              {this.printIndividualProgress(hasStudentHistory)}
              {this.printIndividualStudentProgressAndScreeningResults(isHS)}
            </section>
            {!printCurrentClasswideInterventionGraph &&
              !printAllClasswideInterventionGraphs &&
              !printAllIndividualInterventionGraphs &&
              !printOnlyClasswideInterventionSkillGraphs &&
              !printStudentClasswideProgressAndScreeningResults && (
                <React.Fragment>
                  {this.renderScreeningResults(isHS)}
                  {this.renderIndividualStudentProgressSection()}
                  {this.renderStudentLog()}
                </React.Fragment>
              )}
          </div>
        </ScrollIndicator>
      </div>
    );
  }
}

const studentDetailProps = {
  allClasswideScores: PropTypes.array, // TODO OF
  classwideBenchmarkScores: PropTypes.array, // TODO OF
  individualInterventionScores: PropTypes.array, // TODO OF
  loading: PropTypes.bool,
  student: PropTypes.object, // TODO SHAPE
  studentGroup: PropTypes.object, // TODO SHAPE
  schoolYear: PropTypes.number,
  otherResults: PropTypes.array,
  otherStudentGroups: PropTypes.array,
  studentEnrollments: PropTypes.array,
  benchmarkWindows: PropTypes.array,
  classwideInterventionScores: PropTypes.array,
  studentsInGroup: PropTypes.array,
  shouldShowStudentsScores: PropTypes.bool,
  printOption: PropTypes.string,
  printCurrentClasswideInterventionGraph: PropTypes.bool,
  printAllClasswideInterventionGraphs: PropTypes.bool,
  printAllIndividualInterventionGraphs: PropTypes.bool,
  printCurrentStudentProfilePage: PropTypes.bool,
  printAllStudentDetailsIncludingAllGraphs: PropTypes.bool,
  printStudentClasswideProgressAndScreeningResults: PropTypes.bool,
  printOnlyClasswideInterventionSkillGraphs: PropTypes.bool,
  selectedSkillAssessmentId: PropTypes.string
};
StudentDetail.propTypes = studentDetailProps;

class StudentDetailContext extends Component {
  static contextType = ClassContext;

  state = { canMountStudentDetail: false };

  componentDidUpdate(prevProps) {
    if ((prevProps.loading || !this.state.canMountStudentDetail) && !this.props.loading) {
      this.context.setContext({ studentGroup: this.props.studentGroup, student: this.props.student }, () => {
        this.setState({ canMountStudentDetail: true });
      });
    }
  }

  componentWillUnmount() {
    this.context.setStudent({});
  }

  render() {
    if (this.state.canMountStudentDetail) {
      return (
        <StudentDetail
          {...this.props}
          student={this.context.student}
          studentGroup={this.context.studentGroup}
          students={this.context.students}
        />
      );
    }
    return <Loading />;
  }
}
StudentDetailContext.propTypes = studentDetailProps;

// Data Container
export default withTracker(params => {
  const user = getMeteorUserSync();
  const { studentGroupId, studentId, siteId, schoolYear: contextSchoolYear } = params;
  const schoolYear = contextSchoolYear || utils.getCurrentSchoolYear(user);
  const studentGroupsSub = Meteor.subscribe("StudentGroupsAssociatedWithUser", schoolYear, siteId);
  const studentHandle = Meteor.subscribe("Students:GetStudentById", siteId, studentGroupId, studentId);
  let studentGroup = null;
  let student = null;
  let allClasswideScores = [];
  let classwideBenchmarkScores = [];
  let classwideInterventionScores = [];
  let individualInterventionScores = [];
  const assessmentsResultsHandle = Meteor.subscribe("AssessmentResults:FindStudentResultsFromOtherGroups", {
    studentGroupId,
    studentIds: [studentId],
    schoolYear
  });
  const enrollmentsHandle = Meteor.subscribe("StudentGroupEnrollmentsAssociatedWithUser", {
    studentId,
    schoolYear,
    user
  });

  const benchmarkWindowsSub = Meteor.subscribe("BenchmarkWindowsForSchoolWithSchoolYear", siteId, schoolYear);

  let studentEnrollments = [];
  let otherStudentAssessmentResults = [];
  let otherStudentGroups = [];
  let benchmarkWindows = [];
  const initialLoading = areSubscriptionsLoading(
    assessmentsResultsHandle,
    enrollmentsHandle,
    studentGroupsSub,
    studentHandle,
    benchmarkWindowsSub
  );
  let finalLoading = true;
  if (!initialLoading) {
    benchmarkWindows = BenchmarkWindows.find().fetch();
    studentGroup = StudentGroups.findOne({ _id: studentGroupId });
    student = Students.findOne({ _id: studentId });
    const { orgid, grade } = studentGroup;
    const additionalGradeForRules = grade === "K" ? "01" : null;
    const rulesHandle = Meteor.subscribe("GradeLevelRulesByStudentGroup", studentGroupId, additionalGradeForRules);
    const assessmentsHandle = Meteor.subscribe("AssessmentsForGrade", grade);
    const studentGroupsHandle = Meteor.subscribe("StudentGroups:NamesForStudentLog", orgid);
    const individualRulesHandle = Meteor.subscribe("Rules:IndividualRootRulesByGrade", grade);
    const screeningAssignmentsHandle = Meteor.subscribe("ScreeningAssignmentsByGrade", grade);
    const studentGroupHistory = [...(studentGroup.additionalHistory || []), ...(studentGroup.history || [])];

    allClasswideScores = studentGroupHistory.filter(sgh => !!sgh.whenEnded);

    const assessmentResultIdsOfAllClasswideScores = allClasswideScores.map(
      ({ assessmentResultId }) => assessmentResultId
    );
    const assessmentResults = AssessmentResults.find({
      _id: { $nin: assessmentResultIdsOfAllClasswideScores },
      status: "COMPLETED",
      type: { $in: ["classwide", "benchmark"] }
    }).fetch();
    otherStudentAssessmentResults = reformatAssessmentResults(assessmentResults);
    studentEnrollments = StudentGroupEnrollments.find({ studentId }).fetch();
    classwideBenchmarkScores = studentGroupHistory.filter(sgh => sgh.type === "benchmark" && sgh.whenEnded);
    classwideInterventionScores = studentGroupHistory.filter(sgh => sgh.type === "classwide" && sgh.whenEnded);
    individualInterventionScores = (student.history || []).filter(sh => !!sh.whenEnded);

    finalLoading = areSubscriptionsLoading(
      rulesHandle,
      assessmentsHandle,
      studentGroupsHandle,
      individualRulesHandle,
      screeningAssignmentsHandle
    );
    if (!finalLoading) {
      otherStudentGroups = StudentGroups.find(
        { _id: { $in: studentEnrollments.map(enr => enr.studentGroupId) } },
        { fields: { name: 1 } }
      )
        .fetch()
        .sort(sortByGradeAndName);
    }
  }
  const {
    printCurrentClasswideInterventionGraph,
    printAllClasswideInterventionGraphs,
    printAllIndividualInterventionGraphs,
    printCurrentStudentProfilePage,
    printAllStudentDetailsIncludingAllGraphs,
    printStudentClasswideProgressAndScreeningResults,
    printOnlyClasswideInterventionSkillGraphs
  } = getPrintOptionsFrom(params);

  return {
    allClasswideScores,
    individualInterventionScores,
    classwideBenchmarkScores,
    classwideInterventionScores,
    student,
    studentGroup,
    schoolYear,
    otherResults: otherStudentAssessmentResults,
    studentEnrollments,
    loading: finalLoading,
    otherStudentGroups,
    printCurrentClasswideInterventionGraph,
    printAllClasswideInterventionGraphs,
    printAllIndividualInterventionGraphs,
    printCurrentStudentProfilePage,
    printAllStudentDetailsIncludingAllGraphs,
    printStudentClasswideProgressAndScreeningResults,
    printOnlyClasswideInterventionSkillGraphs,
    benchmarkWindows
  };
})(StudentDetailContext);

export { StudentDetail as PureStudentDetail };
export { StudentDetailContext };

export function getPrintOptionsFrom(props) {
  let printCurrentClasswideInterventionGraph = false;
  let printAllClasswideInterventionGraphs = false;
  let printAllIndividualInterventionGraphs = false;
  let printCurrentStudentProfilePage = false;
  let printAllStudentDetailsIncludingAllGraphs = false;
  let printStudentClasswideProgressAndScreeningResults = false;

  switch (props.printOption) {
    case PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION:
      printCurrentClasswideInterventionGraph = true;
      break;
    case PRINT_OPTIONS.ALL_CLASSWIDE_INTERVENTION_GRAPHS:
      printAllClasswideInterventionGraphs = true;
      break;
    case PRINT_OPTIONS.ALL_INDIVIDUAL_INTERVENTION_GRAPHS:
      printAllIndividualInterventionGraphs = true;
      break;
    case PRINT_OPTIONS.CURRENT_STUDENT_PROFILE:
      printCurrentStudentProfilePage = true;
      break;
    case PRINT_OPTIONS.ALL_STUDENT_DETAILS:
      printAllStudentDetailsIncludingAllGraphs = true;
      break;
    case PRINT_OPTIONS.STUDENT_CLASSWIDE_PROGRESS_AND_SCREENING_RESULTS:
      printStudentClasswideProgressAndScreeningResults = true;
      break;
    default:
  }
  return {
    printCurrentClasswideInterventionGraph,
    printAllClasswideInterventionGraphs,
    printAllIndividualInterventionGraphs,
    printCurrentStudentProfilePage,
    printAllStudentDetailsIncludingAllGraphs,
    printStudentClasswideProgressAndScreeningResults,
    printOnlyClasswideInterventionSkillGraphs: props.printOnlyClasswideInterventionSkillGraphs === "true"
  };
}
