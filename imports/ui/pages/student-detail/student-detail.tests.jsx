import { assert } from "chai";
import React from "react";
import td from "testdouble";
import { Meteor } from "meteor/meteor";
import { cleanup, waitFor } from "@testing-library/react";
import { PureStudentDetail } from "./student-detail.jsx";
import { ClassContext } from "../classContext";
import { renderWithRouter } from "../../../../tests/helpers/testUtils";
import "@testing-library/jest-dom";

// Mock getCurrentSchoolYear to return a resolved value
jest.mock("/imports/api/utilities/utilities", () => ({
  ...jest.requireActual("/imports/api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => Promise.resolve(2019)),
  getMeteorUser: jest.fn(() => ({ profile: { orgid: "test_organization_id" } })),
  getMeteorUserId: jest.fn(() => "test_user_id")
}));

const basicStudent = {
  _id: "studentId",
  history: [],
  identity: { name: { firstName: "Test", lastName: "Student" } }
};

const defaultContext = {
  student: basicStudent,
  studentGroup: { history: [] },
  setStats: () => {}
};

// eslint-disable-next-line react/prop-types
function renderStudentDetailWith({ context = defaultContext, props = {} } = {}) {
  const componentProps = {
    classwideBenchmarkScores: [],
    IndividualInterventionScores: [],
    allClasswideScores: [],
    studentEnrollments: [],
    otherResults: [],
    student: { history: [] },
    studentGroup: { history: [] },
    benchmarkWindows: [],
    loading: false,
    ...props
  };
  return (
    <ClassContext.Provider value={{ ...defaultContext, ...context }}>
      <PureStudentDetail {...componentProps} />
    </ClassContext.Provider>
  );
}

jest.mock("../../../api/utilities/utilities", () => ({
  ...jest.requireActual("../../../api/utilities/utilities"),
  getCurrentSchoolYear: jest.fn(() => 2018),
  getFormattedSchoolYear: jest.fn(() => "2017-18"),
  translateBenchmarkPeriod: jest.fn(() => ({ title: "Winter" }))
}));

describe("Student-Detail UI", () => {
  afterEach(() => {
    cleanup();
  });
  afterAll(() => {
    jest.restoreAllMocks();
  });
  describe("Render", () => {
    const studentGroup = { history: [] };
    beforeEach(async () => {
      td.replace(Meteor, "call");
      td.when(Meteor.call("CalculateIndividualStats", studentGroup)).thenCallback(null, {});
      td.when(Meteor.call("getIndividualStudentDetailData", td.matchers.anything())).thenCallback(null, {});
      // Mock Meteor methods and subscriptions
      Meteor.user = jest.fn(() => ({ profile: { orgid: "test_organization_id" } }));
      Meteor.subscribe = jest.fn(() => ({ ready: () => true }));
    });
    afterEach(() => {
      td.reset();
    });
    it("renders the primary component", () => {
      // Verify that the method does what we expected
      const PureStudentDetailComponent = renderWithRouter(renderStudentDetailWith());
      assert.isDefined(PureStudentDetailComponent, "StudentDetailComponent did not render");
    });

    it("renders the StudentLog", async () => {
      const { getByTestId } = renderWithRouter(renderStudentDetailWith());
      await waitFor(() => {
        const studentLogComponent = getByTestId("conStudentActivityLog");
        expect(studentLogComponent).toBeVisible();
      });
    });

    it("renders the individual intervention progress when the student has history interventions", async () => {
      const assessmentId = "assessmentId";
      const context = {
        student: {
          _id: "studentId",
          currentSkill: {},
          identity: { name: { firstName: "Test", lastName: "Student" } },
          history: [
            {
              benchmarkAssessmentId: "benchmarkAssessmentId",
              assessmentId,
              type: "individual",
              assessmentResultMeasures: [{ assessmentId, studentScores: [] }]
            }
          ]
        },
        studentGroup: { history: [] }
      };
      const individualProgressProps = {
        printAllIndividualInterventionGoalSkills: false,
        printAllIndividualInterventionSkills: false
      };

      const { getByTestId } = renderWithRouter(renderStudentDetailWith({ context, props: individualProgressProps }));

      await waitFor(() => {
        const individualInterventionProgressComponent = getByTestId("student-detail-intervention-progress");
        expect(individualInterventionProgressComponent).toBeVisible();
      });
    });

    describe("ScreeningResultsChart", () => {
      afterEach(cleanup);
      const assessmentResultId = "assessmentResultId";
      const classwideBMScores = [
        {
          assessmentId: "assessmentId",
          assessmentResultId,
          assessmentResultMeasures: [
            {
              assessmentId: "assessmentId",
              assessmentName: "Assessment Name",
              targetScores: [3, 4, 5],
              studentResults: [
                {
                  studentId: basicStudent._id,
                  score: "5"
                }
              ]
            }
          ],
          type: "benchmark",
          enrolledStudentIds: [basicStudent._id],
          benchmarkPeriodId: "winter",
          whenEnded: {
            date: new Date(),
            on: 1234567899
          }
        }
      ];
      const allClasswideScores = [
        {
          assessmentResultId,
          benchmarkPeriodId: "winter",
          enrolledStudentIds: [basicStudent._id],
          assessmentResultMeasures: [
            {
              assessmentId: "assessmentId",
              assessmentName: "Assessment Name",
              targetScores: [3, 4, 5],
              studentResults: [
                {
                  studentId: basicStudent._id,
                  score: "5"
                }
              ]
            }
          ],
          whenEnded: {
            date: new Date(),
            on: 1234567890
          }
        }
      ];

      it("renders the screening results when student scores in classwideBenchmark scores are present", async () => {
        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({ props: { classwideBenchmarkScores: classwideBMScores, allClasswideScores } })
        );
        await waitFor(() => {
          const individualScreeningChartComponent = getByTestId(`${assessmentResultId}_${basicStudent._id}`);
          expect(individualScreeningChartComponent).toBeVisible();
        });
      });

      it('renders "student was absent or unavailable" text message if there aren\'t any valid student scores', async () => {
        const bmScoresWithNullStudentScore = JSON.parse(JSON.stringify(allClasswideScores));
        bmScoresWithNullStudentScore[0].assessmentResultMeasures[0].studentResults[0].score = null;

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            props: { classwideBenchmarkScores: bmScoresWithNullStudentScore, allClasswideScores }
          })
        );

        await waitFor(() => {
          const chartResults = getByTestId("screening-results-charts");
          expect(chartResults.textContent).toContain("Test was absent or unavailable during screening");
        });
      });

      it('renders "student did not participate in this screening assessment" text if there aren\'t any screening scores for this student', async () => {
        const bmScoresWithoutStudentScores = getBmScoresWithoutStudentScores(allClasswideScores);

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            props: { classwideBenchmarkScores: bmScoresWithoutStudentScores, allClasswideScores }
          })
        );

        await waitFor(() => {
          const screeningResultsCharts = getByTestId("screening-results-charts");
          expect(screeningResultsCharts.textContent).toContain("Test did not participate in this screening assessment");
        });
      });

      it("renders information about the enrollment in which the student took the screening assessment if assessments from other groups are available", async () => {
        const otherGroupId = "otherGroupId";
        const bmScoresWithoutStudentScores = getBmScoresWithoutStudentScores(allClasswideScores);
        const otherGroupBMResults = [{ ...classwideBMScores[0], studentGroupId: otherGroupId }];
        const otherGroups = [
          {
            _id: otherGroupId,
            name: "Other Group Name"
          }
        ];

        const { getByTestId } = renderWithRouter(
          renderStudentDetailWith({
            props: {
              classwideBenchmarkScores: bmScoresWithoutStudentScores,
              allClasswideScores,
              otherResults: otherGroupBMResults,
              otherStudentGroups: otherGroups
            }
          })
        );

        await waitFor(() => {
          const screeningResultsCharts = getByTestId("screening-results-charts");
          expect(screeningResultsCharts.textContent).toContain(
            `Test was enrolled in class ${otherGroups[0].name} when they took the screening assessment`
          );
        });
      });
    });
  });
});

function getBmScoresWithoutStudentScores(bmScores) {
  const bmScoresWithoutStudentScores = JSON.parse(JSON.stringify(bmScores));
  bmScoresWithoutStudentScores[0].assessmentResultMeasures[0].studentResults = [];
  bmScoresWithoutStudentScores[0].enrolledStudentIds = [];
  return bmScoresWithoutStudentScores;
}
