import React, { Component } from "react";
import PropTypes from "prop-types";
import { Meteor } from "meteor/meteor";
import { isAdminOrUniversalCoach } from "/imports/api/roles/methods";
import Alert from "react-s-alert";
import * as helpers from "../components/student-groups/helperFunction";
import { getMeteorUser } from "/imports/api/utilities/utilities";

const ClassContext = React.createContext();
const { Provider } = ClassContext;

const initialState = {
  studentsWithIndividualRuleNotProcessed: [],
  showInterventionModal: false,
  groupStats: {}
};

class ClassProvider extends Component {
  state = {
    ...initialState,
    studentGroup: {}, // STUDENT GROUP CONTEXT SHOULD BE SET IN THE PARENT COMPONENT (WHEN ACTUALLY CHOOSING A GROUP)
    students: [],
    student: {}
  };

  setStudentGroup = (studentGroup, callback = () => {}) =>
    this.setState(
      prevState => ({
        ...prevState,
        studentGroup
      }),
      callback()
    );

  setContext = (newContext, cb = () => {}) =>
    this.setState(
      prevState => ({
        ...prevState,
        ...newContext
      }),
      cb
    );

  setStudents = students =>
    this.setState(prevState => ({
      ...prevState,
      students
    }));

  setStudent = student => this.setState({ student });

  addUnprocessedStudent = student =>
    this.setState(prevState => ({
      ...prevState,
      showInterventionModal: true,
      studentsWithIndividualRuleNotProcessed: [...prevState.studentsWithIndividualRuleNotProcessed, student]
    }));

  endInterventionHandling = () => this.setState(prevState => ({ ...prevState, ...initialState }));

  setStats = stats => {
    this.setState(prevState => {
      return {
        ...prevState,
        groupStats: stats
      };
    });
  };

  processIndividualRule = ({ assessmentResultId, allowNoScore, studentId, cb }) => {
    Meteor.call(
      "processIndividualRule",
      {
        assessmentResultId,
        allowNoScore,
        studentId
      },
      err => {
        if (err) {
          this.handleIndividualRuleProcessingError(err, studentId);
          return null;
        }

        return cb && cb();
      }
    );
  };

  handleIndividualRuleProcessingError = (err, studentId) => {
    const allSkillTreesCompleted = err.message.match(
      /Student already completed all individual intervention skill trees/
    );
    const duplicateIndividualInterventionError = err.message.match(/Student already has an assessment result document/);
    const alreadyCompletedTree = err.message.match(/Already completed this progress monitoring tree/);
    const isAdminUser = isAdminOrUniversalCoach(this.state.studentGroup.siteId, getMeteorUser()?.profile?.siteAccess);
    if (duplicateIndividualInterventionError && isAdminUser) {
      this.addUnprocessedStudent(this.state.students.find(s => s._id === studentId));
    } else if (duplicateIndividualInterventionError && !isAdminUser) {
      helpers.displayError(`${this.getStudentNameByStudentId(studentId)} previously had an individual intervention.
      Please talk to your coach/admin about starting a new intervention.`);
    } else if (alreadyCompletedTree) {
      this.setContext({
        alreadyCompletedTreeData: err.details
      });
    } else if (allSkillTreesCompleted) {
      Alert.warning(err.reason || err.message, { timeout: 8000 });
    } else {
      helpers.displayError(`There was an error: ${err.message}`);
    }
  };

  render() {
    const {
      studentsWithIndividualRuleNotProcessed,
      showInterventionModal,
      groupStats,
      studentGroup,
      students,
      student
    } = this.state;
    return (
      <Provider
        value={{
          ...this.state, // allow using new(not defined in context keys by this.setContext fn
          studentsWithIndividualRuleNotProcessed,
          showInterventionModal,
          groupStats,
          studentGroup,
          students,
          student,
          setStats: this.setStats,
          addUnprocessedStudent: this.addUnprocessedStudent,
          endInterventionHandling: this.endInterventionHandling,
          setStudentGroup: this.setStudentGroup,
          setStudents: this.setStudents,
          setStudent: this.setStudent,
          setContext: this.setContext,
          processIndividualRule: this.processIndividualRule,
          handleIndividualRuleProcessingError: this.handleIndividualRuleProcessingError
        }}
      >
        {this.props.children}
      </Provider>
    );
  }
}

ClassProvider.propTypes = {
  children: PropTypes.any
};

export { ClassProvider, ClassContext };
