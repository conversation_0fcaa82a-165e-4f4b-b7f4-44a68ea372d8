import { Meteor } from "meteor/meteor";
import React, { Component } from "react";
import PropTypes from "prop-types";
import { withRouter } from "react-router-dom";
import { withTracker } from "meteor/react-meteor-data";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import _max from "lodash/max";
import get from "lodash/get";
import { cloneDeep, isEqual } from "lodash";

import {
  ninjalog,
  translateBenchmarkPeriod,
  calculateRoI,
  getMeteorUser,
  shouldUseDevMode
} from "/imports/api/utilities/utilities";
import ProgressMonitoringChart from "../../components/pmChart";
import { Assessments } from "/imports/api/assessments/assessments";
import PrintInterventionsDropdown from "../../components/dashboard/printInterventionsDropdown";
import ScoreEntryRowCell from "../../components/score-entry/score-entry-row-cell";
import InterventionMessage from "../../components/dashboard/intervention-message";
import { StudentGroups } from "/imports/api/studentGroups/studentGroups";
import { ScoreEntryContext } from "../../components/score-entry/score-entry-context";
import { getCurrentDate } from "/imports/api/helpers/getCurrentDate";
import { filterOutHistoryItemsThatDidNotLeadToIntervention } from "../../utilities";
import SkillVideoButton from "../../components/dashboard/skill-video-button";
import IncrementalRehearsalButton from "../../components/dashboard/intervention-content/incremental-rehearsal-button";
import PreviewSkillTreeModal from "./preview-skill-tree-modal";
import TooltipWrapper from "../../components/tooltip-wrapper";

function max(array) {
  return _max(array) || -Infinity;
}

function findMeetingTargetIndexFromScore(score, targets) {
  let result = -1;
  for (let i = 0; i < targets.length; i += 1) {
    if (score < targets[i]) {
      return i - 1;
    }
    result = i;
  }
  return result;
}

const getTimeRange = dates => {
  const start = Math.min(...dates);
  const end = Math.max(...dates);
  return { start, end };
};

export function renderResultFeedback(
  calculatedAssessmentResults,
  bmScoresObj,
  pmScoresObj,
  studentFullName,
  bmAssId,
  bmPeriodId
) {
  // todo logic for actual feedback
  // take apart the results and look at the targets and display some nice user feedback...
  let pmScoreMeasureIndex = 1;
  if (bmScoresObj.bmAssessmentId === pmScoresObj.assessmentId) {
    pmScoreMeasureIndex = 0;
  }
  const bmTargetScores = calculatedAssessmentResults.measures[0].targetScores;
  const pmTargetScores = calculatedAssessmentResults.measures[pmScoreMeasureIndex].targetScores;

  const currentBMResultScore = parseInt(calculatedAssessmentResults.measures[0].studentResults[0].score);
  const currentPMResultScore = parseInt(
    calculatedAssessmentResults.measures[pmScoreMeasureIndex].studentResults[0].score
  );

  // eslint-disable-next-line no-restricted-globals
  if (isNaN(currentBMResultScore)) {
    return null;
  }

  const allPreviousBMScores = bmScoresObj.scores.slice(0, -1) || [0];
  const allPreviousPMScores = pmScoresObj.scores
    .filter(scoreObj => scoreObj.bmPeriodId === bmPeriodId && scoreObj.bmAssId === bmAssId)
    .map(obj => obj.score);

  const sameAsst = bmScoresObj.bmAssessmentId === pmScoresObj.assessmentId;
  const scoreTypeText = sameAsst ? "Goal" : "Intervention";
  const scoreTypeArticleText = sameAsst ? "a" : "an";
  // Moving on to next tree? (passed bm?)
  if (currentBMResultScore >= bmTargetScores[1]) {
    return (
      <div className="text-end font-light">
        <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
          <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
          &nbsp;received a{" "}
          <strong>
            <u>Goal Skill score of {currentBMResultScore}</u>
          </strong>
          ! Excellent work.
          <br />
          Save the scores to determine if {studentFullName} should move on to a new goal skill.
        </RbAlert>
      </div>
    );
  }
  // also... indicate improvement on the benchmark if so
  const improvedOnBenchmark = currentBMResultScore > max(allPreviousBMScores);
  // Moving on to a different intervention set?
  const bestTargetBefore = findMeetingTargetIndexFromScore(
    max(sameAsst ? allPreviousBMScores : allPreviousPMScores),
    pmTargetScores
  );
  const pmTargetNow = findMeetingTargetIndexFromScore(currentPMResultScore, pmTargetScores);
  if (pmTargetNow > bestTargetBefore) {
    if (pmTargetNow >= 1) {
      return (
        <div className="text-end font-light">
          <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
            <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
            &nbsp;received {scoreTypeArticleText}{" "}
            <strong>
              <u>
                {scoreTypeText} Skill score of {currentPMResultScore}
              </u>
            </strong>
            ! Excellent work. {studentFullName} will be moving on to a new Intervention Skill.
            {improvedOnBenchmark && !sameAsst ? (
              <strong> {studentFullName} also improved on the Goal Skill!</strong>
            ) : null}
            <br />
            Save the scores to continue the intervention.
          </RbAlert>
        </div>
      );
    }
    return (
      <div className="text-end font-light">
        <RbAlert variant="success" className="alert-success-dark m-l-10 m-r-10 text-start">
          <i className="fa fa-thumbs-o-up fa-lg fa-right" /> {studentFullName}
          &nbsp;received {scoreTypeArticleText}{" "}
          <strong>
            <u>
              {scoreTypeText} Skill score of {currentPMResultScore}
            </u>
          </strong>
          ! Excellent work. There will be new practice materials next time.
          {improvedOnBenchmark && !sameAsst ? (
            <strong> {studentFullName} also improved on the Goal Skill!</strong>
          ) : null}
          <br />
          Save the scores to continue the intervention for {studentFullName}.
        </RbAlert>
      </div>
    );
  }
  // Staying here?
  return (
    <div className="text-end font-light">
      <RbAlert variant="warning" className="alert-warning-dark m-l-10 m-r-10 text-start">
        <i className="fa fa-warning fa-lg fa-right" /> {studentFullName}
        &nbsp;received {scoreTypeArticleText}{" "}
        <strong>
          <u>
            {scoreTypeText} Skill score of {currentPMResultScore}
          </u>
        </strong>
        ! Great job. We will continue practicing this Intervention Skill.
        {improvedOnBenchmark ? <strong> {studentFullName} also improved on the Goal Skill!</strong> : null}
        <br />
        Save the scores to continue the intervention for {studentFullName}.
      </RbAlert>
    </div>
  );
}

class IndividualIntervention extends Component {
  static contextType = ScoreEntryContext;

  constructor(props) {
    super(props);
    this.state = {
      bsModalShow: false,
      bsModalContent: "",
      bsModalClassNames: "",
      resultFeedback: null,
      calculatedAssessmentResults: null,
      isInterventionSkillGroupsModalOpen: false,
      isRecalculatingStats: false,
      isSavingScores: false,
      shouldDisplayPreviewSkillTreeModal: false
    };
  }

  componentDidMount() {
    this.calculateScores();
  }

  componentDidUpdate(prevProps) {
    if (!isEqual(prevProps.assessmentResult, this.props.assessmentResult)) {
      this.calculateScores();
      return true;
    }
    return false;
  }

  getCurrentScores = props => {
    const { assessmentId, bmAssessmentId, assessmentResult, studentInfo, getStatus } = props;
    const pmAssessmentScore = assessmentResult.scores.find(
      sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
    );

    const bmAssessmentScore = assessmentResult.scores.find(
      sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
    );
    return {
      pmAssessmentScore: pmAssessmentScore?.value?.length ? pmAssessmentScore?.value : "",
      bmAssessmentScore: bmAssessmentScore?.value?.length ? bmAssessmentScore?.value : "",
      ...(getStatus ? { pmStatus: pmAssessmentScore?.status, bmStatus: bmAssessmentScore?.status } : {})
    };
  };

  showModal(message, passedIntervention) {
    this.setState({
      bsModalShow: true,
      bsModalContent: message,
      bsModalClassNames: `notice notice-${passedIntervention ? "success" : "warning"}`,
      bsModalIcon: `noticeIcon fa fa-2x fa-${passedIntervention ? "thumbs-o-up" : "warning"}`
    });
  }

  calculateScores() {
    const { siteId, assessmentResult } = this.props;
    if (!siteId || !assessmentResult) {
      return;
    }
    this.setState({ isRecalculatingStats: true });
    Meteor.call(
      "calculateScoreResult",
      {
        assessmentResultId: assessmentResult._id
      },
      (err, res) => {
        if (err) {
          ninjalog.error({
            msg: "calculateScoreResult -> err",
            val: err
          });
        } else {
          this.setState({ calculatedAssessmentResults: res, isRecalculatingStats: false });
        }
      }
    );
  }

  saveScores = e => {
    e.preventDefault();
    this.context.clearUnusualHighScoreFields();
    const { siteId, assessmentResult, studentInfo } = this.props;
    if (!siteId || !assessmentResult) {
      return;
    }
    this.setState({ isSavingScores: true });
    Meteor.call(
      "saveScoreResult",
      {
        assessmentResultId: assessmentResult._id
      },
      err => {
        this.setState({ isSavingScores: false });
        if (!err) {
          this.setState({ calculatedAssessmentResults: null });
          Meteor.call("assignStudentToSkillGroup", {
            siteId,
            studentId: studentInfo._id
          });
        }
      }
    );
  };

  hideModal = () => {
    this.setState({
      bsModalShow: false,
      bsModalContent: "",
      bsModalClassNames: "",
      bsModalIcon: ""
    });
  };

  renderChart({ scores, type, timeRange }) {
    const chartOptions = {
      chartType: "line",
      title: "",
      height: 400,
      xAxisTitle: "Week",
      yAxisTitle: "Score",
      paddingTop: 10,
      marginTop: 20,
      marginRight: 10,
      timeRange
    };
    return (
      <ProgressMonitoringChart
        pmName="Progress Monitoring Scores"
        scores={scores}
        chartId={this.props.studentInfo._id + type}
        options={chartOptions}
        type="Chart"
        skillType={type}
      />
    );
  }

  renderSkillGraph = (pmScoresObj, bmScoresObj) => {
    const { studentROI, benchmarkScoreId, assessmentScoreId } = this.props;
    const dateNowTimestamp = new Date().getTime();
    const timeRange = getTimeRange([
      ...(pmScoresObj.dates?.length ? pmScoresObj.dates : [dateNowTimestamp]),
      ...(bmScoresObj.dates?.length ? bmScoresObj.dates : [dateNowTimestamp])
    ]);
    return (
      <div className="skill-graph clearfix">
        {benchmarkScoreId !== assessmentScoreId ? (
          <div>
            <h5>
              <small>
                <i className="fa fa-bullseye" /> Intervention Skill:
              </small>{" "}
              {pmScoresObj.skillName}
            </h5>
            {this.renderChart({
              scores: pmScoresObj,
              type: "PMChart",
              timeRange
            })}
          </div>
        ) : null}
        <div>
          <h5>
            <small>
              <i className="fa fa-trophy" /> Goal Skill:
            </small>{" "}
            {bmScoresObj.skillName}
            {studentROI ? <span className="roi float-end">{`Rate of Improvement: ${studentROI}`}</span> : null}
          </h5>
          {this.renderChart({
            scores: bmScoresObj,
            type: "BMChart",
            timeRange
          })}
        </div>
      </div>
    );
  };

  renderScoreSubmitButton() {
    if (Object.keys(this.context.getUnusualHighScoreFields(this.props.assessmentResult._id)).length) {
      return (
        <button type="button" className="btn btn-default">
          <i className="fa fa-warning fa-right" />
          Fix Unusual High Score
        </button>
      );
    }

    const { pmAssessmentScore, bmAssessmentScore, bmStatus, pmStatus } = this.getCurrentScores({
      ...this.props,
      getStatus: true
    });
    if (pmAssessmentScore?.length && bmAssessmentScore?.length && pmStatus === "COMPLETE" && bmStatus === "COMPLETE") {
      return (
        <div className="col-sm-12">
          <button
            key="1"
            className="btn btn-success"
            data-testid="saveScoreBtn"
            disabled={this.state.isSavingScores}
            onClick={this.saveScores}
          >
            <i className="fa fa-save fa-right" />
            Save Results
          </button>
        </div>
      );
    }
    return (
      <button type="button" className="btn btn-default">
        <i className="fa fa-warning fa-right" />
        Enter Scores to Continue
      </button>
    );
  }

  renderScoreEntryRowCell = ({ className, scoreType }) => {
    const {
      benchmarkAssessmentName,
      assessmentName,
      assessmentResult,
      isReadOnly,
      studentInfo,
      assessmentId,
      bmAssessmentId,
      siteId,
      inActiveSchoolYear
    } = this.props;
    const {
      assessmentTargets: [skillInstructional, skillMastery],
      benchmarkAssessmentTargets: [goalInstructional, goalMastery]
    } = assessmentResult.individualSkills;
    const targets = {
      pm: [skillInstructional, skillMastery],
      bm: [goalInstructional, goalMastery]
    };
    const {
      env: { CI }
    } = this.context;
    const pmAssessmentScoreLimit = {
      assessmentId: assessmentResult.individualSkills.assessmentId,
      limit: assessmentResult.individualSkills.assessmentTargets[1] * 5
    };
    const bmAssessmentScoreLimit = {
      assessmentId: assessmentResult.individualSkills.benchmarkAssessmentId,
      limit: assessmentResult.individualSkills.benchmarkAssessmentTargets[1] * 5
    };

    const pmAssessmentScore = assessmentResult.scores.find(
      sc => sc.assessmentId === assessmentId && sc.studentId === studentInfo._id
    );
    const bmAssessmentScore = assessmentResult.scores.find(
      sc => sc.assessmentId === bmAssessmentId && sc.studentId === studentInfo._id
    );

    const scorePostfix = scoreType === "bm" ? "bmScore" : "pmScore";
    return (
      <div className={`${className} relativeWrapper`}>
        {shouldUseDevMode(CI) ? (
          <small className="w6 debug-small">
            At: ({targets[scoreType][0]}), Above: ({targets[scoreType][1]})
          </small>
        ) : null}
        <ScoreEntryRowCell
          cellId={`${studentInfo._id}_${scorePostfix}`}
          assessmentScoreLimit={scoreType === "bm" ? bmAssessmentScoreLimit : pmAssessmentScoreLimit}
          assessmentResultId={assessmentResult._id}
          assessmentScore={scoreType === "bm" ? bmAssessmentScore : pmAssessmentScore}
          inActiveSchoolYear={inActiveSchoolYear}
          isReadOnly={isReadOnly}
          index={scoreType === "bm" ? 1 : 0}
          rowScoreCount={1}
          seleniumSelectorText={scoreType === "bm" ? "enterGoalScore" : "enterSkillScore"}
          siteId={siteId}
        />
        <label>{scoreType === "bm" ? benchmarkAssessmentName : assessmentName}</label>
      </div>
    );
  };

  togglePreviewSkillTreeModal = () => {
    this.setState(state => ({ shouldDisplayPreviewSkillTreeModal: !state.shouldDisplayPreviewSkillTreeModal }));
  };

  render() {
    const {
      assessmentId,
      assessmentName,
      measureNumber,
      hasVideo,
      protocolAssessment,
      assessmentScoreId,
      benchmarkPeriodId,
      benchmarkScoreId,
      bmAssessmentId,
      compiledBMScores,
      compiledPMScores,
      feedbackPMScores,
      inActiveSchoolYear,
      interventionsAvailable,
      monitorBMAssessmentVendorId,
      monitorProtocolAssessmentVendorId,
      siteId,
      studentGroupName,
      studentInfo,
      studentGroupId
    } = this.props;
    const {
      calculatedAssessmentResults,
      isRecalculatingStats,
      bsModalShow,
      bsModalClassNames,
      bsModalContent,
      bsModalIcon
    } = this.state;
    // Make deep copies of these props.
    const pmFeedbackObj = cloneDeep(feedbackPMScores);
    const pmScoresObj = cloneDeep(compiledPMScores);
    const bmScoresObj = cloneDeep(compiledBMScores);

    const studentFullName = `${studentInfo.identity.name.firstName} ${studentInfo.identity.name.lastName}`;

    if (calculatedAssessmentResults) {
      // need to add new potential nodes to the array(s)
      const { customDate } = get(getMeteorUser(), "profile", {});
      const currentDate = getCurrentDate(customDate, studentInfo.orgid);
      if (
        calculatedAssessmentResults.measures[1]?.studentResults[0] &&
        (pmScoresObj.scores.length > 0 || pmScoresObj.assessmentId !== bmScoresObj.bmAssessmentId)
      ) {
        const pmScore = calculatedAssessmentResults.measures[1].studentResults[0].score;
        pmScoresObj.scores.push(parseInt(pmScore));
        pmScoresObj.dates.push(currentDate.getTime());
      }
      if (calculatedAssessmentResults.measures[0]?.studentResults[0]) {
        const bmScore = calculatedAssessmentResults.measures[0].studentResults[0].score;
        bmScoresObj.scores.push(parseInt(bmScore));
        bmScoresObj.dates.push(currentDate.getTime());
      }
    }
    const translatedBenchmarkPeriod = translateBenchmarkPeriod(benchmarkPeriodId);
    const benchmarkPeriodName = (translatedBenchmarkPeriod && translatedBenchmarkPeriod.name) || "";

    const { pmAssessmentScore, bmAssessmentScore } = this.getCurrentScores(this.props);

    const {
      env: { CI }
    } = this.context;
    const shouldRenderAdditionalInfo = shouldUseDevMode(CI);
    const shouldDisplayBoostItButton =
      protocolAssessment?.ir?.measure &&
      protocolAssessment?.ir?.measure === protocolAssessment?.monitorAssessmentMeasure &&
      protocolAssessment._id === studentInfo.currentSkill.assessmentId;

    return (
      <div
        className="skill-container individual pre-intervention clearfix"
        data-benchmark-period-name={benchmarkPeriodName}
        data-student-id={studentInfo._id}
        data-testid="individual-intervention-skill"
      >
        <div className="skill-details">
          {studentInfo?.currentSkill?.assessmentId && !studentInfo?.currentSkill?.message?.dismissed ? (
            <InterventionMessage
              entityId={studentInfo._id}
              entityType="Student"
              message={studentInfo.currentSkill.message}
              name={studentInfo.identity.name}
            />
          ) : null}
          <strong>{studentFullName}</strong> is currently practicing the skill
          <h3 className="w9 d-flex gap-3 vertical-align-middle">
            {assessmentName}
            {shouldRenderAdditionalInfo ? (
              <span className="w5 font-18">{` (AM#${monitorProtocolAssessmentVendorId})`}</span>
            ) : null}
            <div>
              <SkillVideoButton measureNumber={measureNumber} skillName={assessmentName} hasVideo={hasVideo} />
            </div>
          </h3>
          <div className="d-flex justify-content-between">
            <div className="d-flex flex-row gap-1">
              <PrintInterventionsDropdown
                assessmentMeasure={monitorBMAssessmentVendorId}
                protocolMeasure={monitorProtocolAssessmentVendorId}
                interventionsAvailable={interventionsAvailable}
                assessmentId={assessmentId}
                benchmarkAssessmentId={bmAssessmentId}
                benchmarkPeriodId={benchmarkPeriodId}
                materialsType="intervention-packet"
                grade={studentInfo.grade}
                studentName={studentFullName}
                initialText="Select Activity"
                loadingText="Custom building classwide intervention materials. Just a moment please."
                groupName={studentGroupName}
                studentGroupId={studentGroupId}
              />
              {shouldUseDevMode(CI, ["LOCAL", "DEV", "QA", "STAGE"]) ? (
                <React.Fragment>
                  <Button variant="outline-blue" className="skill-button" onClick={this.togglePreviewSkillTreeModal}>
                    <TooltipWrapper
                      text={<i className="fa fa-sitemap cursor-pointer" />}
                      tooltipText={"Individual Progress Monitoring Tree Preview"}
                      placement="top"
                      customClassName=""
                      isClickTriggerEnabled={false}
                    />
                  </Button>
                  {this.state.shouldDisplayPreviewSkillTreeModal ? (
                    <PreviewSkillTreeModal
                      showModal={this.state.shouldDisplayPreviewSkillTreeModal}
                      onCloseModal={this.togglePreviewSkillTreeModal}
                      params={{
                        grade: studentInfo?.grade || "",
                        benchmarkPeriodId: benchmarkPeriodId || "",
                        benchmarkAssessmentId: bmAssessmentId || "",
                        isPreviewOnly: true
                      }}
                    />
                  ) : null}
                </React.Fragment>
              ) : null}
            </div>
            {shouldDisplayBoostItButton && (
              <div>
                <IncrementalRehearsalButton
                  studentId={studentInfo._id}
                  orgid={studentInfo.orgid}
                  siteId={siteId}
                  incrementalRehearsal={protocolAssessment.ir}
                  datesOfCompletion={studentInfo?.ir?.[protocolAssessment.ir.measure]?.dates}
                />
              </div>
            )}
          </div>
        </div>
        <div className="skill-score-entry">
          {inActiveSchoolYear ? (
            <form className="form-horizontal text-center" autoComplete="off">
              <div className="container">
                {benchmarkScoreId === assessmentScoreId ? (
                  <div className="form-group row">
                    {this.renderScoreEntryRowCell({
                      className: "col-sm-12",
                      scoreType: "bm"
                    })}
                    {this.renderScoreSubmitButton()}
                  </div>
                ) : (
                  <div className="form-group row">
                    {this.renderScoreEntryRowCell({
                      className: "col-sm-6",
                      scoreType: "pm"
                    })}
                    {this.renderScoreEntryRowCell({
                      className: "col-sm-6",
                      scoreType: "bm"
                    })}
                    {this.renderScoreSubmitButton()}
                  </div>
                )}
              </div>
            </form>
          ) : null}
        </div>
        <div className="clearfix" />
        {!isRecalculatingStats &&
        calculatedAssessmentResults?.measures[0]?.studentResults[0] &&
        pmAssessmentScore.length &&
        bmAssessmentScore.length
          ? renderResultFeedback(
              calculatedAssessmentResults,
              bmScoresObj,
              pmFeedbackObj,
              studentFullName,
              bmAssessmentId,
              benchmarkPeriodId
            )
          : null}
        {this.renderSkillGraph(pmScoresObj, bmScoresObj)}
        <ButtonToolbar>
          <Modal show={bsModalShow} onHide={this.hideModal} dialogClassName={bsModalClassNames}>
            <Modal.Body>
              <i id="bsModalClose" className="fa fa-close fa-sm" onClick={this.hideModal} />
              {bsModalContent}
              <i className={bsModalIcon} />
            </Modal.Body>
          </Modal>
        </ButtonToolbar>
      </div>
    );
  }
}

IndividualIntervention.propTypes = {
  assessmentId: PropTypes.string,
  assessmentName: PropTypes.string,
  assessmentResult: PropTypes.object,
  assessmentScoreId: PropTypes.string,
  benchmarkAssessmentName: PropTypes.string,
  benchmarkPeriodId: PropTypes.string,
  benchmarkScoreId: PropTypes.string,
  bmAssessmentId: PropTypes.string,
  compiledBMScores: PropTypes.object,
  compiledPMScores: PropTypes.object,
  feedbackPMScores: PropTypes.object,
  protocolAssessment: PropTypes.object,
  hasVideo: PropTypes.bool,
  inActiveSchoolYear: PropTypes.bool,
  interventionsAvailable: PropTypes.array,
  isFetchingSkillGroups: PropTypes.bool,
  isReadOnly: PropTypes.bool,
  measureNumber: PropTypes.string,
  monitorBMAssessmentVendorId: PropTypes.string,
  monitorProtocolAssessmentVendorId: PropTypes.string,
  preAssessmentResultId: PropTypes.string,
  refreshScroll: PropTypes.func,
  schoolYear: PropTypes.number,
  siteId: PropTypes.string,
  skillGroups: PropTypes.array,
  studentGroupId: PropTypes.string,
  studentGroupName: PropTypes.string,
  studentInfo: PropTypes.object,
  studentROI: PropTypes.string
};

const IndividualInterventionContentContainer = withRouter(
  withTracker(props => {
    const { assessmentResult, studentInfo, assessmentName, match, studentGroupId } = props;
    let monitorProtocolAssessmentVendorId;
    let protocolAssessment;
    let bmAssessment;
    const studentHistory = studentInfo.history || [];
    if (studentInfo) {
      protocolAssessment = Assessments.findOne({
        _id: assessmentResult.individualSkills.assessmentId
      });
      if (assessmentResult.individualSkills.benchmarkAssessmentId) {
        bmAssessment = Assessments.findOne({
          _id: assessmentResult.individualSkills.benchmarkAssessmentId
        });
      }
    }

    if (protocolAssessment && bmAssessment) {
      // Skill being worked on (PM)
      monitorProtocolAssessmentVendorId = protocolAssessment.monitorAssessmentMeasure;
    }

    const { assessmentId } = assessmentResult.individualSkills;
    const bmAssessmentId = assessmentResult.individualSkills.benchmarkAssessmentId;
    const feedbackPMScores = {
      scores: [],
      assessmentId
    };
    const compiledScoresShell = {
      skillName: `${assessmentResult.individualSkills.assessmentName}`,
      instructionalTarget: assessmentResult.individualSkills.assessmentTargets[0],
      masteryTarget: assessmentResult.individualSkills.assessmentTargets[1]
    };
    const compiledPMScores = {
      ...compiledScoresShell,
      scores: [],
      dates: [],
      assessmentId
    };
    const compiledBMScores = {
      ...compiledScoresShell,
      scores: [],
      dates: [],
      skillName: `${assessmentResult.individualSkills.benchmarkAssessmentName}`,
      instructionalTarget: assessmentResult.individualSkills.benchmarkAssessmentTargets[0],
      masteryTarget: assessmentResult.individualSkills.benchmarkAssessmentTargets[1],
      bmAssessmentId
    };
    const rawBMdates = [];
    filterOutHistoryItemsThatDidNotLeadToIntervention(studentHistory)
      .sort((a, b) => a.whenEnded.on - b.whenEnded.on)
      .forEach(h => {
        if (h.assessmentId === assessmentId && h.assessmentId !== bmAssessmentId) {
          const assMeasure = h.assessmentResultMeasures.find(m => m.assessmentId === assessmentId);
          if (assMeasure && assMeasure.medianScore !== "") {
            feedbackPMScores.scores.push({
              score: Number(assMeasure.medianScore),
              bmAssId: h.benchmarkAssessmentId,
              bmPeriodId: h.benchmarkPeriodId,
              date: h.whenEnded.date
            });
            if (!h.interventions.length) {
              compiledPMScores.hasDrillDownScore = true;
            }
            compiledPMScores.scores.push(Number(assMeasure.medianScore));
            compiledPMScores.dates.push(new Date(h.whenEnded.date).getTime());
          }
        }
        if (h.benchmarkAssessmentId === bmAssessmentId) {
          const assMeasure = h.assessmentResultMeasures.find(m => m.assessmentId === bmAssessmentId);
          if (assMeasure && assMeasure.medianScore !== "") {
            compiledBMScores.scores.push(Number(assMeasure.medianScore));
            compiledBMScores.dates.push(new Date(h.whenEnded.date).getTime());
            rawBMdates.push(h.whenEnded.date);
          }
        }
      });

    const assessmentScore = assessmentResult.scores.find(s => s.assessmentId === assessmentId);
    const benchmarkScore = assessmentResult.scores.find(s => s.assessmentId === bmAssessmentId);

    const studentGroup = StudentGroups.findOne(studentGroupId);
    const roi = calculateRoI(rawBMdates, compiledBMScores.scores);

    return {
      assessmentName,
      measureNumber: protocolAssessment?.monitorAssessmentMeasure,
      hasVideo: protocolAssessment?.hasVideo,
      currentAssessmentResultScores: assessmentResult.scores,
      protocolAssessment,
      assessmentId,
      bmAssessmentId,
      benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
      assessmentScoreId: assessmentScore && assessmentScore._id,
      benchmarkScoreId: benchmarkScore && benchmarkScore._id,
      monitorProtocolAssessmentVendorId,
      monitorBMAssessmentVendorId: bmAssessment.monitorAssessmentMeasure,
      interventionsAvailable: assessmentResult.individualSkills.interventions.map(i => i.interventionAbbrv),
      feedbackPMScores,
      compiledPMScores,
      compiledBMScores,
      studentROI: roi,
      studentGroupName: get(studentGroup, "name", ""),
      siteId: match.params.siteId
    };
  })(IndividualIntervention)
);

export default IndividualInterventionContentContainer;
