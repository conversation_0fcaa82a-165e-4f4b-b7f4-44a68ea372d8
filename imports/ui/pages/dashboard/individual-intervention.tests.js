import { Meteor } from "meteor/meteor";
import { assert } from "chai";
import { renderResultFeedback } from "./individual-intervention.jsx";

// helper function for children props weirdness
// possibly move to a utility file if other test scenarios could use it
function childrenArray(reactElement) {
  if (!Array.isArray(reactElement.props.children)) {
    return reactElement.props.children ? [reactElement.props.children] : [];
  }
  return reactElement.props.children;
}

// alertElementCb -> using callbacks so that it can leverage closures modified by beforeEach calls
function greenAlertBoxPresent(alertElementCb, calcAssResultsWithScore) {
  it("should display GREEN alert-success-dark alert box", () => {
    assert.match(
      alertElementCb(calcAssResultsWithScore).props.className,
      /alert-success-dark/,
      "message did not have the green alert class"
    );
  });
}
function yellowAlertBoxPresent(alertElementCb, withScores) {
  it("should display YELLOW alert-warning-dark alert box", () => {
    assert.match(
      alertElementCb(withScores).props.className,
      /alert-warning-dark/,
      "message did not have the yellow alert class"
    );
  });
}

const testBmAssId = "testBmAssId";
const testBmPeriodId = "testBmPeriodId";

// pm score shell builder
function makeTestPMScore(score) {
  return {
    bmPeriodId: testBmPeriodId,
    bmAssId: testBmAssId,
    score
  };
}

// default parameters for the renderResultFeedback function
// rename if testing other functions in this file (yes!)
function defaults() {
  return {
    calcAssResults: {
      measures: [
        {
          studentResults: [{ score: 0 }],
          targetScores: [0, 0, 0]
        },
        {
          studentResults: [{ score: 0 }],
          targetScores: [0, 0, 0]
        }
      ]
    },
    bmScoresObj: {
      scores: [0],
      bmAssessmentId: "differentAssessment"
    },
    pmScoresObj: {
      scores: [makeTestPMScore(0)],
      assessmentId: "testAssessmentId"
    }
  };
}

// get the alert child node that has the testable information from the renderResultFeedback function
function getAlertElementFromTestInput(calcAssResults, bmScoresObj, pmScoresObj, studentFullName, bmAssId, bmPeriodId) {
  const testResult = renderResultFeedback(
    calcAssResults,
    bmScoresObj,
    pmScoresObj,
    studentFullName,
    bmAssId,
    bmPeriodId
  );
  const testResultChildren = childrenArray(testResult);
  return testResultChildren.find(child => child.type.displayName === "Alert");
}

// resets the pmScoresObj and bmScoresObj between tests
function setupScoresObjParams(practicingGoalSkill) {
  const { bmScoresObj, pmScoresObj } = defaults();
  if (practicingGoalSkill) {
    bmScoresObj.bmAssessmentId = pmScoresObj.assessmentId;
  }
  return { bmScoresObj, pmScoresObj };
}

// tests all the scenarios of entering scores and crossing thresholds and leveraging existing history in parameters
// practicingGoalSkill -> if true, tests all scenarios with the goal skill and intervention skill the same (1 textbox)
//                        else tests as if the intervention skill is on its own, separated from the goal skill
function checkSkillThresholdFeedBackFacts({ practicingGoalSkill }) {
  const practicingMeasureIndex = practicingGoalSkill ? 0 : 1;
  let bmScoresObj;
  let pmScoresObj;
  beforeEach(() => {
    ({ bmScoresObj, pmScoresObj } = setupScoresObjParams(practicingGoalSkill));
  });
  // ensure goal skill was not passed by default
  const calcAssResults = () => ({
    ...defaults().calcAssResults,
    measures: [
      {
        studentResults: [
          {
            score: 0
          }
        ],
        targetScores: [10, 50, 200]
      },
      {
        studentResults: [
          {
            score: 0
          }
        ],
        targetScores: [10, 50, 200]
      }
    ]
  });
  const continuePracticingAlertBoxPresent = (alertElementCb, withScores) => {
    it("should display We will continue practicing this Intervention Skill", () => {
      const alertElementChildren = childrenArray(alertElementCb(withScores));
      const verbageNode = alertElementChildren.find(
        child => child && child.toString().match(/We will continue practicing this Intervention Skill/i)
      );
      assert.isDefined(verbageNode, "could not find continue practicing verbage");
    });
  };
  describe("and the student has not passed any target thresholds on the intervention skill", () => {
    const alertElementCb = calcAssResultsWithScore =>
      getAlertElementFromTestInput(calcAssResultsWithScore || calcAssResults(), bmScoresObj, pmScoresObj);
    describe("and the student's new score does not meet any target thresholds", () => {
      yellowAlertBoxPresent(alertElementCb);
      continuePracticingAlertBoxPresent(alertElementCb);
    });
    describe("and the student's new score passes a threshold", () => {
      const calcAssResultsWithScore = calcAssResults();
      const crossThreshold = (score, matchString) => {
        describe(`with a score of ${score}`, () => {
          // get intervention skill measure (1)
          beforeEach(() => {
            calcAssResultsWithScore.measures[practicingMeasureIndex].studentResults[0].score = score;
          });
          // const alertElement = getAlertElementFromTestInput(calcAssResultsWithScore, bmScoresObj, pmScoresObj);
          greenAlertBoxPresent(alertElementCb, calcAssResultsWithScore);
          it(`should display '${matchString}'`, () => {
            const alertElementChildren = childrenArray(alertElementCb(calcAssResultsWithScore));
            const verbageNode = alertElementChildren.find(
              child => child && child.toString().match(new RegExp(matchString, "i"))
            );
            assert.isDefined(verbageNode);
          });
        });
      };
      // test edge cases as well as normal cases. the values should be in the range of the targetscores above
      [10, 20, 13, 49].forEach(score => {
        crossThreshold(score, "There will be new practice materials next time");
      });
      [50, 100, 1000, 51].forEach(score => {
        if (practicingGoalSkill) {
          crossThreshold(score, "should move on to a new goal skill");
        } else {
          crossThreshold(score, "will be moving on to a new Intervention Skill");
        }
      });
    });
  });
  describe("and the student has passed the first threshold on the intervention skill previously", () => {
    const alertElementWithHistoryCb = withScores => {
      if (practicingGoalSkill) {
        bmScoresObj.scores.push(8);
        bmScoresObj.scores.push(10);
        // last score gets chopped off by logic, remove this if that gets changed
        bmScoresObj.scores.push(15);
      } else {
        pmScoresObj.scores.push(makeTestPMScore(8));
        pmScoresObj.scores.push(makeTestPMScore(10));
      }

      if (withScores) {
        const calcAssResultsWithScore = calcAssResults();
        // get intervention skill measure (1)
        // give it a score that also passes threshold 1
        calcAssResultsWithScore.measures[practicingMeasureIndex].studentResults[0].score = 15;
        return getAlertElementFromTestInput(
          calcAssResultsWithScore,
          bmScoresObj,
          pmScoresObj,
          "",
          testBmAssId,
          testBmPeriodId
        );
      }
      return getAlertElementFromTestInput(calcAssResults(), bmScoresObj, pmScoresObj);
    };
    describe("and the student's new score does not pass a threshold", () => {
      yellowAlertBoxPresent(alertElementWithHistoryCb, false);
      continuePracticingAlertBoxPresent(alertElementWithHistoryCb, false);
    });
    describe("and the student's new score passes the first threshold", () => {
      yellowAlertBoxPresent(alertElementWithHistoryCb, true);
      continuePracticingAlertBoxPresent(alertElementWithHistoryCb, true);
    });
  });
}

if (Meteor.isClient) {
  describe("imports/ui/pages/dashboard/individual-intervention.jsx tests", () => {
    describe("renderResultFeedback tests", () => {
      it("returns a p tag element", () => {
        const testResult = renderResultFeedback(
          defaults().calcAssResults,
          defaults().bmScoresObj,
          defaults().pmScoresObj
        );
        assert.equal(testResult.type, "div", "did not return a div element");
      });
      describe("when the intervention skill is not the same as the goal skill", () => {
        checkSkillThresholdFeedBackFacts({ practicingGoalSkill: false });
      });
      describe("when the intervention skill is the same as the goal skill", () => {
        checkSkillThresholdFeedBackFacts({ practicingGoalSkill: true });
      });
    });
  });
}
