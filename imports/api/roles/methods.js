import get from "lodash/get";
import { getMeteorUser } from "../utilities/utilities";

export const isSupportUser = async () =>
  get(await getMeteorUser(), "profile.siteAccess[0].role") === "arbitraryIdsupport";

export const isUniversalCoach = async () =>
  get(await getMeteorUser(), "profile.siteAccess[0].role") === "arbitraryIduniversalCoach";

export const roles = [
  {
    _id: "arbitraryIddataAdmin",
    label: "dataAdmin",
    name: "dataAdmin"
  },
  {
    _id: "arbitraryIdsuperAdmin",
    label: "superAdmin",
    name: "superAdmin"
  },
  {
    _id: "arbitraryIdteacher",
    label: "teacher",
    name: "teacher"
  },
  {
    _id: "arbitraryIdadmin",
    label: "admin",
    name: "admin"
  },
  {
    _id: "arbitraryIdsupport",
    label: "support",
    name: "support"
  },
  {
    _id: "arbitraryIduniversalCoach",
    label: "universalCoach",
    name: "universalCoach"
  },
  {
    _id: "arbitraryIduniversalDataAdmin",
    label: "universalDataAdmin",
    name: "universalDataAdmin"
  },
  {
    _id: "arbitraryIddownloader",
    label: "downloader",
    name: "downloader"
  }
];

export const isAdminOrUniversalCoach = async (siteId, siteAccess = []) => {
  if (await isUniversalCoach()) {
    return true;
  }
  const currentSite = siteAccess.find(singleSite => singleSite.siteId === siteId);
  return currentSite && currentSite.role === "arbitraryIdadmin";
};

export default roles;
