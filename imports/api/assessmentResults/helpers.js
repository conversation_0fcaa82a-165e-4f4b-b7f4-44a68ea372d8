import get from "lodash/get";
import moment from "moment";
import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { extend, difference, cloneDeep, intersection, keyBy } from "lodash";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { StudentGroups } from "../studentGroups/studentGroups";
import { getEnrolledStudentIds, getIndividualResultsMessage } from "./utilities";
import { getHistoryFieldName, getMeteorUserId, hasGroupPassedClasswideIntervention } from "../utilities/utilities";
import { AssessmentResults } from "./assessmentResults";
import { Students } from "../students/students";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";

export function reformatAssessmentResults(assessmentResults) {
  return assessmentResults.map(assessmentResult => {
    const updatedObject = { ...assessmentResult };
    updatedObject.whenEnded = assessmentResult.lastModified;
    updatedObject.assessmentResultMeasures = assessmentResult.measures;
    delete updatedObject.lastModified;
    delete updatedObject.measures;
    return updatedObject;
  });
}

export function canForceGroupToClasswideIntervention(studentGroup) {
  const isInClasswideIntervention = get(studentGroup, "currentClasswideSkill.benchmarkPeriodId");
  if (isInClasswideIntervention) {
    return false;
  }
  const hasClasswideHistory = (studentGroup.history || []).some(item => item.type === "classwide");
  return !hasClasswideHistory;
}

/*
Four Week Rules Individual Intervention Recommendations
1) Four weeks have passed since the first progress monitoring scores were saved for a classwide intervention.
For example:
Class 1 is scheduled for classwide intervention on 8/2. They complete classwide interventions during the week of 8/2-8/6
 and progress monitoring scores are entered for the first time on 8/6. The first time that students will be eligible
 to be recommended for individual intervention will be 28 days after 8/6, or 9/3.

2) After rule #1 is met, if the class scores at or above the mastery target and a student scores
in the frustrational range (below the instructional target) the student is recommended for individual intervention.
In this example the student can be recommended for individual intervention if they score in the frustrational range
and the class moved up a skill any time from 9/3 on.
For example:
classwide intervention progress monitoring scores are entered on Friday, 9/10.
At this time the class median hits mastery and the class moves up to the next skill.
The score entered for student A on 9/10 is below the instructional target.
Student A is recommended for individual intervention on 9/10.

3) If the student does not have a score entered for the date that the class moved up to a new skill
use the most recent progress monitoring score entered for this skill (after rule #1).
If the student has no progress monitoring scores entered for this skill they are not eligible.
For example:
if student A has no score entered on 9/10 when the class moved up from skill 2 to skill 3,
but they had a progress monitoring score entered for skill 2 on 9/8 that was in the frustrational range
they will be recommended for individual intervention.
If the student has no scores on skill 2 they are not eligible.
*/
export function getRecommendationsForIndividualIntervention({ history = [], currentDate = new Date() } = {}) {
  const classwideInterventionHistoryItems = history.filter(item => item.type === "classwide");
  if (!classwideInterventionHistoryItems.length) {
    return undefined;
  }
  const oldestClasswideHistoryItem = classwideInterventionHistoryItems[classwideInterventionHistoryItems.length - 1];

  if (!have4WeeksPassedSinceDate(oldestClasswideHistoryItem.whenEnded.date, currentDate)) {
    return undefined;
  }

  const atLeastFourWeeksHistory = classwideInterventionHistoryItems.filter(historyItem => {
    return have4WeeksPassedSinceDate(oldestClasswideHistoryItem.whenEnded.date, historyItem.whenEnded.date);
  });

  if (!atLeastFourWeeksHistory.length) {
    return undefined;
  }
  const [latestHistoryItem] = atLeastFourWeeksHistory;

  let missingScoresRecommendationsResult = null;
  const mostRecentPassingHistoryItem = atLeastFourWeeksHistory.find(ci => {
    const arm = ci.assessmentResultMeasures[0];
    return hasGroupPassedClasswideIntervention({
      medianScore: arm.medianScore,
      totalStudentsAssessed: arm.totalStudentsAssessed,
      targetScores: arm.targetScores,
      studentScores: arm.studentScores,
      numberOfEnrolledStudents: ci.enrolledStudentIds.length
    });
  });
  if (!mostRecentPassingHistoryItem) {
    return { idsOfStudentsInFrustrationalRangeInPast: undefined, missingScoresRecommendationsResult: undefined };
  }

  cloneDeep(atLeastFourWeeksHistory)
    .reverse()
    .forEach((historyItem, index) => {
      const hasSkillChanged = atLeastFourWeeksHistory[index - 1]?.assessmentId === historyItem.assessmentId;
      missingScoresRecommendationsResult = getMissingScoresRecommendations({
        historyItem,
        missingScoresRecommendationsResult,
        mostRecentPassingHistoryItem,
        hasSkillChanged
      });
    });

  missingScoresRecommendationsResult.missingScoresRecommendations =
    intersection(
      missingScoresRecommendationsResult.twoAnySkillsAbsentWhenMovedUpStudentIds,
      missingScoresRecommendationsResult.absentStudentIdsNotMeetingTarget
    ) || [];

  let historyItemsForLatestSkill;
  if (mostRecentPassingHistoryItem.assessmentId !== latestHistoryItem.assessmentId) {
    historyItemsForLatestSkill = cloneDeep(atLeastFourWeeksHistory)
      .filter(item => item.assessmentId === mostRecentPassingHistoryItem.assessmentId)
      .reverse();
  } else {
    historyItemsForLatestSkill = cloneDeep(atLeastFourWeeksHistory)
      .filter(item => item.assessmentId === latestHistoryItem.assessmentId)
      .reverse();
  }

  let idsOfStudentsInFrustrationalRangeInPast = [];
  historyItemsForLatestSkill.forEach(item => {
    if (have4WeeksPassedSinceDate(oldestClasswideHistoryItem.whenEnded.date, item.whenEnded.date)) {
      idsOfStudentsInFrustrationalRangeInPast = getIdsOfStudentsEligibleForIndividualIntervention({
        classwideInterventionMeasure: item.assessmentResultMeasures[0],
        idsOfStudentsInFrustrationalRangeInPast
      });
    }
  });
  idsOfStudentsInFrustrationalRangeInPast = intersection(
    idsOfStudentsInFrustrationalRangeInPast,
    latestHistoryItem.enrolledStudentIds
  );
  return { idsOfStudentsInFrustrationalRangeInPast, missingScoresRecommendationsResult };
}

function initializeAbsentOnSameSkillInFrustrationalRange(
  enrolledStudentIds,
  absentOnSameSkillInFrustrationalRangeStudentCountByStudentId
) {
  return enrolledStudentIds.reduce((a, studentId) => {
    const temp = a;
    if (absentOnSameSkillInFrustrationalRangeStudentCountByStudentId?.[studentId]) {
      temp[studentId] = absentOnSameSkillInFrustrationalRangeStudentCountByStudentId[studentId];
    } else {
      temp[studentId] = { anyScoreAtOrAbove: null };
    }
    return temp;
  }, {});
}

function updateAbsentStudentStatusBasedOnOutcome(historyItem, updatedMissingScoresRecommendations) {
  const { enrolledStudentIds } = historyItem;
  let { absentOnSameSkillInFrustrationalRangeStudentCountByStudentId } = updatedMissingScoresRecommendations;
  absentOnSameSkillInFrustrationalRangeStudentCountByStudentId = initializeAbsentOnSameSkillInFrustrationalRange(
    enrolledStudentIds,
    absentOnSameSkillInFrustrationalRangeStudentCountByStudentId
  );
  historyItem.assessmentResultMeasures[0].studentResults.forEach(result => {
    const absentStudentDoc = absentOnSameSkillInFrustrationalRangeStudentCountByStudentId[result.studentId];
    if (!absentStudentDoc.anyScoreAtOrAbove) {
      absentStudentDoc.anyScoreAtOrAbove = result.individualRuleOutcome !== "below";
    }
  });
  return absentOnSameSkillInFrustrationalRangeStudentCountByStudentId;
}

function initializeAbsentWhenMovedUpStudentCount(enrolledStudentIds, updatedMissingScoresRecommendations) {
  const { absentWhenMovedUpStudentCountByStudentId } = updatedMissingScoresRecommendations;
  const countByStudentId = {};
  enrolledStudentIds.forEach(studentId => {
    if (absentWhenMovedUpStudentCountByStudentId?.[studentId]) {
      countByStudentId[studentId] = absentWhenMovedUpStudentCountByStudentId[studentId];
    } else {
      countByStudentId[studentId] = {
        totalAbsentCount: 0,
        absentCountSinceScoredAtOrAbove: 0,
        wasAbsentOnMostRecentSkillWhenMovedUp: false
      };
    }
  });
  return countByStudentId;
}

function updateStudentsAbsentWhenMovedUp(enrolledStudentIds, updatedMissingScoresRecommendations) {
  const { absentWhenMovedUpStudentCountByStudentId } = updatedMissingScoresRecommendations;
  enrolledStudentIds.forEach(studentId => {
    absentWhenMovedUpStudentCountByStudentId[studentId].wasAbsentOnMostRecentSkillWhenMovedUp = false;
  });
  return absentWhenMovedUpStudentCountByStudentId;
}

function hasPassedCWI(historyItem) {
  const { medianScore, totalStudentsAssessed, targetScores, studentScores } = historyItem.assessmentResultMeasures[0];
  const numberOfEnrolledStudents = historyItem.enrolledStudentIds.length;
  return hasGroupPassedClasswideIntervention({
    medianScore,
    totalStudentsAssessed,
    targetScores,
    studentScores,
    numberOfEnrolledStudents
  });
}

function processClasswideInterventionResults({
  isMostRecentPassingHistoryItem,
  updatedMissingScoresRecommendations,
  historyItem
}) {
  const {
    absentOnSameSkillInFrustrationalRangeStudentCountByStudentId,
    absentWhenMovedUpStudentCountByStudentId,
    absentStudentIdsNotMeetingTarget = [],
    twoAnySkillsAbsentWhenMovedUpStudentIds = []
  } = updatedMissingScoresRecommendations;
  const studentIdsWithScores = historyItem.assessmentResultMeasures[0].studentResults.map(sr => sr.studentId);
  const absentStudentIds = difference(historyItem.enrolledStudentIds, studentIdsWithScores);
  if (isMostRecentPassingHistoryItem) {
    Object.entries(absentOnSameSkillInFrustrationalRangeStudentCountByStudentId || {}).forEach(
      ([studentId, { anyScoreAtOrAbove }]) => {
        if (
          absentWhenMovedUpStudentCountByStudentId[studentId].absentCountSinceScoredAtOrAbove >= 2 &&
          absentWhenMovedUpStudentCountByStudentId[studentId].wasAbsentOnMostRecentSkillWhenMovedUp &&
          !anyScoreAtOrAbove
        ) {
          // #3
          absentStudentIdsNotMeetingTarget.push(studentId);
        }
      }
    );
  }

  absentStudentIds.forEach(studentId => {
    absentWhenMovedUpStudentCountByStudentId[studentId].totalAbsentCount += 1;
    absentWhenMovedUpStudentCountByStudentId[studentId].absentCountSinceScoredAtOrAbove += 1;
    absentWhenMovedUpStudentCountByStudentId[studentId].wasAbsentOnMostRecentSkillWhenMovedUp = true;
    if (absentWhenMovedUpStudentCountByStudentId[studentId].absentCountSinceScoredAtOrAbove >= 2) {
      if (!twoAnySkillsAbsentWhenMovedUpStudentIds.includes(studentId)) {
        // #2
        twoAnySkillsAbsentWhenMovedUpStudentIds.push(studentId);
      }
    }
  });

  return {
    absentOnSameSkillInFrustrationalRangeStudentCountByStudentId,
    absentWhenMovedUpStudentCountByStudentId,
    absentStudentIdsNotMeetingTarget,
    twoAnySkillsAbsentWhenMovedUpStudentIds
  };
}

function pullStudentsMeetingRemovalCriteria(studentResults, updatedMissingScoresRecommendations) {
  const {
    twoAnySkillsAbsentWhenMovedUpStudentIds = [],
    absentWhenMovedUpStudentCountByStudentId
  } = updatedMissingScoresRecommendations;
  let { absentStudentIdsNotMeetingTarget } = updatedMissingScoresRecommendations;
  if (twoAnySkillsAbsentWhenMovedUpStudentIds.length) {
    twoAnySkillsAbsentWhenMovedUpStudentIds.forEach(studentId => {
      const studentOutcome = studentResults.find(s => s.studentId === studentId);
      if (studentOutcome && studentOutcome.individualRuleOutcome !== "below") {
        // NOTE(fmazur) - rule 4 - student removal
        absentStudentIdsNotMeetingTarget = absentStudentIdsNotMeetingTarget.filter(sId => sId !== studentId);
        absentWhenMovedUpStudentCountByStudentId[studentId].absentCountSinceScoredAtOrAbove = 0;
      }
    });
  } else {
    // NOTE(fmazur) - Rule 2 needs to be met first
    absentStudentIdsNotMeetingTarget = [];
  }
  return {
    ...updatedMissingScoresRecommendations,
    twoAnySkillsAbsentWhenMovedUpStudentIds,
    absentWhenMovedUpStudentCountByStudentId,
    absentStudentIdsNotMeetingTarget
  };
}

/*
 Missing Scores Individual Intervention Recommendations:
 1) Four weeks have passed since the first progress monitoring scores were saved for a classwide intervention.
 2) The student is marked as absent (no score entered) on two separate occasions at the time the class moves up to a new skill
    in the classwide sequence (the two times don't need to be consecutive).
 3) Student would meet this rule criteria after rule #2 is met and either happens:
    - The student has not previously achieved a progress monitoring score at or above the instructional target on the current skill(last completed)
    for which they were marked absent
    - If student was absent for the whole skill.
 4) If the student is recommended for missing scores but then on a subsequent skill scores at or above
    the instructional target (at any time, not just when the class moves up) they are then removed from
    the list of students recommended for individual intervention. If criteria 2 and 3 are met again after this
    time the student can be recommended again. Prioritization would continue to count all weeks with missing scores.

 Student recommended this way (missing scores) are prioritized after student recommended for individual intervention
 via the 4-week rule (SPRIN-2171).
 Students who have more missing scores on the weeks the class moved up are prioritized higher than those with fewer.
 After missing weeks these students can be prioritized in alphabetical order.

 If they are recommended via the 4-week rule and missing scores, 4 week rule takes priority, so they would not be removed
 if a subsequent score is in the instructional range.
*/
export function getMissingScoresRecommendations({
  historyItem,
  missingScoresRecommendationsResult,
  mostRecentPassingHistoryItem,
  hasSkillChanged
}) {
  let updatedMissingScoresRecommendations = { ...missingScoresRecommendationsResult } || {};
  const isMostRecentPassingHistoryItem =
    mostRecentPassingHistoryItem.assessmentResultId === historyItem.assessmentResultId;
  const {
    enrolledStudentIds,
    assessmentResultMeasures: [{ studentResults }]
  } = historyItem;
  const hasPassedClasswideIntervention = hasPassedCWI(historyItem);

  if (isMostRecentPassingHistoryItem) {
    updatedMissingScoresRecommendations.absentOnSameSkillInFrustrationalRangeStudentCountByStudentId = updateAbsentStudentStatusBasedOnOutcome(
      historyItem,
      updatedMissingScoresRecommendations
    );
  }

  updatedMissingScoresRecommendations.absentWhenMovedUpStudentCountByStudentId = initializeAbsentWhenMovedUpStudentCount(
    enrolledStudentIds,
    updatedMissingScoresRecommendations
  );
  if (hasSkillChanged) {
    updatedMissingScoresRecommendations.absentWhenMovedUpStudentCountByStudentId = updateStudentsAbsentWhenMovedUp(
      enrolledStudentIds,
      updatedMissingScoresRecommendations
    );
  }

  if (hasPassedClasswideIntervention) {
    updatedMissingScoresRecommendations = processClasswideInterventionResults({
      isMostRecentPassingHistoryItem,
      updatedMissingScoresRecommendations,
      historyItem
    });
  }

  updatedMissingScoresRecommendations = pullStudentsMeetingRemovalCriteria(
    studentResults,
    updatedMissingScoresRecommendations
  );

  return updatedMissingScoresRecommendations;
}

export function have4WeeksPassedSinceDate(sinceDate, toDate = new Date()) {
  const fourWeeksAfterSinceDate = moment(sinceDate).add(4, "weeks");
  return moment(toDate).isSameOrAfter(fourWeeksAfterSinceDate);
}

export function getIdsOfStudentsInFrustrationalRange(studentResults = []) {
  return studentResults
    .filter(({ status, individualRuleOutcome }) => status === "COMPLETE" && individualRuleOutcome === "below")
    .map(({ studentId }) => studentId);
}

export function getIdsOfStudentsEligibleForIndividualIntervention({
  classwideInterventionMeasure,
  idsOfStudentsInFrustrationalRangeInPast = []
}) {
  const { studentResults } = classwideInterventionMeasure;
  const assessedStudentIds = studentResults.map(({ studentId }) => studentId);
  const idsOfStudentsInFrustrationalRange = getIdsOfStudentsInFrustrationalRange(studentResults);

  const idsOfStudentsInFrustrationalRangeInPastWithoutCurrentScore = idsOfStudentsInFrustrationalRangeInPast.filter(
    studentId => !assessedStudentIds.includes(studentId)
  );

  return [
    ...new Set([...idsOfStudentsInFrustrationalRangeInPastWithoutCurrentScore, ...idsOfStudentsInFrustrationalRange])
  ];
}

export async function assignToStudentsOrGroup({
  assessmentResult,
  currentClasswideSkill,
  individualSkills: skills,
  previousAssessmentResult,
  isAdditional = false,
  hasCompletedCWI = false
}) {
  const individualSkills = skills;
  const studentGroup = await StudentGroups.findOneAsync(assessmentResult.studentGroupId);
  const byDateOnObj = await getTimestampInfo(getMeteorUserId(), assessmentResult.orgid);
  const historyFieldName = getHistoryFieldName(isAdditional);
  // ************ HISTORY ******************
  // only add classwide skill to history if we're moving on to another.
  const modifiedStudentGroup = potentialUpdateStudentGroupWithClasswideSkillAndHistoryChanges(
    studentGroup,
    assessmentResult,
    previousAssessmentResult,
    byDateOnObj,
    historyFieldName
  );
  const modifiedStudentGroupUpdateSet = {
    [historyFieldName]: modifiedStudentGroup[historyFieldName]
  };
  if (!isAdditional && hasCompletedCWI) {
    modifiedStudentGroupUpdateSet.hasCompletedCWI = true;
  }
  // ******************* END HISTORY ***********************
  // student group assignments and updates
  await updateStudentGroupCurrentSkillAndHistory(
    assessmentResult,
    modifiedStudentGroup,
    modifiedStudentGroupUpdateSet,
    previousAssessmentResult,
    currentClasswideSkill
  );

  // Student assignments and updates
  await updateStudentCurrentSkillAndHistory(assessmentResult, previousAssessmentResult, byDateOnObj, individualSkills);
  return assessmentResult;
}

function potentialUpdateStudentGroupWithClasswideSkillAndHistoryChanges(
  studentGroup,
  assessmentResult,
  previousAssessmentResult,
  byDateOnObj,
  historyFieldName = "history"
) {
  const localStudentGroup = { ...studentGroup };
  if (!previousAssessmentResult) {
    if (!localStudentGroup[historyFieldName]) {
      localStudentGroup[historyFieldName] = [];
    }
  } else if (assessmentResult.type === "classwide" && previousAssessmentResult.type === "classwide") {
    if (!localStudentGroup[historyFieldName]) {
      localStudentGroup[historyFieldName] = [];
    }
    // update extra stats on the skill before storing it
    localStudentGroup.currentClasswideSkill.whenEnded = byDateOnObj;
    if (previousAssessmentResult) {
      localStudentGroup.currentClasswideSkill.assessmentResultMeasures = previousAssessmentResult.measures;
      localStudentGroup.currentClasswideSkill.type = previousAssessmentResult.type;
      localStudentGroup.currentClasswideSkill.benchmarkPeriodId = previousAssessmentResult.benchmarkPeriodId;
      localStudentGroup.currentClasswideSkill.enrolledStudentIds = getEnrolledStudentIds(previousAssessmentResult);
    }
    localStudentGroup[historyFieldName].unshift(localStudentGroup.currentClasswideSkill);
  } else if (previousAssessmentResult && previousAssessmentResult.type === "benchmark") {
    // no "current" skill was being worked on, so just store the indicating info for screening
    if (!localStudentGroup[historyFieldName]) {
      localStudentGroup[historyFieldName] = [];
    }
    // check to see if we already inserted the benchmark history for this AR
    if (
      !localStudentGroup[historyFieldName].some(
        h => h.type === "benchmark" && h.assessmentResultId === previousAssessmentResult._id
      )
    ) {
      localStudentGroup[historyFieldName].unshift({
        type: previousAssessmentResult.type,
        benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId,
        assessmentResultId: previousAssessmentResult._id,
        whenEnded: byDateOnObj,
        whenStarted: previousAssessmentResult.created,
        assessmentResultMeasures: previousAssessmentResult.measures,
        enrolledStudentIds: getEnrolledStudentIds(previousAssessmentResult)
      });
    }
  }
  return localStudentGroup;
}

async function updateStudentGroupCurrentSkillAndHistory(
  assessmentResult,
  modifiedStudentGroup,
  modifiedStudentGroupUpdateSet,
  previousAssessmentResult,
  currentClasswideSkill
) {
  const lastModified = getTimestampInfo(
    assessmentResult?.created?.by,
    assessmentResult?.orgid,
    "updateStudentGroupCurrentSkillAndHistory"
  );
  if (["benchmark", "classwide"].includes(assessmentResult.type)) {
    const localUpdateSet = { ...modifiedStudentGroupUpdateSet };
    localUpdateSet.currentAssessmentResultIds = (modifiedStudentGroup.currentAssessmentResultIds || []).filter(
      arid => !previousAssessmentResult || arid !== previousAssessmentResult._id
    );
    localUpdateSet.currentAssessmentResultIds.push(assessmentResult._id);
    // not to be confused with modifiedStudentGroup.currentClasswideskill that was used for history
    if (currentClasswideSkill) {
      extend(localUpdateSet, { currentClasswideSkill });
    }
    await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
      $set: {
        ...localUpdateSet,
        lastModified
      }
    });
  } else {
    // individual
    let newAssessmentResultIds = [assessmentResult._id];
    if (modifiedStudentGroup.currentAssessmentResultIds && modifiedStudentGroup.currentAssessmentResultIds.length) {
      const currentAssessmentResults = await AssessmentResults.find({
        _id: {
          $in: modifiedStudentGroup.currentAssessmentResultIds
        }
      }).fetchAsync();
      if (!(currentAssessmentResults && currentAssessmentResults.length)) {
        throw new Meteor.Error("403", "currentAssessmentResults was null or empty");
      }
      // is the current student already in there?
      newAssessmentResultIds = currentAssessmentResults
        .filter(ar => ar.studentId !== assessmentResult.studentId)
        .map(ar => ar._id);
      newAssessmentResultIds.push(assessmentResult._id);
    }
    await StudentGroups.updateAsync(assessmentResult.studentGroupId, {
      $set: {
        currentAssessmentResultIds: newAssessmentResultIds,
        lastModified
      }
    });
  }
}

async function updateStudentCurrentSkillAndHistory(
  assessmentResult,
  previousAssessmentResult,
  byDateOnObj,
  individualSkills
) {
  const { studentId, type } = assessmentResult;
  if (type !== "individual" || !studentId) {
    return;
  }

  const student = (await Students.findOneAsync(studentId)) || {};
  const history = student.history || [];
  const currentSkill = student.currentSkill || {};

  if (previousAssessmentResult.type === "individual" && Object.keys(currentSkill).length) {
    history.unshift({
      ...previousAssessmentResult.individualSkills,
      whenStarted: previousAssessmentResult.created || previousAssessmentResult.whenStarted,
      benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId,
      message: student.currentSkill.message,
      assessmentResultMeasures: previousAssessmentResult.measures,
      type: previousAssessmentResult.type,
      whenEnded: byDateOnObj
    });
  }

  const message = getIndividualResultsMessage({
    history,
    currentSkill: { ...individualSkills, benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId }
  });
  const newCurrentSkill = individualSkills
    ? {
        ...individualSkills,
        interventions: individualSkills.interventions || [],
        whenStarted: byDateOnObj,
        benchmarkPeriodId: previousAssessmentResult.benchmarkPeriodId,
        message
      }
    : {};
  const update = {
    $set: { history, lastModified: { ...byDateOnObj, context: "updateStudentCurrentSkillAndHistory" } }
  };

  if (Object.keys(newCurrentSkill).length) {
    update.$set.currentSkill = newCurrentSkill;
  } else {
    update.$unset = { currentSkill: 1 };
  }

  await Students.updateAsync(studentId, update);
}

export async function insertAssessmentsAndScores({ assessmentResultId, assessmentIds, scores, individualSkills }) {
  check(assessmentResultId, String);
  check(assessmentIds, Array);
  check(scores, Array);
  const lastModified = getTimestampInfo(getMeteorUserId(), null, "insertAssessmentsAndScores");
  const arUpdate = {
    scores,
    assessmentIds,
    lastModified
  };
  if (individualSkills) {
    arUpdate.individualSkills = individualSkills;
  }
  await AssessmentResults.updateAsync(assessmentResultId, { $set: arUpdate });
}

export async function getIdsOfStudentsWithCompletedIndividualInterventionForMostRecentScreening({
  studentIds,
  schoolYear
}) {
  const students = await Students.find(
    {
      _id: { $in: studentIds },
      "currentSkill.assessmentId": { $exists: false },
      "currentSkill.benchmarkPeriodId": { $exists: true }
    },
    { fields: { currentSkill: 1 } }
  ).fetchAsync();
  const studentIdsWithCompletedInterventions = students.map(s => s._id);
  const studentsWithCompletedInterventionsByStudentId = keyBy(students, "_id");

  const studentGroupEnrollments = await StudentGroupEnrollments.find(
    {
      isActive: true,
      schoolYear,
      studentId: { $in: studentIdsWithCompletedInterventions }
    },
    { fields: { studentGroupId: 1, studentId: 1 } }
  ).fetchAsync();
  const benchmarkPeriodOrder = ["8S52Gz5o85hRkECgq", "nEsbWokBWutTZFkTh", "cjCMnZKARBJmG8suT"];
  const completedBenchmarkPeriodIdsByGroupId = (
    await AssessmentResults.find({
      studentGroupId: { $in: studentGroupEnrollments.map(sge => sge.studentGroupId) },
      schoolYear,
      status: "COMPLETED",
      type: "benchmark"
    }).fetchAsync()
  )
    .sort((a, b) => {
      const indexA = benchmarkPeriodOrder.indexOf(a.benchmarkPeriodId);
      const indexB = benchmarkPeriodOrder.indexOf(b.benchmarkPeriodId);
      return indexA - indexB;
    })
    .reduce((a, c) => {
      if (!a[c.studentGroupId]) {
        a[c.studentGroupId] = [];
      }
      if (!a[c.studentGroupId].includes(c.benchmarkPeriodId)) {
        a[c.studentGroupId].push(c.benchmarkPeriodId);
      }
      return a;
    }, {});

  const studentIdsToBeRemoved = [];
  studentGroupEnrollments.forEach(({ studentId, studentGroupId }) => {
    if (
      completedBenchmarkPeriodIdsByGroupId[studentGroupId]?.[
        completedBenchmarkPeriodIdsByGroupId[studentGroupId].length - 1
      ] === studentsWithCompletedInterventionsByStudentId[studentId].currentSkill.benchmarkPeriodId
    ) {
      studentIdsToBeRemoved.push(studentId);
    }
  });
  return studentIdsToBeRemoved;
}

export async function getIdsOfHSStudentsWithCompletedAllInterventions(studentIds) {
  return Students.find(
    {
      _id: { $in: studentIds },
      grade: "HS",
      currentSkill: { $exists: true },
      "currentSkill.assessmentId": { $exists: false }
    },
    { fields: { _id: 1 } }
  ).mapAsync(s => s._id);
}
