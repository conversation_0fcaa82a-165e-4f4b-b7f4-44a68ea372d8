import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Random } from "meteor/random";
import { get, intersection } from "lodash";
import moment from "moment";

import * as auth from "../authorization/server/methods";
import { StudentGroups } from "./studentGroups";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import {
  getCurrentSchoolYear,
  getHistoryFieldName,
  getMedianNumber,
  getMeteorUser,
  getMeteorUserId,
  hasGroupPassedClasswideIntervention,
  MAX_SKILLS_FROM_NEXT_GRADE
} from "../utilities/utilities";
import BenchmarkPeriodHelpers from "../benchmarkPeriods/methods";
import { calculateClasswideStatsForMultipleGroups } from "../utilities/server/interventionStats";
import { Grades } from "../grades/grades";
import { isHighSchoolGrade } from "../../ui/utilities";
import { updateUsersSiteAccess } from "../utilities/saveGroupData";
import {
  getIndividualInterventionQueueBasedOnFourWeekRule,
  setCurrentAssessmentResultForStudentGroup
} from "../assessmentResults/methods";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getIndividualRuleOutcome, getPercent } from "../assessmentResults/utilities";
import { getCurrentDate } from "../helpers/getCurrentDate";
import { Rules } from "../rules/rules";
import { getNextSkill } from "../rules/server/methods";
import { Assessments } from "../assessments/assessments";
import { getScoreTargets } from "../assessments/methods";

Meteor.methods({
  async "StudentGroups:getGroups"(orgId, siteId) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    check(orgId, Match.Maybe(String));
    check(siteId, Match.Maybe(String));

    if (!orgId && !siteId) {
      return [];
    }

    // TODO(fmazur) - check user access

    const query = {};
    if (siteId) {
      query.siteId = siteId;
    }
    if (orgId) {
      query.orgid = orgId;
    }

    return StudentGroups.find(query, {
      fields: { created: 0, lastModified: 0, type: 0, rosterImportId: 0 }
    }).fetchAsync();
  },
  async "StudentGroups:updateCalculateAsOfDate"({
    orgid,
    siteId,
    entityId,
    calculateStatsAsOfDate,
    benchmarkPeriodId
  }) {
    check(orgid, String);
    check(siteId, String);
    check(entityId, String);
    check(calculateStatsAsOfDate, Date);
    check(benchmarkPeriodId, String);

    if (
      !this.userId &&
      !(await auth.hasAccess(["admin", "universalCoach"], {
        userId: this.userId,
        siteId
      }))
    ) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const summerBreakStartDate = moment(
      `15.05.${await getCurrentSchoolYear(await getMeteorUser(), orgid)} 12:00:00`,
      "DD.MM.YYYY hh:mm:ss"
    ).toDate();
    const dateToSet =
      calculateStatsAsOfDate.valueOf() < summerBreakStartDate.valueOf() ? calculateStatsAsOfDate : summerBreakStartDate;
    const lastModified = await getTimestampInfo(this?.userId, orgid, "StudentGroups:updateCalculateAsOfDate");
    await StudentGroups.updateAsync(entityId, {
      $set: { classwideStatsAsOfDate: dateToSet, lastModified }
    });
  },
  async "StudentGroups:getAveragePercentScreenedPerOrg"(orgid, schoolYear) {
    check(orgid, String);
    check(schoolYear, Number);
    if (
      !this.userId &&
      !(await auth.hasAccess(["support", "universalCoach"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to get benchmark organization statistics!");
    } else {
      return getAveragePercentScreenedPerOrg(orgid, schoolYear);
    }
  },
  async "StudentGroups:getAverageStatsPerOrg"(orgid, schoolYear) {
    check(orgid, String);
    check(schoolYear, Number);
    if (
      !this.userId &&
      !(await auth.hasAccess(["support", "universalCoach"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to get progress monitoring organization statistics!");
    }
    return getAverageStatsPerOrg(orgid, schoolYear);
  },
  async "StudentGroups:getActiveStudentGroupsCountInSites"(orgid) {
    check(orgid, String);
    if (
      await auth.hasAccess(["support", "universalCoach", "superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      })
    ) {
      return getActiveStudentGroupsCountInSites(orgid);
    }
    throw new Meteor.Error(403, "You are not authorized to get Student Groups count!");
  },
  async "StudentGroups:getGroupsOwnedByUsers"(userIds, orgid, siteId) {
    check(userIds, Array);
    check(orgid, String);
    check(siteId, String);
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to get student group owners.");
    }
    return getGroupsOwnedByUsers(userIds, siteId);
  },
  async "StudentGroups:updateGroupsData"({ updatedGroups, selectedUserIds, orgid, siteId }) {
    check(updatedGroups, [Match.ObjectIncluding({ _id: String, ownerIds: [String], name: String })]);
    check(selectedUserIds, Array);
    check(orgid, String);
    check(siteId, String);
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to update group data.");
    }
    return updateGroupsData({ updatedGroups, selectedUserIds, orgid, siteId });
  },
  async "StudentGroups:updateStudentScoreInClasswideIntervention"({
    studentGroupId,
    assessmentId,
    studentId,
    studentScore,
    masteryTarget,
    isLastSkill = false,
    orgid
  }) {
    check(studentGroupId, String);
    check(assessmentId, String);
    check(studentId, String);
    check(studentScore, String);
    check(masteryTarget, Number);
    check(isLastSkill, Boolean);
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to update student score.");
    }
    return updateStudentScoreInClasswideIntervention({
      studentGroupId,
      assessmentId,
      studentId,
      studentScore,
      masteryTarget,
      isLastSkill
    });
  },
  async "StudentGroups:getGroupsInOrg"(orgid) {
    check(orgid, String);
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "You are not authorized to fetch group data.");
    }
    const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);

    return StudentGroups.find(
      { orgid, schoolYear },
      { fields: { siteId: 1, name: 1, sectionId: 1, isActive: 1 } }
    ).fetchAsync();
  },
  async "StudentGroups:useIncrementalRehearsalForDay"({ studentGroupId, measureNumber, day }) {
    check(studentGroupId, String);
    check(measureNumber, String);
    check(day, Number);
    const studentGroup = await StudentGroups.findOneAsync(
      { _id: studentGroupId },
      { fields: { orgid: 1, siteId: 1, ir: 1 } }
    );
    if (!studentGroup) {
      throw new Meteor.Error(404, "Student group not found");
    }
    const { orgid, siteId } = studentGroup;
    if (
      !(await auth.hasAccess(["teacher", "admin", "universalCoach", "support"], {
        userId: this.userId,
        siteId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use StudentGroups:useIncrementalRehearsalForDay");
    }
    const meteorUser = await getMeteorUser();
    const customDate = get(meteorUser, "profile.customDate");
    const currentDate = await getCurrentDate(customDate, orgid);
    const timestamp = Math.floor(currentDate.getTime() / 1000);
    const lastModified = await getTimestampInfo(this?.userId, studentGroup.orgid, "addAssessmentId");
    await StudentGroups.updateAsync(
      { _id: studentGroupId },
      { $push: { [`ir.${measureNumber}.dates`]: timestamp }, $set: { lastModified } }
    );
    return true;
  }
});

async function moveStudentGroupToExistingOpenAssessment({
  studentGroup,
  openAssessmentResult,
  assessmentResult,
  assessmentId
}) {
  // eslint-disable-next-line no-return-assign
  openAssessmentResult.scores.forEach(score => (score.assessmentId = assessmentId));
  openAssessmentResult.assessmentIds = [assessmentId];
  const isAdditional = !!assessmentResult.ruleResults?.nextSkill?.isAdditional;
  if (isAdditional) {
    openAssessmentResult.isAdditional = isAdditional;
  } else {
    delete openAssessmentResult.isAdditional;
  }
  openAssessmentResult.lastModified = await getTimestampInfo(
    this?.userId || getMeteorUserId(),
    studentGroup?.orgid,
    "moveStudentGroupToExistingOpenAssessment"
  );
  await AssessmentResults.updateAsync({ _id: openAssessmentResult._id }, openAssessmentResult, { upsert: true });
  await setCurrentAssessmentResultForStudentGroup({
    studentGroup,
    assessmentResult,
    assessmentId,
    shouldUpdateHistory: false,
    currentAssessmentResultId: openAssessmentResult._id
  });
}

async function moveStudentGroupToNewOpenAssessment({
  studentGroup,
  assessmentResult,
  assessmentId,
  assessmentResultId
}) {
  const studentGroupId = studentGroup._id;
  const { orgid, schoolYear } = assessmentResult;
  const studentIds = (
    await StudentGroupEnrollments.find(
      { studentGroupId, schoolYear, isActive: true },
      { fields: { studentId: 1 } }
    ).fetchAsync()
  ).map(s => s.studentId);
  const scores = studentIds.map(scoreStudentId => ({
    _id: Random.id(),
    assessmentId,
    status: "STARTED",
    studentId: scoreStudentId,
    orgid: studentGroup.orgid,
    siteId: studentGroup.siteId
  }));
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);
  const newAssessmentResult = {
    benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
    orgid,
    created: bdo,
    schoolYear,
    status: "OPEN",
    studentGroupId: assessmentResult.studentGroupId,
    type: "classwide",
    previousAssessmentResultId: assessmentResultId,
    grade: assessmentResult.grade,
    assessmentIds: assessmentResult.assessmentIds,
    scores
  };
  const newAssessmentResultId = await AssessmentResults.insertAsync(newAssessmentResult);
  await setCurrentAssessmentResultForStudentGroup({
    studentGroup,
    assessmentResult,
    assessmentId,
    shouldUpdateHistory: false,
    currentAssessmentResultId: newAssessmentResultId
  });
  return newAssessmentResultId;
}

function getUpdatedAssessmentResultMeasure({
  assessmentResultMeasure,
  assessmentResult,
  studentId,
  studentScore,
  masteryTarget
}) {
  const cwr = assessmentResult.classwideResults;
  const studentResult = assessmentResultMeasure.studentResults.find(sr => sr.studentId === studentId);
  const foundScoreIndex = assessmentResultMeasure.studentScores.findIndex(score => {
    return score === parseInt(studentResult.score);
  });
  if (foundScoreIndex >= 0) {
    assessmentResultMeasure.studentScores[foundScoreIndex] = parseInt(studentScore);
    assessmentResultMeasure.studentScores.sort();
  }
  // In case of classwide intervention masteryTarget = cutoffTarget
  if (studentScore < masteryTarget && studentResult.score >= masteryTarget) {
    studentResult.meetsTarget = false;
    assessmentResultMeasure.numberMeetingTarget =
      assessmentResultMeasure.numberMeetingTarget > 0 ? assessmentResultMeasure.numberMeetingTarget - 1 : 0;
    if (!cwr.studentIdsNotMeetingTarget.includes(studentId)) {
      cwr.studentIdsNotMeetingTarget.push(studentId);
    }
  } else if (studentScore >= masteryTarget && studentResult.score < masteryTarget) {
    studentResult.meetsTarget = true;
    assessmentResultMeasure.numberMeetingTarget =
      assessmentResultMeasure.numberMeetingTarget < assessmentResultMeasure.totalStudentsAssessed
        ? assessmentResultMeasure.numberMeetingTarget + 1
        : assessmentResultMeasure.totalStudentsAssessed;
    if (cwr.studentIdsNotMeetingTarget.includes(studentId)) {
      cwr.studentIdsNotMeetingTarget.splice(cwr.studentIdsNotMeetingTarget.indexOf(studentId), 1);
    }
  }
  studentResult.score = studentScore;
  assessmentResultMeasure.percentMeetingTarget = Math.round(
    (assessmentResultMeasure.numberMeetingTarget / assessmentResultMeasure.totalStudentsAssessed) * 100
  );
  assessmentResultMeasure.medianScore = getMedianNumber(assessmentResultMeasure.studentScores, "roundUp");
  studentResult.individualRuleOutcome = getIndividualRuleOutcome(studentScore, assessmentResultMeasure.targetScores);

  cwr.percentMeetingTarget = assessmentResultMeasure.percentMeetingTarget;
  cwr.totalStudentsMeetingAllTargets = assessmentResultMeasure.numberMeetingTarget;
  cwr.percentAtRisk = 100 - assessmentResultMeasure.percentMeetingTarget;
  // assessmentResult type classwide always contains only one measure
  cwr.totalStudentsAssessedOnAllMeasures = assessmentResultMeasure.totalStudentsAssessed;

  assessmentResult.measures = [assessmentResultMeasure];
  return assessmentResultMeasure;
}

export async function updateStudentScoreInClasswideIntervention({
  studentGroupId,
  assessmentId,
  studentId,
  studentScore,
  masteryTarget,
  isLastSkill = false
}) {
  /* eslint-disable no-param-reassign */
  const assessmentResult = await AssessmentResults.findOneAsync(
    { assessmentIds: assessmentId, type: "classwide", status: "COMPLETED", studentGroupId },
    { sort: { "created.on": -1 } }
  );
  const lastModified = await getTimestampInfo(
    getMeteorUserId(),
    assessmentResult?.orgid,
    "updateStudentScoreInClasswideIntervention"
  );
  const assessmentResultId = assessmentResult._id;
  assessmentResult.scores = assessmentResult.scores.map(score => {
    if (score.studentId !== studentId) {
      return score;
    }
    return { ...score, value: studentScore, status: "COMPLETE" };
  });

  const studentGroup = await StudentGroups.findOneAsync({
    _id: studentGroupId
  });
  let armIndex = 0;
  const additionalHistory = studentGroup.additionalHistory || [];
  const studentGroupHistory = [...additionalHistory, ...(studentGroup.history || [])];
  const historyItem =
    studentGroupHistory.find((sgh, i) => {
      armIndex = i;
      return sgh.assessmentId === assessmentId && sgh.assessmentResultId === assessmentResultId;
    }) || [];
  const { assessmentResultMeasures, enrolledStudentIds } = historyItem;

  if (!assessmentResultMeasures.length) {
    throw new Meteor.Error(
      "updateStudentScoreInClasswideIntervention",
      `Assessment result measure for an assessment result with id: ${assessmentResultId} not found`
    );
  }

  const isAdditionalSkill = additionalHistory.find(
    additionalHistoryItem => additionalHistoryItem.assessmentResultId === historyItem.assessmentResultId
  );

  const arm = assessmentResultMeasures[0];
  const previousMedianScore = arm.medianScore;
  const previousPercentageAtOrAbove = getPercent(
    arm.studentScores.filter(val => val >= arm.targetScores[0]).length,
    arm.totalStudentsAssessed
  );
  const assessmentResultMeasure = getUpdatedAssessmentResultMeasure({
    assessmentResultMeasure: arm,
    assessmentResult,
    studentId,
    studentScore,
    masteryTarget
  });

  const percentageAtOrAbove = Math.round(
    (arm.studentScores.filter(val => val >= arm.targetScores[0]).length / arm.totalStudentsAssessed) * 100
  );

  const percentageAtOrAboveChanged = previousPercentageAtOrAbove !== percentageAtOrAbove;
  const medianScoreThresholdChanged = previousMedianScore !== assessmentResultMeasure.medianScore;

  const potentialChange = percentageAtOrAboveChanged || medianScoreThresholdChanged;
  const openAssessmentResult = await AssessmentResults.findOneAsync({
    status: "OPEN",
    type: "classwide",
    studentGroupId
  });

  const { medianScore, totalStudentsAssessed, targetScores, studentScores } = assessmentResultMeasure;

  if (
    potentialChange &&
    !hasGroupPassedClasswideIntervention({
      medianScore,
      totalStudentsAssessed,
      targetScores,
      studentScores,
      numberOfEnrolledStudents: enrolledStudentIds.length
    })
  ) {
    assessmentResult.ruleResults = {
      passed: false,
      nextSkill: {
        assessmentId: arm.assessmentId,
        assessmentName: arm.assessmentName,
        interventions: [],
        targets: arm.targetScores,
        ...(assessmentResult.isAdditional ? { isAdditional: true } : {})
      }
    };
    if (openAssessmentResult) {
      await moveStudentGroupToExistingOpenAssessment({
        studentGroup,
        openAssessmentResult,
        assessmentResult,
        assessmentId
      });
      assessmentResult.nextAssessmentResultId = openAssessmentResult._id;
    } else if (!studentGroup.currentAssessmentResultIds.length) {
      const nextAssessmentResultId = await moveStudentGroupToNewOpenAssessment({
        studentGroup,
        assessmentResult,
        assessmentId,
        assessmentResultId
      });
      assessmentResult.nextAssessmentResultId = nextAssessmentResultId;
    }
  } else if (
    potentialChange &&
    openAssessmentResult &&
    hasGroupPassedClasswideIntervention({
      medianScore,
      totalStudentsAssessed,
      targetScores,
      studentScores,
      numberOfEnrolledStudents: enrolledStudentIds.length
    })
  ) {
    if (isLastSkill) {
      await AssessmentResults.removeAsync({ _id: openAssessmentResult._id });
      await StudentGroups.updateAsync(
        { _id: studentGroupId },
        {
          $set: {
            currentClasswideSkill: {
              whenStarted: assessmentResult.lastModified,
              benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
              message: {
                additionalStudentsAddedToInterventionQueue: false,
                messageCode: "5",
                dismissed: false
              }
            },
            lastModified
          },
          $pull: { currentAssessmentResultIds: openAssessmentResult._id }
        }
      );
      delete assessmentResult.nextAssessmentResultId;
      assessmentResult.ruleResults = { passed: true };
    } else {
      const rule = await Rules.findOneAsync({ grade: studentGroup.grade });
      const defaultSkillsLength = rule?.skills.length || 0;
      const additionalRule = studentGroup.grade === "K" ? await Rules.findOneAsync({ grade: "01" }) : null;
      if (additionalRule) {
        rule.skills.push(...additionalRule.skills.slice(0, MAX_SKILLS_FROM_NEXT_GRADE));
      }
      const nextSkill = getNextSkill({
        rule,
        passed: true,
        assessmentId,
        isBenchmark: false,
        defaultSkillsLength
      });
      const nextAssessmentId = nextSkill.assessmentId;
      const nextAssessment = await Assessments.findOneAsync({ _id: nextAssessmentId });
      let nextAssessmentName = "";
      let nextTargets = [];
      if (nextAssessment) {
        nextAssessmentName = nextAssessment.name;
        nextTargets = getScoreTargets({
          assessment: nextAssessment,
          grade: studentGroup.grade,
          benchmarkPeriodId: assessmentResult.benchmarkPeriodId,
          assessmentType: "classwide"
        });
      }
      if (nextSkill.isAdditional) {
        openAssessmentResult.isAdditional = true;
      }
      assessmentResult.ruleResults = {
        passed: true,
        nextSkill: {
          assessmentId: nextAssessmentId,
          assessmentName: nextAssessmentName,
          interventions: [],
          targets: nextTargets,
          ...(nextSkill.isAdditional ? { isAdditional: true } : {})
        }
      };
      await moveStudentGroupToExistingOpenAssessment({
        studentGroup,
        openAssessmentResult,
        assessmentResult,
        assessmentId: nextAssessmentId
      });
      assessmentResult.nextAssessmentResultId = openAssessmentResult._id;
    }
  }

  const historyFieldName = getHistoryFieldName(isAdditionalSkill);

  await StudentGroups.updateAsync(
    {
      _id: studentGroupId,
      [`${historyFieldName}.assessmentId`]: assessmentId,
      [`${historyFieldName}.assessmentResultMeasures.studentResults.studentId`]: studentId
    },
    { $set: { [`${historyFieldName}.${armIndex}.assessmentResultMeasures`]: assessmentResultMeasures, lastModified } }
  );
  await AssessmentResults.updateAsync({ _id: assessmentResultId }, { ...assessmentResult, lastModified });

  await updateIndividualInterventionRecommendationFromManageScoreChanges({
    studentGroupId,
    assessmentResult,
    lastModified
  });
  /* eslint-enable */
}

export async function updateIndividualInterventionRecommendationFromManageScoreChanges({
  studentGroupId,
  assessmentResult,
  lastModified
}) {
  const updatedStudentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
  const recommendations = await getIndividualInterventionQueueBasedOnFourWeekRule({
    studentGroup: updatedStudentGroup,
    assessmentResult: { ...assessmentResult, lastModified },
    timestampInfo: lastModified
  });
  const studentIdsCurrentlyEnrolled = (
    await StudentGroupEnrollments.find({ studentGroupId, isActive: true }, { fields: { studentId: 1 } }).fetchAsync()
  ).map(sge => sge.studentId);
  await StudentGroups.updateAsync(
    { _id: studentGroupId },
    { $set: { individualInterventionQueue: intersection(studentIdsCurrentlyEnrolled, recommendations || []) } }
  );
}

export async function getSiteIdFromStudentGroupWithStudentGroupId(studentGroupId) {
  check(studentGroupId, String);
  const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
  return studentGroup.siteId;
}

export async function getOrgidFromStudentGroupWithStudentGroupId(studentGroupId) {
  check(studentGroupId, String);
  const studentGroup = await StudentGroups.findOneAsync({ _id: studentGroupId });
  return studentGroup.orgid;
}

export async function getAveragePercentScreenedPerOrg(orgid, schoolYear) {
  let percentScreened = "N/A";

  // Newly introduced High School grades don't do screenings (grades above 08)
  const gradesWithScreenings = [];
  (await Grades.find({}, { fields: { _id: 1 } }).fetchAsync())
    // eslint-disable-next-line array-callback-return
    .filter(grade => {
      if (!isHighSchoolGrade(grade._id)) gradesWithScreenings.push(grade._id);
    });

  const currentBMPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const studentGroupsIds = (
    await StudentGroups.find(
      { orgid, isActive: true, schoolYear, grade: { $in: gradesWithScreenings } },
      { fields: { _id: 1 } }
    ).fetchAsync()
  ).map(studentGroup => studentGroup._id);

  if (studentGroupsIds.length > 0) {
    const currentBenchmarkPeriodAssessmentResults = await AssessmentResults.find(
      {
        orgid,
        type: "benchmark",
        status: "COMPLETED",
        schoolYear,
        benchmarkPeriodId: currentBMPeriod._id
      },
      { fields: { _id: 1, studentGroupId: 1 } }
    ).fetchAsync();

    const currentBenchmarkPeriodAssessmentResultsFilteredByGroups = currentBenchmarkPeriodAssessmentResults.filter(
      assessmentResult => studentGroupsIds.includes(assessmentResult.studentGroupId)
    );
    percentScreened = Math.round(
      (currentBenchmarkPeriodAssessmentResultsFilteredByGroups.length / studentGroupsIds.length) * 100
    );
  }

  return percentScreened;
}

export async function getAverageStatsPerOrg(orgid, schoolYear) {
  const studentGroupIds = (
    await StudentGroups.find(
      { orgid, isActive: true, schoolYear, currentClasswideSkill: { $exists: true } },
      { fields: { _id: 1 } }
    ).fetchAsync()
  ).map(sg => sg._id);

  const percentScreened = await getAveragePercentScreenedPerOrg(orgid, schoolYear);
  let averageInterventionConsistency = "N/A";
  let averageWeeksPerSkill = "N/A";

  const { allClasswideResults } = await calculateClasswideStatsForMultipleGroups(studentGroupIds);

  let groupInterventionConsistencyWeightedSum = 0;
  let groupWeeksPerSkillWeightedSum = 0;
  let numberOfStudents = 0;
  allClasswideResults.forEach(stats => {
    groupInterventionConsistencyWeightedSum += stats.interventionConsistency * stats.numberOfStudentsInGroup;
    groupWeeksPerSkillWeightedSum += stats.averageWeeksPerSkill * stats.numberOfStudentsInGroup;
    numberOfStudents += stats.numberOfStudentsInGroup;
  });

  if (numberOfStudents) {
    averageInterventionConsistency = groupInterventionConsistencyWeightedSum / numberOfStudents;
    averageWeeksPerSkill = groupWeeksPerSkillWeightedSum / numberOfStudents;
  }

  return { percentScreened, averageInterventionConsistency, averageWeeksPerSkill };
}

async function getActiveStudentGroupsCountInSites(orgid) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  return StudentGroups.aggregate([
    {
      $match: {
        isActive: true,
        schoolYear,
        orgid
      }
    },
    {
      $group: {
        _id: "$siteId",
        activeGroupsCount: { $sum: 1 }
      }
    }
  ]);
}

async function getGroupsOwnedByUsers(userIds, siteId) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser());
  return StudentGroups.find(
    {
      siteId,
      isActive: true,
      schoolYear,
      $or: [{ ownerIds: { $in: userIds } }, { secondaryTeachers: { $in: userIds } }]
    },
    { fields: { _id: 1, ownerIds: 1, name: 1, grade: 1, sectionId: 1, secondaryTeachers: 1 } }
  ).fetchAsync();
}

export async function updateGroupsData({ updatedGroups, selectedUserIds = [], orgid, siteId }) {
  const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
  const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "updateGroupsData");
  // eslint-disable-next-line no-restricted-syntax
  for await (const group of updatedGroups) {
    const updateQuery = { $set: { name: group.name, ownerIds: group.ownerIds, lastModified } };
    await StudentGroups.updateAsync({ _id: group._id }, updateQuery);
  }
  await StudentGroups.updateAsync(
    { secondaryTeachers: { $in: selectedUserIds }, schoolYear },
    { $pull: { secondaryTeachers: { $in: selectedUserIds } }, $set: { lastModified } }
  );
  const newOwnerIds = updatedGroups.map(group => group.ownerIds[0]);
  await updateUsersSiteAccess({
    userIdsGainingAccess: newOwnerIds,
    userIdsLosingAccess: selectedUserIds,
    orgid,
    siteId
  });
  return true;
}
