import { countBy, flatten, get, groupBy, intersection, keyBy, sortBy, uniq, difference } from "lodash";
import moment from "moment/moment";
import { normalizeGrade } from "../utilities/sortingHelpers/normalizeSortItem";
import { getGrowthResults } from "../assessmentGrowth/utilities";
import { generateHSLColor } from "../../ui/utilities";
import { getGradeLabel, getPercentage } from "../../ui/pages/program-evaluation/helpers";
import { colors } from "../constants";
import { ROLE_IDS } from "../../../tests/cypress/support/common/constants";

export function getFallToSpringGrowthResultsForGrade({
  studentGroupsByGrade,
  assessmentGrowths,
  assessments,
  grade,
  bmPeriods
}) {
  const studentGroupsInGrade = studentGroupsByGrade[grade];
  const groupsWithHistory = studentGroupsInGrade ? studentGroupsInGrade.filter(sg => sg.history) : [];
  const mergedGrowthResults = { winterToSpring: [] };
  const growthComparisonMapForGrade = assessmentGrowths.find(ag => ag.grade === grade);
  // eslint-disable-next-line no-prototype-builtins
  const assessmentComparisonMap = growthComparisonMapForGrade?.hasOwnProperty("programEvaluation")
    ? growthComparisonMapForGrade.programEvaluation
    : growthComparisonMapForGrade;
  if (!assessmentComparisonMap) {
    return null;
  }
  const growthChartResults = getGrowthResults({
    history: flatten(groupsWithHistory.map(sg => sg.history)),
    assessmentComparisonMap,
    assessments,
    bmPeriods
  });
  const { winterToSpring, fallToWinter } = growthChartResults;
  // NOTE(fmazur) - SPRIN-2285 should only use as many section as final classwide in winter to spring
  winterToSpring.forEach((f, index) => {
    const fall = get(fallToWinter[index], "fall");
    mergedGrowthResults.winterToSpring.push({ ...(fall ? { fall } : {}), ...winterToSpring[index] });
  });
  return mergedGrowthResults;
}

export function getGrowthDataBySchoolYearByGrade({
  studentGroupsAggregate,
  studentGroupsBySiteIdBySchoolYear,
  assessmentGrowth,
  assessmentNameById,
  bmPeriods
}) {
  const assessments = Object.entries(assessmentNameById).map(([_id, name]) => ({ _id, name }));
  const fallToSpringGrowthBySchoolYear = {};
  studentGroupsAggregate.forEach(schoolYearSet => {
    fallToSpringGrowthBySchoolYear[schoolYearSet.schoolYear] = {};
    schoolYearSet.sites.forEach(school => {
      const studentGroupsByGrade = groupBy(
        studentGroupsBySiteIdBySchoolYear[schoolYearSet.schoolYear][school._id]?.documents || [],
        "grade"
      );
      const gradesWithGroups = Object.keys(studentGroupsByGrade || {}).sort((a, b) => {
        return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
      });
      gradesWithGroups
        .filter(grade => grade !== "HS")
        .forEach(grade => {
          fallToSpringGrowthBySchoolYear[schoolYearSet.schoolYear][grade] =
            fallToSpringGrowthBySchoolYear[schoolYearSet.schoolYear][grade] ?? [];
          const growth = getFallToSpringGrowthResultsForGrade({
            studentGroupsByGrade,
            assessmentGrowths: assessmentGrowth,
            assessments,
            grade,
            bmPeriods
          }).winterToSpring;
          fallToSpringGrowthBySchoolYear[schoolYearSet.schoolYear][grade].push(growth);
        });
    });
  });
  const finalGrowthDataBySchoolYearByGrade = {};
  Object.entries(fallToSpringGrowthBySchoolYear).forEach(([growthSchoolYear, fallToSpringByGrade]) => {
    finalGrowthDataBySchoolYearByGrade[growthSchoolYear] = {};
    Object.entries(fallToSpringByGrade).forEach(([grade, growthSets]) => {
      const growthSetContainer = [];
      const totalScoreNumerator = [];
      growthSets.forEach((growthSet, index) => {
        growthSet.forEach((periodGrowthSet, subsetIndex) => {
          growthSetContainer[subsetIndex] = Object.entries(periodGrowthSet).reduce(
            (a, [comparsionPeriod, periodData]) => {
              let totalStudentsAssessed = growthSetContainer[subsetIndex]?.[comparsionPeriod].totalStudentsAssessed;
              const currentTotalStudentsAssessed = periodData.totalStudentsAssessed || 0;
              const currentScore = periodData.score || 0;

              if (currentTotalStudentsAssessed || currentTotalStudentsAssessed === 0) {
                totalStudentsAssessed = totalStudentsAssessed
                  ? totalStudentsAssessed + currentTotalStudentsAssessed
                  : currentTotalStudentsAssessed;
              }

              a[comparsionPeriod] = {
                totalStudentsAssessed: totalStudentsAssessed || 0
              };

              if (!totalScoreNumerator[subsetIndex]) {
                totalScoreNumerator[subsetIndex] = {};
              }
              if (!totalScoreNumerator[subsetIndex][comparsionPeriod]) {
                totalScoreNumerator[subsetIndex][comparsionPeriod] = 0;
              }

              totalScoreNumerator[subsetIndex][comparsionPeriod] += currentScore * currentTotalStudentsAssessed;
              if (growthSets.length - 1 === index) {
                a[comparsionPeriod] = {
                  ...periodData,
                  score: totalScoreNumerator[subsetIndex][comparsionPeriod] / totalStudentsAssessed,
                  totalStudentsAssessed: totalStudentsAssessed || 0
                };
              }

              return a;
            },
            {}
          );
        });

        if (growthSets.length - 1 === index) {
          finalGrowthDataBySchoolYearByGrade[growthSchoolYear][grade] = {
            winterToSpring: growthSetContainer
          };
        }
      });
    });
  });
  return finalGrowthDataBySchoolYearByGrade;
}

function calculateAllStudentsMetric({ schoolYear, assessmentScoresUpload, availableGrades }) {
  const {
    firstProficientByGrade: stateAssessmentSpringProficientByGrade,
    secondProficientByGrade: districtAssessmentSpringProficientByGrade
  } = getExternalProficientCount({
    schoolYear,
    firstProficientFieldName: "stateAssessmentProficient",
    secondProficientFieldName: "districtAssessmentSpringProficient",
    assessmentScoresUpload,
    availableGrades
  });

  const stateAllStudentsGraphData = prepareExternalScoresGraphData(stateAssessmentSpringProficientByGrade);
  const districtAllStudentsGraphData = prepareExternalScoresGraphData(districtAssessmentSpringProficientByGrade);

  return { stateAllStudentsGraphData, districtAllStudentsGraphData };
}

function calculateClasswideImplementationData(data = []) {
  const classwideImplementationData = data.reduce(
    (total, { numberOfAssessedStudents, numberOfProficientStudents }) => {
      return {
        numberOfAssessedStudents: total.numberOfAssessedStudents + numberOfAssessedStudents,
        numberOfProficientStudents: total.numberOfProficientStudents + numberOfProficientStudents
      };
    },
    { numberOfAssessedStudents: 0, numberOfProficientStudents: 0 }
  );

  classwideImplementationData.percentage = getPercentage(
    classwideImplementationData.numberOfProficientStudents,
    classwideImplementationData.numberOfAssessedStudents
  );

  return classwideImplementationData;
}

function prepareExternalScoresGraphData(assessmentSpringProficientByGrade) {
  return Object.keys(assessmentSpringProficientByGrade)
    .map(key => {
      const isNA = !assessmentSpringProficientByGrade[key].true && !assessmentSpringProficientByGrade[key].false;
      const num = assessmentSpringProficientByGrade[key].true || 0;
      const dividedBy = (assessmentSpringProficientByGrade[key].false || 0) + num;

      return {
        name: getGradeLabel(key),
        grade: key,
        y: getPercentage(num, dividedBy),
        ...(isNA ? null : { n: dividedBy }),
        isNA
      };
    })
    .filter(s => s)
    .sort((a, b) => {
      return normalizeGrade(a) < normalizeGrade(b) ? -1 : 1;
    });
}

export function getLowAndHighImplementationMetric(
  lowImplementation,
  highImplementation,
  gradesWithStudentGroupsInSite
) {
  const groupedLowImplementation = groupBy(lowImplementation, "grade");
  const groupedHighImplementation = groupBy(highImplementation, "grade");
  const lowImplementationGraphData = [];
  const highImplementationGraphData = [];

  gradesWithStudentGroupsInSite.forEach(grade => {
    if (groupedLowImplementation[grade]) {
      const lowImplementationData = calculateClasswideImplementationData(groupedLowImplementation[grade]);

      lowImplementationGraphData.push({
        name: getGradeLabel(grade),
        grade,
        y: lowImplementationData.percentage,
        n: lowImplementationData.numberOfAssessedStudents
      });
    }
    if (groupedHighImplementation[grade]) {
      const highImplementationData = calculateClasswideImplementationData(groupedHighImplementation[grade]);

      highImplementationGraphData.push({
        name: getGradeLabel(grade),
        grade,
        y: highImplementationData.percentage,
        n: highImplementationData.numberOfAssessedStudents
      });
    }
  });

  return { lowImplementationGraphData, highImplementationGraphData };
}

function getLowAndHighImplementation({
  proficiencyData,
  studentGroups,
  assessmentResultsForSite,
  allClasswideStats,
  studentGroupEnrollmentsByGroupId
}) {
  const lowImplementation = [];
  const highImplementation = [];
  const assessedStudentIds = proficiencyData.map(({ studentId }) => studentId);
  const proficientStudentIds = proficiencyData
    .filter(({ isProficient }) => isProficient)
    .map(({ studentId }) => studentId);

  studentGroups.forEach(group => {
    const hasGroupCWI = !!assessmentResultsForSite.find(
      ar => ar.studentGroupId === group._id && ar.type === "classwide"
    );
    const groupStats = allClasswideStats?.find(cws => cws.studentGroupId === group._id);
    const enrolledStudentIds = (studentGroupEnrollmentsByGroupId[group._id] || [])
      .filter(sge => sge.isActive)
      .map(sge => sge.studentId);
    const numberOfAssessedStudents = intersection(assessedStudentIds, enrolledStudentIds).length;
    if (hasGroupCWI && groupStats && numberOfAssessedStudents) {
      const numberOfProficientStudents = intersection(proficientStudentIds, enrolledStudentIds).length;
      const classwideProgressPercentage =
        (groupStats.numberOfSkillsPracticed / groupStats.numberOfSkillsInClasswideTree) * 100;
      if (classwideProgressPercentage < 75) {
        lowImplementation.push({
          grade: group.grade,
          numberOfAssessedStudents,
          numberOfProficientStudents
        });
      } else {
        highImplementation.push({
          grade: group.grade,
          numberOfAssessedStudents,
          numberOfProficientStudents
        });
      }
    }
  });
  return { lowImplementation, highImplementation };
}

function calculateLowAndHighImplementationGraphData({
  assessmentScoresUploadForSingleSchoolYear,
  studentGroups,
  assessmentResultsForSite,
  allClasswideStats,
  studentGroupEnrollmentsByGroupId,
  gradesWithStudentGroupsInSite
}) {
  const springStateProficiencyData = assessmentScoresUploadForSingleSchoolYear
    .filter(asu => typeof asu.data.stateAssessmentProficient === "boolean")
    .map(({ studentId, data }) => ({ studentId, isProficient: data.stateAssessmentProficient }));

  const springDistrictProficiencyData = assessmentScoresUploadForSingleSchoolYear
    .filter(asu => typeof asu.data.districtAssessmentSpringProficient === "boolean")
    .map(({ studentId, data }) => ({ studentId, isProficient: data.districtAssessmentSpringProficient }));

  const {
    lowImplementation: stateLowImplementation,
    highImplementation: stateHighImplementation
  } = getLowAndHighImplementation({
    proficiencyData: springStateProficiencyData,
    studentGroups,
    assessmentResultsForSite,
    allClasswideStats,
    studentGroupEnrollmentsByGroupId
  });
  const {
    lowImplementation: districtLowImplementation,
    highImplementation: districtHighImplementation
  } = getLowAndHighImplementation({
    proficiencyData: springDistrictProficiencyData,
    studentGroups,
    assessmentResultsForSite,
    allClasswideStats,
    studentGroupEnrollmentsByGroupId
  });

  const {
    lowImplementationGraphData: stateLowImplementationGraphData,
    highImplementationGraphData: stateHighImplementationGraphData
  } = getLowAndHighImplementationMetric(stateLowImplementation, stateHighImplementation, gradesWithStudentGroupsInSite);
  const {
    lowImplementationGraphData: districtLowImplementationGraphData,
    highImplementationGraphData: districtHighImplementationGraphData
  } = getLowAndHighImplementationMetric(
    districtLowImplementation,
    districtHighImplementation,
    gradesWithStudentGroupsInSite
  );

  return {
    stateLowImplementationGraphData,
    districtLowImplementationGraphData,
    stateHighImplementationGraphData,
    districtHighImplementationGraphData
  };
}

function getExternalProficientCount({
  schoolYear,
  firstProficientFieldName,
  secondProficientFieldName,
  assessmentScoresUpload,
  availableGrades
}) {
  const firstProficientByGrade = {};
  const secondProficientByGrade = {};
  availableGrades.forEach(({ _id: grade }) => {
    const assessmentScoresUploadInGrade = assessmentScoresUpload.filter(
      asu => asu.grade === grade && asu.schoolYear === schoolYear
    );
    if (assessmentScoresUploadInGrade.length) {
      firstProficientByGrade[grade] = countBy(
        assessmentScoresUploadInGrade.filter(asug => asug.data[firstProficientFieldName] !== ""),
        asu => asu.data[firstProficientFieldName]
      );
      secondProficientByGrade[grade] = countBy(
        assessmentScoresUploadInGrade.filter(asug => asug.data[secondProficientFieldName] !== ""),
        asu => asu.data[secondProficientFieldName]
      );
    } else {
      firstProficientByGrade[grade] = {};
      secondProficientByGrade[grade] = {};
    }
  });
  return { firstProficientByGrade, secondProficientByGrade };
}

function getGraphAssessmentNames(assessmentScoresUploadForSingleSchoolYear) {
  const stateWithAssessmentName = assessmentScoresUploadForSingleSchoolYear.find(asu => !!asu.data.stateAssessmentName);
  const districtWithAssessmentName = assessmentScoresUploadForSingleSchoolYear.find(
    asu => !!asu.data.districtAssessmentName
  );
  const stateAssessmentName =
    stateWithAssessmentName && stateWithAssessmentName.data ? stateWithAssessmentName.data.stateAssessmentName : "N/A";
  const districtAssessmentName =
    districtWithAssessmentName && districtWithAssessmentName.data
      ? districtWithAssessmentName.data.districtAssessmentName
      : "N/A";
  return { stateAssessmentName, districtAssessmentName };
}

function getFirstNonNAAssessmentName(data, type) {
  return (
    data.find(sd => sd?.[type]?.[`${type}AssessmentName`] && sd[type][`${type}AssessmentName`] !== "N/A")?.[type]?.[
      `${type}AssessmentName`
    ] || "N/A"
  );
}

function getSpringDataContainer(springData) {
  return {
    districtAssessmentName: getFirstNonNAAssessmentName(springData, "district"),
    stateAssessmentName: getFirstNonNAAssessmentName(springData, "state"),
    categories: [],
    grades: {}
  };
}

export function calculateExternalProficiencyDataForSchools(proficiencyDataForEachSchoolByType, schoolNameById) {
  const colorList = [colors.orange, colors.darkBlue, colors.steelBlue30];
  const { springData, fallToSpringData, springByYearData } = proficiencyDataForEachSchoolByType;
  const parsedSpringData = getSpringDataContainer(springData);

  springData.forEach(springSet => {
    const schoolName = schoolNameById[springSet.school];
    parsedSpringData.categories.push(schoolName);

    ["district", "state"].forEach(type => {
      const seriesName = `${type}Series`;
      springSet[type]?.[seriesName]?.forEach(dataItem => {
        const seriesKey = `${type}${dataItem.name.replace(/\s/g, "")}`;
        dataItem.data.forEach(d => {
          if (!parsedSpringData.grades[d.grade]) {
            parsedSpringData.grades[d.grade] = {
              schoolNames: []
            };
          }
          if (!parsedSpringData.grades[d.grade][seriesKey]) {
            parsedSpringData.grades[d.grade][seriesKey] = [];
          }
          parsedSpringData.grades[d.grade].schoolNames.push(schoolName);
          parsedSpringData.grades[d.grade][seriesKey].push({ y: 0, n: 0, ...d, schoolName });
        });
      });
    });
  });

  const parsedFallToSpringData = {
    districtSeriesByGrade: {},
    districtCategoriesByGrade: {}
  };
  fallToSpringData.forEach(fallToSpringSet => {
    const schoolId = fallToSpringSet.school;
    const schoolName = schoolNameById[schoolId];
    if (!parsedFallToSpringData.districtAssessmentName || parsedFallToSpringData.districtAssessmentName === "N/A") {
      parsedFallToSpringData.districtAssessmentName = fallToSpringSet.districtAssessmentName || "N/A";
    }
    fallToSpringSet.districtSeries.forEach(seriesSet => {
      const scope = seriesSet.name; // Fall or Spring
      seriesSet.data.forEach(dataItem => {
        if (!parsedFallToSpringData.districtSeriesByGrade[dataItem.grade]) {
          parsedFallToSpringData.districtSeriesByGrade[dataItem.grade] = [
            { name: "Fall", color: colorList[0], data: [] },
            { name: "Spring", color: colorList[1], data: [] }
          ];
        }
        if (!parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade]) {
          parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade] = [];
        }
        // { [grade]: [{name:fall, color: "", data: [school1,school2]}, { name:spring}] }
        if (scope === "Fall") {
          if (!parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade].includes(schoolName)) {
            parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade].push(schoolName);
          }
          parsedFallToSpringData.districtSeriesByGrade[dataItem.grade][0].data.push({
            ...dataItem,
            schoolName
          });
        } else if (scope === "Spring") {
          if (!parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade].includes(schoolName)) {
            parsedFallToSpringData.districtCategoriesByGrade[dataItem.grade].push(schoolName);
          }
          parsedFallToSpringData.districtSeriesByGrade[dataItem.grade][1].data.push({
            ...dataItem,
            schoolName
          });
        }
      });
    });

    Object.entries(parsedFallToSpringData.districtCategoriesByGrade).forEach(([grade, categoryItems]) => {
      parsedFallToSpringData.districtSeriesByGrade[grade].forEach((seriesLegendSet, legendSetIndex) => {
        const filledDataPoints = [];
        categoryItems.forEach(category => {
          const dataItem = seriesLegendSet.data.find(dataPoint => dataPoint?.schoolName === category);
          if (dataItem) {
            filledDataPoints.push(dataItem);
          } else {
            filledDataPoints.push({});
          }
        });
        parsedFallToSpringData.districtSeriesByGrade[grade][legendSetIndex].data = filledDataPoints;
      });
    });
  });

  const parsedSpringByYearData = {
    districtSeriesByGrade: {},
    districtCategoriesByGrade: {},
    stateSeriesByGrade: {},
    stateCategoriesByGrade: {}
  };

  springByYearData.forEach(springByYearSet => {
    const schoolId = springByYearSet.school;
    const schoolName = schoolNameById[schoolId];

    ["district", "state"].forEach(type => {
      const seriesKey = `${type}Series`;
      const categoryName = `${type}CategoriesByGrade`;
      const seriesByGradeName = `${type}SeriesByGrade`;
      const assessmentKey = `${type}AssessmentName`;
      if (!parsedSpringByYearData[assessmentKey] || parsedSpringByYearData[assessmentKey] === "N/A") {
        parsedSpringByYearData[assessmentKey] = springByYearSet[type][assessmentKey] || "N/A";
      }
      const numberOfSchoolYears = (springByYearSet[type]?.[seriesKey] || []).length;
      springByYearSet[type]?.[seriesKey]?.forEach(yearDataSet => {
        const seriesName = yearDataSet.name; // school years
        yearDataSet.data.forEach(d => {
          if (!parsedSpringByYearData[seriesByGradeName][d.grade]) {
            parsedSpringByYearData[seriesByGradeName][d.grade] = springByYearSet[type]?.[seriesKey].map((el, i) => ({
              ...el,
              color: colorList[numberOfSchoolYears - i - 1],
              data: []
            }));
          }
          const seriesIndex = parsedSpringByYearData[seriesByGradeName][d.grade].findIndex(
            el => el.name === seriesName
          );
          parsedSpringByYearData[seriesByGradeName][d.grade][seriesIndex]?.data?.push({
            y: 0,
            n: 0,
            ...d,
            schoolName
          });

          if (!parsedSpringByYearData[categoryName]) {
            parsedSpringByYearData[categoryName] = {};
          }
          if (!parsedSpringByYearData[categoryName][d.grade]) {
            parsedSpringByYearData[categoryName][d.grade] = [];
          }
          if (!parsedSpringByYearData[categoryName][d.grade].includes(schoolName)) {
            parsedSpringByYearData[categoryName][d.grade].push(schoolName);
          }
        });
      });

      // NOTE(fmazur) - need to parse after initial aggregation as we didn't have complete categories earlier
      Object.entries(parsedSpringByYearData[categoryName]).forEach(([grade, categoryItems]) => {
        parsedSpringByYearData[seriesByGradeName][grade].forEach((seriesLegendSet, legendSetIndex) => {
          const filledDataPoints = [];
          categoryItems.forEach(category => {
            const dataItem = seriesLegendSet.data.find(dataPoint => dataPoint?.schoolName === category);
            if (dataItem) {
              filledDataPoints.push(dataItem);
            } else {
              filledDataPoints.push({});
            }
          });
          parsedSpringByYearData[seriesByGradeName][grade][legendSetIndex].data = filledDataPoints;
        });
      });
    });
  });

  const createSeriesData = type => {
    const series = {};
    const categoriesByGrade = {};
    Object.entries(parsedSpringData.grades)
      .sort(([a], [b]) => (normalizeGrade(a) < normalizeGrade(b) ? -1 : 1))
      .forEach(([grade, data]) => {
        const schoolNames = uniq(data.schoolNames);
        categoriesByGrade[grade] = schoolNames;
        series[grade] = [
          {
            name: "All Students",
            color: colorList[0],
            data: schoolNames.map(schoolName => {
              const dataItem = (data[`${type}AllStudents`] || []).find(d => d?.schoolName === schoolName);
              return dataItem || {};
            })
          },
          {
            name: "Low Implementation",
            color: colorList[1],
            data: schoolNames.map(schoolName => {
              const dataItem = (data[`${type}LowImplementation`] || []).find(d => d?.schoolName === schoolName);
              return dataItem || {};
            })
          },
          {
            name: "High Implementation",
            color: colorList[2],
            data: schoolNames.map(schoolName => {
              const dataItem = (data[`${type}HighImplementation`] || []).find(d => d?.schoolName === schoolName);
              return dataItem || {};
            })
          }
        ];
      });

    return { series, categoriesByGrade };
  };

  const { series: districtSeriesByGrade, categoriesByGrade: districtCategoriesByGrade } = createSeriesData("district");
  const { series: stateSeriesByGrade, categoriesByGrade: stateCategoriesByGrade } = createSeriesData("state");

  return {
    springData: {
      districtAssessmentName: parsedSpringData.districtAssessmentName,
      districtSeriesByGrade,
      districtCategoriesByGrade,
      stateAssessmentName: parsedSpringData.stateAssessmentName,
      stateSeriesByGrade,
      stateCategoriesByGrade
    },
    fallToSpringData: parsedFallToSpringData,
    springByYearData: parsedSpringByYearData
  };
}

export function getNumberOfDigitsForSeries(array) {
  return Math.max(...array.map(dc => dc.data.map(d => d.n || 0)).flat(2)).toString().length;
}

export function getProficientOnExternalMeasureSpringData({
  studentGroups,
  assessmentResultsForSite,
  allClasswideStats,
  studentGroupEnrollmentsByGroupId,
  gradesWithStudentGroupsInSite,
  schoolYear,
  assessmentScoresUpload,
  availableGrades
}) {
  const options = [
    { name: "All Students", color: colors.orange },
    { name: "Low Implementation", color: colors.darkBlue },
    { name: "High Implementation", color: colors.steelBlue30 }
  ];

  const { stateAllStudentsGraphData, districtAllStudentsGraphData } = calculateAllStudentsMetric({
    schoolYear,
    assessmentScoresUpload,
    availableGrades
  });

  const assessmentScoresUploadForSelectedYear = assessmentScoresUpload.filter(asu => asu.schoolYear === schoolYear);
  const { stateAssessmentName, districtAssessmentName } = getGraphAssessmentNames(
    assessmentScoresUploadForSelectedYear
  );

  const {
    stateLowImplementationGraphData,
    districtLowImplementationGraphData,
    stateHighImplementationGraphData,
    districtHighImplementationGraphData
  } = calculateLowAndHighImplementationGraphData({
    assessmentScoresUploadForSingleSchoolYear: assessmentScoresUploadForSelectedYear,
    studentGroups,
    assessmentResultsForSite,
    allClasswideStats,
    studentGroupEnrollmentsByGroupId,
    gradesWithStudentGroupsInSite
  });

  if (!stateAllStudentsGraphData.length && !districtAllStudentsGraphData.length) {
    return null;
  }

  const shouldDisplayStateGraph = assessmentScoresUploadForSelectedYear.filter(
    asu => asu.data.stateAssessmentProficient
  ).length;
  const shouldDisplayDistrictGraph = assessmentScoresUploadForSelectedYear.filter(
    asu => asu.data.districtAssessmentSpringProficient
  ).length;

  const stateSeries = [
    { ...options[0], data: stateAllStudentsGraphData },
    { ...options[1], data: stateLowImplementationGraphData },
    { ...options[2], data: stateHighImplementationGraphData }
  ];

  const districtSeries = [
    { ...options[0], data: districtAllStudentsGraphData },
    { ...options[1], data: districtLowImplementationGraphData },
    { ...options[2], data: districtHighImplementationGraphData }
  ];

  return {
    state: { stateAssessmentName, stateSeries, shouldDisplayStateGraph },
    district: { districtAssessmentName, districtSeries, shouldDisplayDistrictGraph }
  };
}

export function getProficientOnExternalMeasureSpringByYearData({
  schoolYears,
  assessmentScoresUpload,
  availableGrades
}) {
  const predefinedColors = [colors.orange, colors.darkBlue, colors.steelBlue30, colors.violet];
  const options = schoolYears.map((schoolYear, index) => {
    const { stateAllStudentsGraphData, districtAllStudentsGraphData } = calculateAllStudentsMetric({
      schoolYear,
      assessmentScoresUpload,
      availableGrades
    });
    let color;
    if (index < 5) {
      color = predefinedColors[index];
    } else {
      color = generateHSLColor(Math.random() * 50, 100);
    }
    return {
      name: schoolYear,
      color,
      data: { state: stateAllStudentsGraphData, district: districtAllStudentsGraphData }
    };
  });

  const stateGraphData = [];
  const districtGraphData = [];
  options.forEach(option => {
    const { state, district } = option.data;
    if (state?.some(data => data.y)) {
      stateGraphData.push({ name: option.name, color: option.color, data: state });
    }
    if (district?.some(data => data.y)) {
      districtGraphData.push({ name: option.name, color: option.color, data: district });
    }
  });

  const { stateAssessmentName, districtAssessmentName } = getGraphAssessmentNames(assessmentScoresUpload);
  const shouldDisplayDistrict = districtGraphData.length;
  const shouldDisplayState = stateGraphData.length;

  return {
    state: {
      stateSeries: [...stateGraphData.sort((a, b) => b.data.length - a.data.length)],
      stateAssessmentName,
      shouldDisplayState
    },
    district: {
      districtSeries: [...districtGraphData.sort((a, b) => b.data.length - a.data.length)],
      districtAssessmentName,
      shouldDisplayDistrict
    }
  };
}

export function getProficientOnExternalMeasureFallToSpringData({
  assessmentScoresUpload,
  schoolYear,
  availableGrades
}) {
  const options = [
    { name: "Fall", color: colors.orange },
    { name: "Spring", color: colors.darkBlue }
  ];

  const {
    firstProficientByGrade: districtAssessmentFallProficientByGrade,
    secondProficientByGrade: districtAssessmentSpringProficientByGrade
  } = getExternalProficientCount({
    schoolYear,
    firstProficientFieldName: "districtAssessmentFallProficient",
    secondProficientFieldName: "districtAssessmentSpringProficient",
    assessmentScoresUpload,
    availableGrades
  });

  const fallGraphData = prepareExternalScoresGraphData(districtAssessmentFallProficientByGrade);
  const springGraphData = prepareExternalScoresGraphData(districtAssessmentSpringProficientByGrade);

  const { districtAssessmentName } = getGraphAssessmentNames(assessmentScoresUpload);
  const shouldDisplayFallToSpring = fallGraphData.some(data => data.y) && springGraphData.some(data => data.y);
  const districtSeries = [
    { ...options[0], data: fallGraphData },
    { ...options[1], data: springGraphData }
  ];

  return { shouldDisplayFallToSpring, districtSeries, districtAssessmentName };
}

export function getUserRolesByIdBySiteId(usersInOrg = [], schoolYear) {
  return usersInOrg.reduce((a, c) => {
    c.profile.siteAccess.forEach(sa => {
      if (sa.schoolYear === schoolYear) {
        if (!a[sa.siteId]) {
          a[sa.siteId] = {};
        }
        if (!a[sa.siteId][c._id]) {
          a[sa.siteId][c._id] = [];
        }
        if (!a[sa.siteId][c._id].includes(sa.role)) {
          a[sa.siteId][c._id].push(sa.role);
        }
      }
    });
    return a;
  }, {});
}

export function getStudentIdsManuallyRecommendedForIndividualIntervention({
  assessmentResults,
  studentGroupsInSchool,
  userRolesById = {}
}) {
  const normallyRecommendedStudentIds = [];
  const manuallyRecommendedStudentIds = [];
  const sortedAssessmentResults = sortBy(assessmentResults, "created.on");
  const assessmentResultById = keyBy(sortedAssessmentResults, "_id");
  const screeningByBenchmarkPeriodIdByStudentGroupId = sortedAssessmentResults
    .filter(a => a.type === "benchmark")
    .reduce((a, c) => {
      if (!a[c.studentGroupId]) {
        a[c.studentGroupId] = {};
      }
      a[c.studentGroupId][c.benchmarkPeriodId] = c;
      return a;
    }, {});
  const classwideAssessmentResultsByStudentGroupId = groupBy(
    sortedAssessmentResults.filter(ar => ar.type === "classwide"),
    "studentGroupId"
  );
  const assessmentResultsByStudentId = groupBy(
    sortedAssessmentResults.filter(ar => ar.type === "individual"),
    "studentId"
  );

  const userIdsAssignedToGroupByGroupId = studentGroupsInSchool.reduce((a, c) => {
    a[c._id] = [...c.ownerIds, ...(c.secondaryTeachers || [])];
    return a;
  }, {});

  if (!Object.keys(assessmentResultsByStudentId || {}).length) {
    return [];
  }

  Object.entries(assessmentResultsByStudentId).forEach(([studentId, assessmentResultsForStudent]) => {
    const firstAssessmentResultsByEachBenchmarkPeriod = assessmentResultsForStudent.reduce((a, c) => {
      if (!a[c.benchmarkPeriodId]) {
        a[c.benchmarkPeriodId] = c;
      }
      return a;
    }, {});
    Object.entries(firstAssessmentResultsByEachBenchmarkPeriod).forEach(
      ([benchmarkPeriodId, studentFirstDocumentForBenchmarkPeriod]) => {
        const { created, studentGroupId } = studentFirstDocumentForBenchmarkPeriod;
        const userIds = userIdsAssignedToGroupByGroupId[studentGroupId] || [];
        if (!userIds.includes(created?.by)) {
          manuallyRecommendedStudentIds.push(studentId);
          return;
        }
        if ((userRolesById?.[created?.by] || []).includes(ROLE_IDS.teacher)) {
          normallyRecommendedStudentIds.push(studentId);
          return;
        }
        const screening = screeningByBenchmarkPeriodIdByStudentGroupId[studentGroupId]?.[benchmarkPeriodId];
        const didScreeningResultInClasswide =
          assessmentResultById[screening?.nextAssessmentResultId]?.type === "classwide";
        const individualInterventionTimestamp = studentFirstDocumentForBenchmarkPeriod.created.on;
        const classwides = classwideAssessmentResultsByStudentGroupId[studentGroupId];
        if (individualInterventionTimestamp < screening?.lastModified?.on || (!screening && !classwides?.length)) {
          manuallyRecommendedStudentIds.push(studentId);
        } else if (classwides?.length || didScreeningResultInClasswide) {
          const firstClasswideEndedTimestamp = classwides[0]?.lastModified?.on;
          if (!firstClasswideEndedTimestamp) {
            manuallyRecommendedStudentIds.push(studentId);
            return;
          }
          const classwideAfterFourthWeekTimestamp = classwides.find(classwide => {
            const endedTimestamp = classwide?.lastModified?.on;
            return (
              endedTimestamp >
              moment(firstClasswideEndedTimestamp)
                .add(4, "weeks")
                .valueOf()
            );
          })?.lastModified?.on;
          if (individualInterventionTimestamp < classwideAfterFourthWeekTimestamp) {
            manuallyRecommendedStudentIds.push(studentId);
          }
        } else {
          normallyRecommendedStudentIds.push(studentId);
        }
      }
    );
  });
  return difference(uniq(manuallyRecommendedStudentIds), normallyRecommendedStudentIds);
}
