import { Meteor } from "meteor/meteor";
import { check } from "meteor/check";
import { parseBoolean } from "./utilities/utilities";

// TODO(fmazur) - load environment variables to local storage on client startup
Meteor.methods({
  getEnvironmentVariables(keys) {
    check(keys, Array);

    return keys.reduce((envs, envProperty) => {
      const envValue = process.env[envProperty] || "";
      // eslint-disable-next-line no-param-reassign
      envs[envProperty] = ["true", "false"].includes(envValue.toLowerCase()) ? parseBoolean(envValue) : envValue;
      return envs;
    }, {});
  },
  getMeteorPrivateSettings(keys) {
    check(keys, Array);

    return keys.reduce((settings, settingKey) => {
      const settingValue = Meteor.settings[settingKey] || "";
      settings[settingKey] = ["true", "false"].includes(settingValue.toLowerCase())
        ? parseBoolean(settingValue)
        : settingValue;
      return settings;
    }, {});
  }
});
