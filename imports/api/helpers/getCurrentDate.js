import { Organizations } from "../organizations/organizations";

export async function getCurrentDate(date, orgid) {
  const today = new Date();
  if (!date || !orgid) {
    return today;
  }
  const organization = await Organizations.findOneAsync({ _id: orgid }, { fields: { isTestOrg: 1 } });
  if (!organization || !organization.isTestOrg) {
    return today;
  }
  return new Date(`${date}T${today.toISOString().split("T")[1]}`) || today;
}
