FROM ubuntu:22.04

# Import MongoDB public GPG key
# Setup gnu privacy guard for mongo which is required in newer ubuntu & mongo
RUN apt-get update && apt-get install -y wget gnupg
RUN wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | apt-key add -

# deb http://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse
RUN echo "deb http://repo.mongodb.org/apt/ubuntu $(cat /etc/lsb-release | grep DISTRIB_CODENAME | cut -d= -f2)/mongodb-org/6.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Setup locale for mongo 6.0 which depends on tzdata
ENV TZ=America/New_York
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Update apt-get sources AND install MongoDB then remove no longer needed package lists
RUN apt-get update && apt-get install -y mongodb-org netcat && rm -rf /var/lib/apt/lists/*

# Create the MongoDB data directory
RUN mkdir -p /data/db

# Add replica set starting script
ADD ./initializeMongo.sh /
ADD ./startMongo.sh /

# Copy packed db dump
COPY ./meteor.tar /meteor.tar

# Initialize MongoDB
RUN /bin/bash /initializeMongo.sh

# Expose port #27017 from the container to the host
EXPOSE 27017
ENTRYPOINT ["./startMongo.sh"]
