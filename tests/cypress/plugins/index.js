const fs = require("fs");
const path = require("path");

// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)
// workaround for https://github.com/cypress-io/cypress/issues/1872
module.exports = on => {
  on("before:browser:launch", (browser = {}, args = []) => {
    if (browser.name === "chrome") {
      // ^ make sure this is your browser name, you may
      // be using 'canary' or 'chromium' for example, so change it to match!
      args.push("--proxy-bypass-list=<-loopback>");
      return args;
    }
    return args;
  });
};

const deleteFiles = folderPath => {
  let deletedFiles = [];
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach(file => {
      const currentPath = path.join(folderPath, file);
      if (fs.lstatSync(currentPath).isDirectory()) {
        // Recursively delete folder contents if necessary
        const subDeletedFiles = deleteFiles(currentPath);
        deletedFiles = deletedFiles.concat(subDeletedFiles);
        fs.rmdirSync(currentPath);
      } else {
        fs.unlinkSync(currentPath);
        deletedFiles.push(currentPath);
      }
    });
  }
  return deletedFiles;
};

module.exports = on => {
  on("task", {
    failed: require("cypress-failed-log/src/failed")(),
    doesFileExist({ folderPath, fileName, partialMatch = false }) {
      if (partialMatch) {
        const files = fs.readdirSync(folderPath);
        return files.some(file => file.startsWith(fileName));
      }
      const fullPath = path?.join(folderPath, fileName);
      return fs.existsSync(fullPath);
    },
    deleteFilesInFolder({ folderPath }) {
      const deletedFiles = deleteFiles(folderPath);
      return deletedFiles;
    }
  });
};
