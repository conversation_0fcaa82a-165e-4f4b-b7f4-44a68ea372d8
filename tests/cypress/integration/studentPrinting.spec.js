import { PRINT_OPTIONS } from "/imports/api/constants";
import { INTERVENTION_SKILLS_BY_GRADE_BY_INDEX, ORGID, TEST_GROUPS } from "../support/common/constants";
import replaceExternalWindow from "../support/common/printWindowUtil";
import { assertSchoolOverviewHeader, clickVisibleText, waitForDetachedDom } from "../support/common/utils";
import { changeSchoolYear, clickDashboardTab, clickElementByTextWithinSideNav } from "../support/common/navigating";

const printStubOpts = {
  onBeforeLoad: win => {
    cy.stub(win, "print");
  }
};

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");
const PREVIOUS_SCHOOL_YEAR = Cypress.env("PREVIOUS_SCHOOL_YEAR");

/**
 * Groups used: grade4group1
 * Modifies: -
 * Can be rerun without test database
 * Requires student with at least 1 score saved in individual intervention and at least 2 classwide intervention skills completed
 * Required student in the past with student detail
 */
describe("Student page printing:", () => {
  describe("Admin prints the student page for a prior year", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
      const studentName = "Gibson, Miguel";
      assertSchoolOverviewHeader();
      changeSchoolYear(`schoolYear${PREVIOUS_SCHOOL_YEAR}`);
      clickElementByTextWithinSideNav("4th Grade");
      goToStudentPageFor(studentName);
      cy.get('[data-testid="loading-icon"]', { timeout: 10000 }).should("not.exist");
    });
    it("should be able to print the student profile page", () => {
      const printOption = PRINT_OPTIONS.CURRENT_STUDENT_PROFILE;
      selectPrintOptions(printOption);
      clickVisibleText("Print");

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url, schoolYear: PREVIOUS_SCHOOL_YEAR });
          cy.findByText("Gibson, Miguel").should("exist");
          cy.get(".intervention-stat").should("have.length", 2);
          cy.findByText("Classwide Intervention Progress").should("exist");
          cy.findByText("Skill Tree Progress", { timeout: 1000 }).should("exist");
          cy.findByText("Individual Intervention Progress").should("exist");
          cy.findByTestId("individual-intervention-progress-section").within(() => {
            cy.findByTestId("2-Digit Multiplied by 2-Digit with and without Regrouping").should("exist");
            cy.findByTestId("2-Digit Multiply by 2-Digit with Regrouping").should("exist");
          });
          cy.findByTestId("screeningResults").should("exist");
          cy.findByText("Student Activity Log").should("exist");
        },
        { timeout: 25000 }
      );
    });
  });
  describe("Teacher prints the student page", () => {
    const classwideSkillNameByInterventionProgressIndex = INTERVENTION_SKILLS_BY_GRADE_BY_INDEX["04"];
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
      const studentName = "Gibson, Miguel";
      goToRosterFor(TEST_GROUPS.grade4group1.name);
      goToStudentPageFor(studentName);
      cy.get('[data-testid="loading-icon"]', { timeout: 20000 }).should("not.exist");
    });
    it("should be able to print current classwide intervention graph by default", () => {
      const printOption = PRINT_OPTIONS.CURRENT_CLASSWIDE_INTERVENTION;
      clickVisibleText("Print");

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url });
          cy.findAllByText("Gibson, Miguel")
            .first()
            .should("exist");
          cy.get(".intervention-stat").should("have.length", 0);
          cy.findByText("Classwide Intervention Progress").should("exist");
          cy.findByTestId("classwide-intervention-progress-section").within(() => {
            cy.findAllByText("Multiplication 0-12").should("have.length", 1);
          });
          cy.findByText("Skill Tree Progress", { timeout: 1000 }).should("not.exist");
          cy.findByText("Individual Intervention Progress").should("not.exist");
          cy.findByTestId("screeningResults").should("not.exist");
          cy.findByText("Student Activity Log").should("not.exist");
        },
        { timeout: 25000 }
      );
    });
    it("should be able to print all classwide intervention graphs", () => {
      const printOption = PRINT_OPTIONS.ALL_CLASSWIDE_INTERVENTION_GRAPHS;
      selectPrintOptions(printOption);
      clickVisibleText("Print");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url });
          cy.findAllByText("Gibson, Miguel")
            .first()
            .should("exist");
          cy.get(".intervention-stat").should("have.length", 0);
          cy.findByText("Classwide Intervention Progress").should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[0]).should("exist");
          // NOTE(fmazur) - Uncomment when Classwide Intervention is recreated using new Class Rules
          // cy.findByTestId(classwideSkillNameByInterventionProgressIndex[1]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[2]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[3]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[4]).should("exist");
          cy.findByText("Skill Tree Progress", { timeout: 1000 }).should("not.exist");
          cy.findByText("Individual Intervention Progress").should("not.exist");
          cy.findByTestId("screeningResults").should("not.exist");
          cy.findByText("Student Activity Log").should("not.exist");
        },
        { timeout: 25000 }
      );
    });
    it("should be able to print all individual intervention graphs", () => {
      const printOption = PRINT_OPTIONS.ALL_INDIVIDUAL_INTERVENTION_GRAPHS;
      selectPrintOptions(printOption);
      clickVisibleText("Print");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url });
          cy.findAllByText("Gibson, Miguel")
            .first()
            .should("exist");
          cy.get(".intervention-stat").should("have.length", 2);
          cy.findByText("Classwide Intervention Progress").should("not.exist");
          cy.findByText("Individual Intervention Progress").should("exist");
          cy.findByTestId("individual-intervention-progress-section").within(() => {
            cy.findAllByText("2-Digit Multiply by 2-Digit with Regrouping").should("have.length", 1);
            cy.findAllByText("2-Digit Multiplied by 2-Digit with and without Regrouping").should("have.length", 1);
            cy.findByText("Interventions").should("not.exist");
            cy.findByText("Winter Goals", { timeout: 1000 }).should("not.exist");
          });
          cy.findByTestId("screeningResults").should("not.exist");
          cy.findByText("Student Activity Log").should("not.exist");
        },
        { timeout: 25000 }
      );
    });
    it("should be able to print current student profile page", () => {
      const printOption = PRINT_OPTIONS.CURRENT_STUDENT_PROFILE;
      selectPrintOptions(printOption);
      clickVisibleText("Print");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url });
          cy.findAllByText("Gibson, Miguel")
            .first()
            .should("exist");
          cy.get(".intervention-stat").should("have.length", 2);
          cy.findByText("Classwide Intervention Progress").should("exist");
          cy.findByText("Skill Tree Progress", { timeout: 1000 }).should("exist");
          cy.findByText("Individual Intervention Progress").should("exist");
          cy.findByTestId("individual-intervention-progress-section").within(() => {
            cy.findAllByText("2-Digit Multiply by 2-Digit with Regrouping").should("have.length", 2);
            cy.findAllByText("2-Digit Multiplied by 2-Digit with and without Regrouping").should("have.length", 2);
            cy.findByText("Interventions").should("exist");
            cy.findByText("Winter Goals", { timeout: 1000 }).should("exist");
          });
          cy.findByTestId("screeningResults").should("exist");
          cy.findByText("Student Activity Log").should("exist");
        },
        { timeout: 25000 }
      );
    });
    it("should be able to print all student details including all graphs", () => {
      const printOption = PRINT_OPTIONS.ALL_STUDENT_DETAILS;
      selectPrintOptions(printOption);
      clickVisibleText("Print");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ printOption, url });
          cy.findAllByText("Gibson, Miguel")
            .first()
            .should("exist");
          cy.get(".intervention-stat").should("have.length", 2);
          cy.findByText("Classwide Intervention Progress").should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[0]).should("exist");
          // NOTE(fmazur) - Uncomment when Classwide Intervention is recreated using new Class Rules
          // cy.findByTestId(classwideSkillNameByInterventionProgressIndex[1]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[2]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[3]).should("exist");
          cy.findByTestId(classwideSkillNameByInterventionProgressIndex[4]).should("exist");
          cy.findByText("Skill Tree Progress", { timeout: 1000 }).should("not.exist");
          cy.findByText("Individual Intervention Progress").should("exist");
          cy.findByTestId("individual-intervention-progress-section").within(() => {
            cy.findAllByText("2-Digit Multiply by 2-Digit with Regrouping").should("have.length", 1);
            cy.findAllByText("2-Digit Multiplied by 2-Digit with and without Regrouping").should("have.length", 1);
            cy.findByText("Interventions").should("not.exist");
            cy.findByText("Winter Goals", { timeout: 1000 }).should("not.exist");
          });
          cy.findByTestId("screeningResults").should("exist");
          cy.findByText("Student Activity Log").should("exist");
        },
        { timeout: 25000 }
      );
    });
  });
});

function goToRosterFor(groupName) {
  clickElementByTextWithinSideNav(groupName);
  clickDashboardTab(groupName, "studentsTab");
}

function goToStudentPageFor(studentName) {
  cy.findAllByText(studentName)
    .first()
    .click();
}

function selectPrintOptions(optionLabel) {
  clickVisibleText("Print Options");
  waitForDetachedDom(500);
  clickVisibleText(optionLabel);
}

function makeWindowRelatedAssertions({ printOption, url, schoolYear = CURRENT_SCHOOL_YEAR }) {
  const [siteId, , studentGroupId, , studentId] = url.replace("http://localhost:3000/site/", "").split("/");
  const targetUrl = `http://localhost:3000/print/StudentDetail?siteId=${siteId}&studentGroupId=${studentGroupId}&studentId=${studentId}&schoolYear=${schoolYear}&orgid=${ORGID}&printOption=${printOption}`;
  cy.visit(targetUrl, printStubOpts);
  replaceExternalWindow(targetUrl);
}
