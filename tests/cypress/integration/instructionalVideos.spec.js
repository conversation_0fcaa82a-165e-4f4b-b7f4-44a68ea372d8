import {
  checkCurrentClasswideSkill,
  expandAllInterventions,
  expandInterventionForStudent,
  getBenchmarkPeriodNameByDate,
  verifyInstructionalVideoPopup
} from "../support/common/utils";
import { TEST_GROUPS } from "../support/common/constants";
import {
  inputAndCalculateClasswideScores,
  inputScreeningScores,
  saveClasswideScores,
  saveScreeningScores
} from "../support/enterScoreHelper";
import {
  clickDashboardTab,
  clickElementByTextWithinSideNav,
  goToScreeningForGivenPeriod
} from "../support/common/navigating";

const benchmarkPeriodName = getBenchmarkPeriodNameByDate();
const verifyInterventionsFunctionByBenchmarkPeriod = {
  Fall: verifyScheduledFollowUpInterventions,
  Winter: verifyScheduledFollowUpInterventions,
  Spring: verifyScheduledFollowUpInterventions
};
const numberOfScreeningFailingScoresEnteredByBenchmarkPeriod = {
  Fall: 6,
  Winter: 6,
  Spring: 8
};
const skillNameByByBenchmarkPeriod = {
  Fall: "Count Objects 1-20, Write Answer",
  Winter: "Sums to 6",
  Spring: "Sums to 12"
};
const firstDrillDownSkillName = skillNameByByBenchmarkPeriod[benchmarkPeriodName];
const verifyInterventions = verifyInterventionsFunctionByBenchmarkPeriod[benchmarkPeriodName];

/**
 * Groups used: grade1group3, grade1group4, grade8group2
 * Modifies: grade1group3 - Screening and Individual Intervention
 * Modifies: grade1group4 - Individual Intervention
 * Modifies: grade8group2 - Classwide Intervention
 * Can't be rerun without test database restart
 * Requires Student group(grade1group4) with 2 individual interventions from screening
 * Requires Student group(grade8group2) with Classwide Intervention without scores
 */
describe("InstructionalVideos", () => {
  const individualInterventionVideoType = "individual-intervention";
  const classwideInterventionVideoType = "classwide-intervention";
  const screeningVideoType = "screening";
  describe("Teacher schedules Screening and Individual Intervention", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher", queryString: "?siteId=test_elementary_site_id" });
    });
    describe("in the screening page", () => {
      it("and sees the instructional video modal when scheduling first screening", () => {
        clickElementByTextWithinSideNav(TEST_GROUPS.grade1group3.name);
        cy.findByTestId("beginScreening").click();
        verifyInstructionalVideoPopup(screeningVideoType);
        cy.findByTestId("input-screening-score-page").should("be.visible");
        inputScreeningScores(true, numberOfScreeningFailingScoresEnteredByBenchmarkPeriod[benchmarkPeriodName], false);
        saveScreeningScores();
      });
      it("and sees the instructional video if there are no open individual interventions", () => {
        goToScreeningForGivenPeriod(TEST_GROUPS.grade1group3.name, benchmarkPeriodName);
        cy.findByTestId("individualInterventionTab").should("not.exist");
        beginInterventionForStudent("Mary, Jane");
        verifyInstructionalVideoPopup(individualInterventionVideoType);
        cy.findByText("Individual interventions scheduled successfully").should("be.visible");
        cy.findByTestId("individualInterventionTab").should("have.class", "active");
        cy.findByText(firstDrillDownSkillName).should("be.visible");
        expandInterventionForStudent("Jane Mary");
        verifyInterventions(1);
      });
      it("and does not see the instructional video if there are open individual interventions", () => {
        goToScreeningForGivenPeriod(TEST_GROUPS.grade1group3.name, benchmarkPeriodName);
        cy.findByTestId("individualInterventionTab").should("be.visible");
        beginInterventionForStudent("Tom, John");
        cy.findByText("Individual interventions scheduled successfully").should("be.visible");
        cy.findByTestId("individualInterventionTab").should("have.class", "active");
        expandAllInterventions();
        verifyInterventions(2);
      });
    });
    describe("in the students page", () => {
      beforeEach(() => {
        clickElementByTextWithinSideNav(TEST_GROUPS.grade1group4.name);
      });
      it("and sees the instructional video if there are no open individual interventions", () => {
        cy.findByTestId("studentsTab").should("be.visible");
        cy.findByTestId("individualInterventionTab").should("not.exist");
        clickDashboardTab(TEST_GROUPS.grade1group4.name, "studentsTab");
        scheduleFirstAvailableInterventionInStudentsList();
        verifyInstructionalVideoPopup(individualInterventionVideoType);
        cy.findByText("Individual interventions scheduled successfully").should("be.visible");
        clickDashboardTab(TEST_GROUPS.grade1group4.name, "individualInterventionTab");
        cy.findByText("Sums to 6").should("be.visible");
        expandInterventionForStudent("John Smith");
        verifyInterventionsFunctionByBenchmarkPeriod[benchmarkPeriodName](1);
      });
      it("and does not see instructional video if there are open individual interventions", () => {
        cy.findByTestId("individualInterventionTab").should("be.visible");
        clickDashboardTab(TEST_GROUPS.grade1group4.name, "studentsTab");
        scheduleFirstAvailableInterventionInStudentsList();
        cy.findByText("Individual interventions scheduled successfully").should("be.visible");
        clickDashboardTab(TEST_GROUPS.grade1group4.name, "individualInterventionTab");
        expandAllInterventions();
        verifyInterventionsFunctionByBenchmarkPeriod[benchmarkPeriodName](2);
      });
    });
  });
  describe("Teacher goes to current classwide intervention", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher", queryString: "?siteId=test_elementary_site_id" });
    });
    it("should see the instructional video if there are no classwide intervention scores", () => {
      goToScreeningForGivenPeriod(TEST_GROUPS.grade8group2.name, "Winter");
      cy.findByText("To the Current Intervention", { exact: false }).click();
      verifyInstructionalVideoPopup(classwideInterventionVideoType);
      cy.findByText("Your class is currently in classwide intervention.", { exact: false }).should("be.visible");
      inputAndCalculateClasswideScores({ isPassing: true, submit: true });
      saveClasswideScores();
      checkCurrentClasswideSkill("2 Digit Subtraction With and Without Regrouping");
    });
    it("should not see the instructional video if there are classwide intervention scores", () => {
      goToScreeningForGivenPeriod(TEST_GROUPS.grade8group2.name, "Winter");
      cy.findByText("To the Current Intervention", { exact: false }).click();
      cy.findByText("Your class is currently in classwide intervention.", { exact: false }).should("be.visible");
    });
  });
});

function beginInterventionForStudent(studentName) {
  cy.findByTestId(`interventionCard_${studentName}`).within(() => {
    cy.findAllByTestId("scheduleInterventionCheckbox")
      .first()
      .click();
  });
  cy.findByText("Begin Individual Intervention", { exact: false }).click();
}

function scheduleFirstAvailableInterventionInStudentsList() {
  cy.findAllByTestId("scheduleInterventionCheckbox")
    .first()
    .click();
  cy.findByText("Begin Individual Intervention").click();
}

// function verifyScheduledInterventions(numberOfInterventionsToVerify) {
//   cy.findAllByTestId("individual-intervention-skill").should("have.length", numberOfInterventionsToVerify);
// }

function verifyScheduledFollowUpInterventions(numberOfInterventionsToVerify) {
  cy.findAllByTestId("individual-intervention-followup-skill").should("have.length", numberOfInterventionsToVerify);
}
