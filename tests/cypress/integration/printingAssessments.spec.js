import {
  checkElementByTextWithinSideNav,
  clickDashboardTab,
  clickElementByTextWithinSideNav,
  goToGroupClasswideIntervention
} from "../support/common/navigating";
import {
  checkCurrentClasswideSkill,
  clickVisibleTestId,
  clickVisibleText,
  expandInterventionForStudent,
  logout
} from "../support/common/utils";
import { CONTEXT_MENU_SITE_IDS, SCHOOL_NAMES, TEST_GROUPS } from "../support/common/constants";

/* eslint-disable cypress/unsafe-to-chain-command */

/**
 * Groups used: grade4group1, grade6group1, grade5group1, grade9group1, grade9group2
 * Modifies: -
 * Can be rerun without test database restart
 * Requires Student group with Classwide Intervention with max n-4/n skills completed
 * Requires Student in drill down
 * Requires Student in intervention with Timed Trial
 * Requires Student group without screening started
 */
describe("Print Monitor Assessments:", () => {
  const downloadsFolder = Cypress.config("downloadsFolder") || "cypress/downloads";

  const verifyFile = ({ fileName, partialMatch = false }) => {
    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(2000);
    cy.task("doesFileExist", { folderPath: downloadsFolder, fileName, partialMatch }).then(exists => {
      if (exists) {
        cy.log(`File ${fileName} exists in the downloads folder`);
      } else {
        throw new Error(`File ${fileName} does not exist in the downloads folder`);
      }
    });
  };

  describe("Elementary School teacher", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
    });
    afterEach(() => {
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(200);
      cy.task("deleteFilesInFolder", { folderPath: downloadsFolder }).then(deletedFiles => {
        deletedFiles.forEach(file => cy.log(`Deleted: ${file}`));
      });
    });
    it("should be able to print Classwide Assessment Intervention Packet", () => {
      const fileName = "springmath-assessmenthCWWDaZE2fusxuSJ9.pdf";
      goToGroupClasswideIntervention(TEST_GROUPS.grade4group1.name);
      clickVisibleText("Create Intervention Materials");
      assertCreateInterventionMaterialsDropdown();
      cy.findAllByText("Generate All Materials")
        .first()
        .click({ force: true });
      verifyFile({ fileName });
    });

    it("should be able to print another intervention materials after changing a skill", () => {
      const fileName = "springmath-assessmenthCWWDaZE2fusxuSJ9.pdf";
      goToGroupClasswideIntervention(TEST_GROUPS.grade4group1.name);
      clickVisibleText("Create Intervention Materials");
      cy.findAllByText("Generate All Materials")
        .first()
        .click({ force: true });
      verifyFile({ fileName });
      cy.findByTestId("skill-item-2").click();
      cy.findByText("Create Intervention Materials").should("be.visible");
    });

    it("should be able to print Individual Drilldown Assessment", () => {
      const firstFileName = "springmath-assessmentJ8aGfDwkaHFN2cCAM.pdf";
      const lastFileName = "springmath-assessmentNwgrGGJ7iD6PjeXfE.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade4group1.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade4group1.name}`).should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade4group1.name, "individualInterventionTab");
      cy.findAllByText("2-Digit Multiply by 2-Digit with Regrouping").should("be.visible");
      expandInterventionForStudent("Samuel Gosling");
      cy.findByText("is currently completing follow ups to their screening assessments.").should("be.visible");
      assertGenerateAssessments(firstFileName, lastFileName);
    });

    it("should be able to print Individual Skill for single student", () => {
      const firstFileName = "springmath-assessmentkmS5FqgMj76hFyDf6.pdf";
      const lastFileName = "springmath-assessmentkmS5FqgMj76hFyDf6.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade6group1.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade6group1.name}`).should("be.visible");
      cy.findByText("Getting started with", { exact: false }).should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade6group1.name, "individualInterventionTab");
      cy.findByText("Mixed Multiplication & Division 0-12").should("be.visible");
      expandInterventionForStudent("Harold Brooks");
      cy.findByText("is currently practicing the skill").should("be.visible");
      assertGenerateInterventionDocuments(firstFileName, lastFileName);
    });

    it("should be able to print Progress Monitoring Assessments", () => {
      const fileName = "springmath-assessments-Mixed Operations.zip";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade7group2.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade7group2.name}`).should("be.visible");
      cy.findByText("Getting started with", { exact: false }).should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade7group2.name, "individualInterventionTab");
      cy.findByText("Mixed Operations").should("be.visible");
      assertProgressMonitoringAssessments(fileName);
    });

    it("should be able to print Benchmark Assessment", () => {
      const fileName = "springmath-screening-";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade5group1.name);
      cy.findAllByText(/(Begin|Continue) (.*\d{4}-\d{2} )?[Ss]creening/, { exact: false })
        .first()
        .click();
      cy.findByText("Print Your Assessments")
        .should("be.visible")
        .click();
      verifyFile({ fileName, partialMatch: true });
    });
  });

  describe("Upcoming materials printing", () => {
    describe("Elementary School teacher", () => {
      beforeEach(() => {
        cy.loginAs({ role: "teacher" });
      });
      afterEach(() => {
        // eslint-disable-next-line cypress/no-unnecessary-waiting
        cy.wait(200);
        cy.task("deleteFilesInFolder", { folderPath: downloadsFolder }).then(deletedFiles => {
          deletedFiles.forEach(file => cy.log(`Deleted: ${file}`));
        });
      });
      it("buttons for all printing variations should be correctly assigned", () => {
        goToGroupClasswideIntervention(TEST_GROUPS.grade4group1.name);

        verifyFutureMaterials(72, "all");
        verifyFile({ fileName: "springmath-assessmentqghrmCYKiRTCRQFbY.pdf" });
        verifyFutureMaterials(28, "teacher");
        verifyFile({ fileName: "springmath-assessmentaqikjxjsFL3bwTfSm.pdf" });
        verifyFutureMaterials(27, "student");
        verifyFile({ fileName: "springmath-assessmentLLYFwF3M7hKwy735N.pdf" });
        verifyFutureMaterials(76, "all");
        verifyFile({ fileName: "springmath-assessmentPkqqMLFL5HKaWHhSA.pdf" });
      });
    });
  });

  describe("High School teacher", () => {
    before(() => {
      cy.loginAs({ role: "teacher" });
      cy.findByTestId("siteSelectorId")
        .should("be.visible")
        .contains("Test Elementary Site");
      cy.findByTestId("userContextMenu").click();
      cy.findByTestId("site_test_high_school_site_id").click();
      logout();
    });
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
    });
    afterEach(() => {
      // eslint-disable-next-line cypress/no-unnecessary-waiting
      cy.wait(200);
      cy.task("deleteFilesInFolder", { folderPath: downloadsFolder }).then(deletedFiles => {
        deletedFiles.forEach(file => cy.log(`Deleted: ${file}`));
      });
    });
    it("should be able to print Classwide Intervention Materials", () => {
      const fileName = "springmath-assessmentWkTxuThkN8GD5sTk5.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade9group1.name);
      checkCurrentClasswideSkill("Mixed Operations");
      cy.findAllByTestId("printAssessmentsButton")
        .first()
        .click();
      verifyFile({ fileName });
    });

    it("should be able to print Boost It materials", () => {
      const fileName = "springmath-assessmentWkTxuThkN8GD5sTk5.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade9group1.name);
      cy.findByText("Boost It").click();
      verifyFile({ fileName });
    });

    it("should be able to print Individual Drilldown Assessment", () => {
      const firstFileName = "springmath-assessmentWkTxuThkN8GD5sTk5.pdf";
      const lastFileName = "springmath-assessmentWkTxuThkN8GD5sTk5.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade9group1.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade9group1.name}`).should("be.visible");
      cy.get(".intervention-content").should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade9group1.name, "individualInterventionTab");
      cy.findByText("Divide 1-Digit into 2-3-Digit without Remainders").should("be.visible");
      expandInterventionForStudent("Rachel James");

      cy.findByText("is currently completing follow ups to their screening assessments.").should("be.visible");
      assertGenerateAssessments(firstFileName, lastFileName);
    });

    it("should be able to print Individual Skill for single student", () => {
      const firstFileName = "springmath-assessment5NiJE3CGhMYpXueaY.pdf";
      const lastFileName = "springmath-assessment5NiJE3CGhMYpXueaY.pdf";
      clickElementByTextWithinSideNav(TEST_GROUPS.grade9group2.name);
      cy.findByTestId(`student-group_${TEST_GROUPS.grade9group2.name}`).should("be.visible");
      cy.get(".intervention-content").should("be.visible");
      clickDashboardTab(TEST_GROUPS.grade9group2.name, "individualInterventionTab");
      cy.findByText("Divide 1-Digit into 2-3-Digit without Remainders").should("be.visible");
      expandInterventionForStudent("Nell Drake");
      cy.findByText("is currently practicing the skill").should("be.visible");
      assertGenerateInterventionDocuments(firstFileName, lastFileName);
      cy.findByText("Generate Progress Monitoring Assessments", { timeout: 1000 }).should("not.exist");
    });

    it("should be able to switch to the default site", () => {
      checkElementByTextWithinSideNav(SCHOOL_NAMES.highSchool);
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_SITE_IDS.elementary);
    });
  });

  function assertCreateInterventionMaterialsDropdown() {
    cy.findAllByTestId("create-intervention-materials-options-CW")
      .eq(0)
      .contains("Generate All Materials");
    cy.findAllByTestId("create-intervention-materials-options-CW")
      .eq(1)
      .contains("Generate Teacher Materials");
    cy.findAllByTestId("create-intervention-materials-options-CW")
      .eq(2)
      .contains("Generate Student Materials and Answer Key");
  }

  function assertGenerateAssessments(firstFileName, lastFileName) {
    cy.findAllByText("Generate Assessment")
      .first()
      .click();
    verifyFile({ fileName: firstFileName });

    cy.findAllByText("Generate Assessment")
      .last()
      .click();
    verifyFile({ fileName: lastFileName });
  }

  function assertProgressMonitoringAssessments(fileName) {
    cy.findAllByText("Generate Progress Monitoring Assessments")
      .first()
      .click();
    verifyFile({ fileName });
  }

  function assertGenerateInterventionDocuments(firstFileName, lastFileName) {
    cy.findAllByText("Select Activity")
      .first()
      .click();
    cy.findAllByText("Timed Trial")
      .first()
      .click();
    verifyFile({ fileName: firstFileName });

    cy.findAllByText("Select Activity")
      .last()
      .click();
    cy.findByText("Timed Trial").click();
    verifyFile({ fileName: lastFileName });
  }
});

function verifyFutureMaterials(measureNumber, type) {
  const textByType = {
    all: "Generate All Materials",
    teacher: "Generate Teacher Materials",
    student: "Generate Student Materials and Answer Key"
  };

  cy.findByText("View intervention materials for future skills")
    .scrollIntoView()
    .should("be.visible")
    .click();

  cy.findByTestId("upcomingAssessmentPrintingContainer")
    .find("button")
    .should("have.length", 4);

  cy.findByTestId("upcomingAssessmentPrintingContainer").within(() => {
    cy.findByTestId(`CW-measure-${measureNumber}`)
      .scrollIntoView()
      .should("be.visible")
      .click();
    cy.findAllByText(textByType[type])
      .first()
      .click();
  });
}
