import replaceExternalWindow from "../support/common/printWindowUtil";
import {
  assertSchoolOverviewHeader,
  clickVisibleTestId,
  clickVisibleText,
  getBenchmarkPeriodNameByDate,
  logout
} from "../support/common/utils";
import { CONTEXT_MENU_SITE_IDS, DOMAIN_URL } from "../support/common/constants";

const benchmarkPeriodName = getBenchmarkPeriodNameByDate();

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");

/**
 * Groups used: -
 * Modifies: -
 * Can be rerun without test database restart
 * Requires -
 */
describe("School overview printing:", () => {
  describe("Admin can print elementary school overview page:", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    it("and sees the print page preview with all elements", () => {
      clickVisibleText("Print This Page");
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          assertElementsOnPrintout(true);
        },
        { timeout: 20000 }
      );
    });
    it("and sees the print page preview without screening element", () => {
      const shouldDisplayScreening = false;
      clickVisibleTestId("screeningProgressHideButton");
      clickVisibleText("Print This Page");
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url, shouldDisplayScreening });
          assertElementsOnPrintout(false);
        },
        { timeout: 20000 }
      );
    });
    it("and sees the print page preview without progress summary element", () => {
      clickVisibleText("Print This Page");
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          assertElementsOnPrintout(true);
        },
        { timeout: 20000 }
      );
    });
    it("and sees the print page preview without screening and progress summary element", () => {
      const shouldDisplayScreening = false;
      cy.findByTestId("screeningProgressHideButton")
        .should("be.visible")
        .click();
      cy.findByText("Print This Page").click();
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url, shouldDisplayScreening });
          assertElementsOnPrintout(false);
        },
        { timeout: 20000 }
      );
    });
  });
  describe("Admin can print high school overview page:", () => {
    beforeEach(() => {
      cy.loginAs({ role: "coach" });
    });
    before(() => {
      cy.loginAs({ role: "coach" });
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_SITE_IDS.highSchool);
      logout();
    });
    after(() => {
      cy.visit(`${DOMAIN_URL}/`);
      assertSchoolOverviewHeader();
      clickVisibleTestId("userContextMenu");
      clickVisibleTestId(CONTEXT_MENU_SITE_IDS.elementary);
    });
    it("and sees the print page preview with all elements", () => {
      clickVisibleText("Print This Page");
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText(`${benchmarkPeriodName} screening is underway!`).should("not.exist");
          cy.findByTestId("classwideInterventionSectionHeader")
            .should("be.visible")
            .contains("Classwide Interventions");
        },
        { timeout: 20000 }
      );
    });
    it("and sees the print page preview without progress summary element", () => {
      cy.findByText("Print This Page")
        .should("be.visible")
        .click();
      assertWindowOpenCall();
      cy.url().then(
        url => {
          makeWindowRelatedAssertions({ url });
          cy.findByText(`${benchmarkPeriodName} screening is underway!`).should("not.exist");
          cy.findByText("Progress Summary").should("not.exist");
          cy.findByTestId("classwideInterventionSectionHeader").should("be.visible");
        },
        { timeout: 20000 }
      );
    });
  });
});

function assertElementsOnPrintout(isScreeningNoticeVisible) {
  cy.findByText(`${benchmarkPeriodName} screening is underway!`, { exact: false }).should(
    isScreeningNoticeVisible ? "be.visible" : "not.exist"
  );
  cy.findByText("Progress Summary").should("not.exist");
  cy.findByTestId("classwideInterventionSectionHeader").should("be.visible");
  cy.findByText("Screening Results (Percent of Students Meeting Target)")
    .scrollIntoView()
    .should("be.visible");
}

function assertWindowOpenCall() {
  cy.window()
    .its("open")
    .should("be.called");
}

const printStubOpts = {
  onBeforeLoad: win => {
    cy.stub(win, "print");
  }
};

function makeWindowRelatedAssertions({ url, shouldDisplayScreening = true }) {
  const [, orgId, gradeId, siteId] = url.replace("http://localhost:3000/", "").split("/");
  const targetUrl = `http://localhost:3000/print/SchoolOverview?orgid=${orgId}&gradeId=${gradeId}&siteId=${siteId}&screeningHidden=${!shouldDisplayScreening}&schoolYear=${CURRENT_SCHOOL_YEAR}`;
  cy.visit(targetUrl, printStubOpts);
  replaceExternalWindow(targetUrl);
}
