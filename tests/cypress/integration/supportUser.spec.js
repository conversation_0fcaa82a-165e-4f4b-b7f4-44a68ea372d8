import { generateNewUserEmail } from "../support/common/emailUtils";
import { DOMAIN_URL, ORGID } from "../support/common/constants";
import { clickVisibleTestId, clickVisibleText } from "../support/common/utils";

const unAuthorizedOrgId = ORGID;

/**
 * Groups used: -
 * Modifies: User roster - adds support user
 * Can't be rerun without test database restart
 * Requires super admin account
 */
describe("Support User:", () => {
  const activeOrganizationName = "Other Organization";
  const supportUserEmail = generateNewUserEmail("supportUser");
  it("can be added by a super admin ", () => {
    cy.loginAs({ role: "superAdmin" });
    cy.get("#menu-nav-dropdown")
      .should("be.visible")
      .click();
    clickVisibleTestId("topNav_manage-users");
    clickVisibleTestId("addSupportUserButton");
    cy.get("#txtSupportEmail").type(supportUserEmail);
    cy.get("#txtSupportFirstName").type("SupportNew");
    cy.get("#txtSupportLastName").type("UserNew");
    cy.get("#selectActiveOrganizations input:first").type("Other", { force: true });
    clickVisibleText(activeOrganizationName);
    clickVisibleText("Send Invite Email");
    cy.findByText("Invite Sent. Please contact the new user to inform them the invite has been sent.").should(
      "be.visible"
    );
  });

  it("can view organizations in read-only mode", () => {
    cy.loginAs({ email: supportUserEmail });
    cy.findByText("Client List").should("be.visible");

    clickVisibleText(activeOrganizationName);
    clickVisibleText("View As Coach");
    cy.findByTestId("siteSelectorId")
      .should("have.text", "Sunny Slope Elementary")
      .should("be.visible");
    clickVisibleText("Kindergarten");
    cy.findByText("MATH 1", { exact: false }).click({ force: true });
    cy.findByText("Begin Screening")
      .should("be.visible")
      .and("be.disabled");
  });

  it("can not view organizations they were not given access to ", () => {
    const unAuthorizedEndpoint = `${DOMAIN_URL}/school-overview/${unAuthorizedOrgId}/all/`;
    cy.loginAs({ email: supportUserEmail });
    cy.findByText(activeOrganizationName).should("be.visible");
    cy.visit(unAuthorizedEndpoint);
    cy.findByText(
      "You do not currently have an active account in SpringMath. Please contact your school data admin in order to activate your account."
    ).should("be.visible");
  });
});
