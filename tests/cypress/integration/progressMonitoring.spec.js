import { clickVisibleTestId, clickVisibleText } from "../support/common/utils";

/**
 * Groups used: -
 * Modifies: Progress Monitoring tree for grade K
 * Can't be rerun without test database restart
 * Requires -
 */
describe("Progress Monitoring", () => {
  beforeEach(() => {
    cy.loginAs({ role: "superAdmin" });
    clickVisibleText("Menu");
    clickVisibleTestId("topNav_progress-monitoring");
  });
  it("Super Admin should be able to see Progress Monitoring tree", () => {
    displayProgressMonitoringTree("K", "Fall", "Count Objects 1-10, Circle Answer");
    cy.get("[class=node]").should("have.length", 10);
    selectTreeNode(8, "AM 150- Count Objects");

    cy.get(".select__single-value")
      .first()
      .should("have.text", "AM 150 - Count Objects Aloud 1-20");
    cy.get('[data-update-string="default.Fall"]').should("have.length", 2);
    cy.get('[data-update-string="default.Winter"]').should("have.length", 2);
    cy.get('[data-update-string="default.Spring"]').should("have.length", 2);
    cy.findByText("Add Custom Benchmark Targets")
      .scrollIntoView()
      .should("be.visible");
    cy.findByText("Add Custom Individual Targets")
      .scrollIntoView()
      .should("be.visible");
    cy.findByText("Add Custom Classwide Targets", { timeout: 1000 }).should("not.exist");

    displayProgressMonitoringTree("HS", "All", "Solve for Slope and Intercept Using Linear Function y=mx+b");
    cy.get("[class=node]").should("have.length", 67);
    selectTreeNode(65, "AM 155- Solve For Slop");
    cy.get('[data-update-string="default.All"]').should("have.length", 2);
  });
  it("Super Admin should be able to add custom benchmark target", () => {
    displayProgressMonitoringTree("K", "Fall", "Count Objects 1-10, Circle Answer");
    selectTreeNode(8, "AM 150- Count Objects");
    cy.findByText("Add Custom Benchmark Targets").click();
    cy.findByText("Benchmark").should("be.visible");
    cy.findByText("Add Custom Benchmark Targets", { timeout: 1000 }).should("not.exist");
    cy.get('[data-update-string="default.Fall"]').should("have.length", 2);
    cy.get('[data-update-string="benchmark.Fall"]').should("have.length", 2);
  });
});

function displayProgressMonitoringTree(grade, period, screeningAssignment) {
  cy.get("[data-testid=grade-selector]")
    .scrollIntoView()
    .should("be.visible")
    .select(grade);
  cy.findByTestId("benchmarkPeriod-selector").select(period);
  cy.findByTestId("screeningAssignment-selector")
    .should("be.visible")
    .select(screeningAssignment);
}

function selectTreeNode(nodeIndex, nodeLabel) {
  cy.get("[class=node]")
    .eq(nodeIndex)
    .contains(nodeLabel)
    .click();
}
