import { INTERVENTION_SKILLS_BY_GRADE_BY_INDEX, ORGID, TEST_GROUPS } from "../support/common/constants";
import replaceExternalWindow from "../support/common/printWindowUtil";
import { checkCurrentClasswideSkill, clickVisibleText } from "../support/common/utils";
import { goToGroupClasswideIntervention } from "../support/common/navigating";

const printStubOpts = {
  onBeforeLoad: win => {
    cy.stub(win, "print");
  }
};

const currentSkillAssessmentResultId = "SWMGrcA4mzmygKX7T";
const latestSkillStudentPrintAssessmentId = "hCWWDaZE2fusxuSJ9"; // Multiplication 0-12
const completedSkillStudentPrintAssessmentId = "rXseaSQtu7bakKQXP"; // Addition 3-Digit Numbers with & without Regrouping

const CURRENT_SCHOOL_YEAR = Cypress.env("CURRENT_SCHOOL_YEAR");
const allClassWideInterventionSkillsOptionLabel = "Classwide Intervention Skills";

/**
 * Groups used: grade4group1
 * Modifies: -
 * Can be rerun without test database restart
 * Requires group with failed screening and classwide intervention at "Multiplication 0-12" skill
 * and student with individual intervention
 * and updated currentSkillAssessmentResultId, latestSkillStudentPrintAssessmentId, completedSkillStudentPrintAssessmentId
 */
describe("Classwide intervention printing:", () => {
  const classwideSkillNameByInterventionProgressIndex = INTERVENTION_SKILLS_BY_GRADE_BY_INDEX["04"];
  describe("Teacher prints", () => {
    beforeEach(() => {
      cy.loginAs({ role: "teacher" });
      goToGroupClasswideIntervention(TEST_GROUPS.grade4group1.name);
      cy.get('[data-testid="loading-icon"]', { timeout: 20000 }).should("not.exist");
    });
    it("the classwide intervention page and the printing page should open", () => {
      const printOptions = [];
      clickVisibleText("Print This Page");

      cy.window()
        .its("open")
        .should("be.called");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({
            printOptions,
            url,
            selectedSkillAssessmentId: latestSkillStudentPrintAssessmentId
          });
          cy.findByText(`Classwide Intervention for ${TEST_GROUPS.grade4group1.name}`).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[4]).should("exist");
        },
        { timeout: 25000 }
      );
    });
    it("the classwide intervention page with full history of graphs for classwide intervention skills", () => {
      const printOptions = [allClassWideInterventionSkillsOptionLabel];
      selectAllPrintOptions(printOptions);
      clickVisibleText("Print This Page");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({
            printOptions,
            url,
            selectedSkillAssessmentId: latestSkillStudentPrintAssessmentId
          });
          cy.findByText(`Classwide Intervention for ${TEST_GROUPS.grade4group1.name}`).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[0]).should("exist");
          // NOTE(fmazur) - Uncomment when Classwide Intervention is recreated using new Class Rules
          // cy.findByText(classwideSkillNameByInterventionProgressIndex[1]).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[2]).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[3]).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[4]).should("exist");
          cy.findByText(classwideSkillNameByInterventionProgressIndex[5]).should("not.exist");
        },
        { timeout: 25000 }
      );
    });
    it("the classwide intervention graphs for students with only graphs for classwide intervention skill", () => {
      const printOptions = [];
      clickVisibleText("Print Student Graphs");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({
            printOptions,
            url,
            printStudentGraphs: true,
            selectedSkillAssessmentId: latestSkillStudentPrintAssessmentId
          });
          cy.findAllByText("Classwide Intervention Progress").should("exist");
          cy.findAllByText(classwideSkillNameByInterventionProgressIndex[4]).should("exist");
          cy.findAllByText("Skill Tree Progress").should("not.exist");
          cy.findAllByText("Individual Intervention Progress").should("not.exist");
          cy.findAllByText("Screening Results").should("not.be.visible");
          cy.findAllByText("Student Activity Log").should("not.exist");
          cy.get(".printContainer").should("have.length", 7); // Number of students + 1
        },
        { timeout: 25000 }
      );
    });
    it("the classwide intervention graphs for students with graphs for classwide intervention previously completed skill", () => {
      const printOptions = [];
      clickVisibleText(classwideSkillNameByInterventionProgressIndex[2]);
      checkCurrentClasswideSkill(classwideSkillNameByInterventionProgressIndex[2]);
      clickVisibleText("Print Student Graphs");

      cy.url().then(
        url => {
          makeWindowRelatedAssertions({
            printOptions,
            url,
            printStudentGraphs: true,
            selectedSkillAssessmentId: completedSkillStudentPrintAssessmentId
          });
          cy.findAllByText("Classwide Intervention Progress").should("exist");
          cy.findAllByText(classwideSkillNameByInterventionProgressIndex[2]).should("exist");
          cy.get(".printContainer").should("have.length", 7); // Number of students + 1
        },
        { timeout: 25000 }
      );
    });
  });
});

function selectAllPrintOptions(optionLabels) {
  clickVisibleText("Print Options");
  optionLabels.forEach(label => {
    clickVisibleText(label);
  });
}

function makeWindowRelatedAssertions({ printOptions, url, printStudentGraphs, selectedSkillAssessmentId }) {
  const [siteId, , studentGroupId] = url.replace("http://localhost:3000/site/", "").split("/");
  const printAllClasswideInterventions = printOptions.includes(allClassWideInterventionSkillsOptionLabel);
  let targetUrl;
  if (!printStudentGraphs) {
    targetUrl = `http://localhost:3000/print/ClasswideIntervention?siteId=${siteId}&studentGroupId=${studentGroupId}&schoolYear=${CURRENT_SCHOOL_YEAR}&assessmentResultId=${currentSkillAssessmentResultId}${CURRENT_SCHOOL_YEAR}&orgid=${ORGID}&selectedSkillIndex=undefined&selectedSkillAssessmentId=${selectedSkillAssessmentId}&printAllClasswideInterventionSkillsGraphs=${printAllClasswideInterventions}`;
  } else {
    targetUrl = `http://localhost:3000/print/AllStudentsClasswideInterventionGraphs?siteId=${siteId}&studentGroupId=${studentGroupId}&grade=${TEST_GROUPS.grade4group1.grade}&schoolYear=${CURRENT_SCHOOL_YEAR}&orgid=${ORGID}&selectedSkillAssessmentId=${selectedSkillAssessmentId}&printOnlyClasswideInterventionSkillGraphs=true`;
  }
  cy.visit(targetUrl, printStubOpts);
  replaceExternalWindow(targetUrl);
}
